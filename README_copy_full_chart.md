# Copy Full Chart Images Script

This script (`copy_full_chart_images.py`) helps you copy images with 'full_chart' in their filename from the finger tapping directories to a centralized location.

## Overview

The script searches for images in the following directory structure:
```
users/[user_id]/finger_tapping/images/*full_chart*
```

Based on the codebase analysis, these images are typically named like:
- `{user_id}_{date}_distance_full_chart_{hand}.png`

## Usage

### Method 1: Using Default Configuration (Recommended)

If you have the project's `config.py` file available, you can run the script without arguments:

```bash
python copy_full_chart_images.py
```

This will:
- Use `BASE_RESULTS_DIRECTORY` from config.py as the source
- Copy images to `./copied_full_chart_images/`
- Preserve the user_id directory structure

### Method 2: Command Line Arguments

```bash
python copy_full_chart_images.py <source_base_dir> <destination_dir> [options]
```

#### Arguments:
- `source_base_dir`: Base directory containing the users/[user_id]/finger_tapping/images structure
- `destination_dir`: Where to copy the images

#### Options:
- `--pattern`: File pattern to search for (default: `*full_chart*`)
- `--preserve-structure`: Keep user_id subdirectories in destination
- `--dry-run`: Show what would be copied without actually copying

#### Examples:

```bash
# Basic copy with flattened structure
python copy_full_chart_images.py "\\files.ubc.ca\team\PPRC\Camera\Booth_Results\reports" "./full_chart_backup"

# Preserve user directory structure
python copy_full_chart_images.py "\\files.ubc.ca\team\PPRC\Camera\Booth_Results\reports" "./full_chart_backup" --preserve-structure

# Dry run to see what would be copied
python copy_full_chart_images.py "\\files.ubc.ca\team\PPRC\Camera\Booth_Results\reports" "./full_chart_backup" --dry-run

# Custom pattern
python copy_full_chart_images.py "\\files.ubc.ca\team\PPRC\Camera\Booth_Results\reports" "./charts" --pattern "*distance_full_chart*"
```

## Output Structure

### With `--preserve-structure` (default for auto mode):
```
destination_dir/
├── user_id_1/
│   ├── user_id_1_date1_distance_full_chart_left.png
│   └── user_id_1_date1_distance_full_chart_right.png
├── user_id_2/
│   ├── user_id_2_date1_distance_full_chart_left.png
│   └── user_id_2_date1_distance_full_chart_right.png
└── ...
```

### Without `--preserve-structure`:
```
destination_dir/
├── user_id_1_date1_distance_full_chart_left.png
├── user_id_1_date1_distance_full_chart_right.png
├── user_id_2_date1_distance_full_chart_left.png
├── user_id_2_date1_distance_full_chart_right.png
└── ...
```

## Features

- **Automatic file conflict resolution**: If a file with the same name exists, adds a numeric suffix
- **Progress reporting**: Shows which files are copied and any failures
- **Flexible pattern matching**: Can search for different filename patterns
- **Dry run mode**: Preview what will be copied before actually doing it
- **Error handling**: Continues copying even if some files fail

## Requirements

- Python 3.6+
- Standard library modules only (no additional dependencies)

## Integration with Project

The script can be imported as a module:

```python
from copy_full_chart_images import find_full_chart_images, copy_images

# Find images
images = find_full_chart_images("/path/to/base/directory")

# Copy them
copy_images(images, "/destination/path", preserve_structure=True)
```
