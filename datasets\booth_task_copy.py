import os
import sys
# Adjust the working directory to the project root
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.join(current_dir, '..')
sys.path.insert(0, project_root)
os.chdir(project_root)
import shutil
import subprocess
from  config import BASE_RAW_DIRECTORY, BASE_PROCESSED_DIRECTORY

def copy_videos(src_directory, dest_directory, task, emotion,parent_task):
    # Iterate over each user's folder in the source directory
    for user_id in os.listdir(src_directory):
        user_path = os.path.join(src_directory, user_id)
        # Check if it's a directory (to handle user folders)
        if os.path.isdir(user_path):
            
            # Iterate over each timestamp folder in the user's folder
            for timestamp in os.listdir(user_path):
                timestamp_path = os.path.join(user_path, timestamp)
                # Check if it's a directory (to handle timestamp folders)
                if os.path.isdir(timestamp_path):
                    
                    face_path = os.path.join(timestamp_path, parent_task)
                    if emotion or parent_task=='eye_movement':
                        face_path = os.path.join(face_path, f'{task}')
                    # Check if the 'finger_tapping' folder exists
                    if os.path.isdir(face_path):
                        # Copy all videos in the 'finger_tapping' folder
                        for video in os.listdir(face_path):
                            video_path = os.path.join(face_path, video)
                            new_name = f"{user_id}_{timestamp}_{video}"
                            # Check if it's a file (to handle only video files)
                            print(video)
                            if os.path.isfile(video_path) and ((emotion is None and task in video) or f'{emotion}' in video): 
                                dest_path = os.path.join(dest_directory, new_name)
                                if not os.path.exists(dest_path):
                                    shutil.copy(video_path, dest_path)
                                    print(f"Copying {video_path} to {dest_path}")

if __name__ == '__main__':
    # # Connect to shared drive and copy videos
    base_directory = f'{BASE_RAW_DIRECTORY}' 
    parent_task='finger_tapping'      
    tasks = ['left','right']  
    for task in tasks:
        dest_directory = f'{BASE_PROCESSED_DIRECTORY}\\{parent_task}\\{task}\\videos'
        #make sure the destination directory exists
        if not os.path.exists(dest_directory):
            os.makedirs(dest_directory)
        copy_videos(base_directory, dest_directory,task,None,parent_task)      
        

    emotions = ['disgust','angry', 'happy', 'sad']
    tasks = ['text','imitation']
    parent_task='facial_expression'
    for task in tasks:
        for emotion in emotions:
            dest_directory = f'{BASE_PROCESSED_DIRECTORY}\\{parent_task}\\{emotion}\\{task}\\videos'
            #make sure the destination directory exists
            if not os.path.exists(dest_directory):
                os.makedirs(dest_directory)
            copy_videos(base_directory, dest_directory,task,emotion,parent_task)
    

    parent_task='hand_movement'      
    tasks = ['both_still','left_open_close','left_up_down','right_open_close','right_up_down']  
    for task in tasks:
        dest_directory = f'{BASE_PROCESSED_DIRECTORY}\\{parent_task}\\{task}\\videos'
        #make sure the destination directory exists
        if not os.path.exists(dest_directory):
            os.makedirs(dest_directory) 
        copy_videos(base_directory, dest_directory,task,None,parent_task)    
    
    parent_task='eye_movement'      
    tasks = ['calibration','okn']  
    for task in tasks:
        dest_directory = f'{BASE_PROCESSED_DIRECTORY}\\{parent_task}\\{task}\\videos'
        #make sure the destination directory exists
        if not os.path.exists(dest_directory):
            os.makedirs(dest_directory)
        copy_videos(base_directory, dest_directory,task,None,parent_task) 
        
    parent_task='facial_expression'
    emotion = 'basic_facial'
    dest_directory = f'{BASE_PROCESSED_DIRECTORY}\\{parent_task}\\{emotion}\\videos'
    #make sure the destination directory exists
    if not os.path.exists(dest_directory):
        os.makedirs(dest_directory)
    copy_videos(base_directory, dest_directory,emotion,'neutral',parent_task)