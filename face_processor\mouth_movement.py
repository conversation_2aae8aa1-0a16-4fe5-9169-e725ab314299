import pandas as pd
import numpy as np
import os
from scipy.spatial import ConvexHull, distance
# Function to load landmarks from a file
def load_landmarks(file_path):
    return pd.read_csv(file_path)

# Function to calculate distances between given pairs of landmarks
def calculate_distances(df, landmarks_pairs):
    distances = []
    for (landmark1, landmark2) in landmarks_pairs:
        point1 = df[[f'Landmark_{landmark1}_x', f'Landmark_{landmark1}_y']]
        point2 = df[[f'Landmark_{landmark2}_x', f'Landmark_{landmark2}_y']]
        dist = point1.sub(point2.values).apply(np.linalg.norm, axis=1)
        distances.append(dist)
    return distances

# Function to perform min-max scaling
def min_max_scale(column):
    return (column - column.min()) / (column.max() - column.min())

# Function to perform Z-score normalization
def z_score_normalize(column):
    return (column - column.mean()) / column.std()

# Then, integrate these functions into your processing workflow
def process_mouth_movements(landmarks_dir, save_doc, normalize='minmax'):
    results = []
    landmark_pairs = [(51, 33), (57, 33), (48, 54), (61, 64), (62, 66), (63, 65)]
    for file_name in os.listdir(landmarks_dir):
        landmarks_file = os.path.join(landmarks_dir, file_name)
        landmarks_df = load_landmarks(landmarks_file)
        distances = calculate_distances(landmarks_df, landmark_pairs)
        distances_df = pd.DataFrame(distances).transpose()  # Each row is a frame, each column a distance

        # Apply normalization
        if normalize == 'minmax':
            distances_df = distances_df.apply(min_max_scale)
        elif normalize == 'zscore':
            distances_df = distances_df.apply(z_score_normalize)

        # Calculate statistics
        means = distances_df.mean()
        stds = distances_df.std()
        amplitude = distances_df.max() - distances_df.min()
        

        id = file_name.split('_')[0]
        results.append({
            'ID': id,
            **{f'Mean_Distance_{i+1}': mean for i, mean in enumerate(means)},
            **{f'STD_Distance_{i+1}': std for i, std in enumerate(stds)},
            **{f'Amplitude_{i+1}': amp for i, amp in enumerate(amplitude)}
        })

        print(f"ID: {id}, Mean Distances: {means.tolist()}, STDs: {stds.tolist()}")

    # Save results to a new CSV file
    results_df = pd.DataFrame(results)
    results_df.to_csv(save_doc, index=False)
    print(f'Results have been saved to {save_doc}')
    



# Paths (example usage)

origin_folder = '\\\\files.ubc.ca\\team\\PPRC\\Camera\\Booth_Processed'
for task in ['left', 'right']:
    save_doc = f'{origin_folder}\\finger_tapping\\{task}\\csvs\\mouth_movements_{task}.csv'
    landmark_files = f'{origin_folder}\\finger_tapping\\{task}\\landmarks'
    process_mouth_movements(landmark_files, save_doc, normalize='minmax')  # or 'zscore'

save_doc = f'{origin_folder}\\facial_expression\\happy\\imitation\\csvs\\mouth_movements.csv'
landmark_files = f'{origin_folder}\\facial_expression\\happy\\imitation\\landmarks\\'
process_mouth_movements(landmark_files, save_doc, normalize='minmax')