import os
import cv2
import pandas as pd
import numpy as np

# Colors for two hands
COLOR_HAND_1 = (0, 255, 0)  # Green for the first hand
COLOR_HAND_2 = (0, 0, 255)  # Red for the second hand

# Input folder containing CSVs and output folder for videos
input_folder = r"\\files.ubc.ca\team\PPRC\Camera\Booth_Processed\finger_tapping\left\pose"  # Folder with CSV files
output_folder = r"\\files.ubc.ca\team\PPRC\Camera\Booth_Processed\finger_tapping\left\landmark_videos"  # Destination folder

# Ensure the output folder exists
os.makedirs(output_folder, exist_ok=True)

# Number of landmarks (21 landmarks per hand)
NUM_LANDMARKS = 21

# Hand connections based on MediaPipe's hand landmarks
HAND_CONNECTIONS = [
    (0, 1), (1, 2), (2, 3), (3, 4),  # Thumb
    (0, 5), (5, 6), (6, 7), (7, 8),  # Index finger
    (0, 9), (9, 10), (10, 11), (11, 12),  # Middle finger
    (0, 13), (13, 14), (14, 15), (15, 16),  # Ring finger
    (0, 17), (17, 18), (18, 19), (19, 20)  # Pinky finger
]

# Video settings
frame_width, frame_height = 1920, 1080  # Video resolution
fps = 60  # Frames per second

# Function to draw hand landmarks and connections on a blank image
def draw_hand_landmarks(blank_image, landmarks, color):
    points = []
    for i in range(NUM_LANDMARKS):
        x, y, z = landmarks[i], landmarks[i + NUM_LANDMARKS], landmarks[i + 2 * NUM_LANDMARKS]
        h, w, _ = blank_image.shape
        cx, cy = int(x * w), int(y * h)
        points.append((cx, cy))

        # Draw the landmark point
        cv2.circle(blank_image, (cx, cy), 5, color, -1)
        # Draw the landmark index
        cv2.putText(blank_image, f'{i}', (cx, cy - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)

    # Draw connections between landmarks
    for connection in HAND_CONNECTIONS:
        point1 = points[connection[0]]
        point2 = points[connection[1]]
        cv2.line(blank_image, point1, point2, color, 2)

# Process all CSV files in the input folder
for csv_file in os.listdir(input_folder):
    if csv_file.endswith('.csv'):
        csv_path = os.path.join(input_folder, csv_file)

        # Read CSV file using pandas
        df = pd.read_csv(csv_path)

        # Set output video path based on the CSV filename
        video_filename = os.path.splitext(csv_file)[0] + '_output.mp4'
        output_video_path = os.path.join(output_folder, video_filename)

        # Initialize VideoWriter to save the video
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')  # Codec
        out = cv2.VideoWriter(output_video_path, fourcc, fps, (frame_width, frame_height))

        # Process the CSV frame by frame
        frame_numbers = df['frame_number'].unique()

        for frame_number in frame_numbers:
            # Create a blank black image
            blank_image = np.zeros((frame_height, frame_width, 3), dtype=np.uint8)  # Adjust resolution as needed

            # Extract all hands for this frame
            frame_data = df[df['frame_number'] == frame_number]

            for index, hand_data in frame_data.iterrows():
                hand_id = hand_data['hand_id']
                hand_label = hand_data['hand_label']  # Left or Right Hand
                hand_landmarks = hand_data[[f'x_{i}' for i in range(NUM_LANDMARKS)] + 
                                           [f'y_{i}' for i in range(NUM_LANDMARKS)] + 
                                           [f'z_{i}' for i in range(NUM_LANDMARKS)]].values

                # Choose color based on hand ID
                if hand_id == 0:
                    color = COLOR_HAND_1
                else:
                    color = COLOR_HAND_2

                # Draw the hand landmarks and connections for the given hand
                draw_hand_landmarks(blank_image, hand_landmarks, color)

            # Write the current frame to the video
            out.write(blank_image)

        # Release the VideoWriter for this video
        out.release()
        print(f"Processed video saved: {output_video_path}")

# Close OpenCV windows if any were opened
cv2.destroyAllWindows()
