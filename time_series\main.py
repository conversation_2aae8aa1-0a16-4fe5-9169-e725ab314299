import numpy as np
from pycatch22 import catch22_all
import os
import pandas as pd
import glob
from scipy import signal, stats

from calculator import calculate_time_series_statistics
def normalize_lasndmarks_data(df):
    # Extract the x and y coordinates for the nose tip (landmark 33)
    nose_tip_x = df['Landmark_33_x']
    nose_tip_y = df['Landmark_33_y']

    # Initialize a new DataFrame to store distances
    distances = pd.DataFrame()

    # Calculate the distance of each landmark from the nose tip
    for i in range(68):
        x_column = f'Landmark_{i}_x'
        y_column = f'Landmark_{i}_y'
        distances[f'distance_{i}'] = np.sqrt((df[x_column] - nose_tip_x)**2 + (df[y_column] - nose_tip_y)**2)

    # Normalize the distances
    max_distance = distances.max(axis=1)
    normalized_distances = distances.divide(max_distance, axis=0)
    normalized_distances['Frame'] = df.iloc[:, 0]   
    return normalized_distances

def time_series_features_folder(data_folder, output_folder,type):
    # Get all the csv files in the folder
    files = glob.glob(data_folder+'*.csv')
    # Create a list to store the features
    
    # Loop through each file
    for file in files:
        print(f'Process:  {file}')
        # Load the data
        data = pd.read_csv(file)
        features_list = []
        csv_name = os.path.basename(file).replace('.csv','')+'_TSF.csv'
        csv_dest = os.path.join(output_folder,csv_name)
        if os.path.exists(csv_dest):
            continue
        try:
            if type == 'landmarks':
                data = normalize_lasndmarks_data(data)
            # Calculate the features for each data column
            for column in data.columns:
                if column == 'frame' or column == 'Frame' or  column == ' timestamp' or column == ' face_id' or column == ' success' or column == ' confidence':
                    continue
                features = calculate_time_series_statistics(column.replace(' ',''), data[column].values)
                # Append the features to the list
                features_list.append(features)
            # Create a DataFrame from the list
            features_df = pd.DataFrame(features_list)
            # Save the DataFrame to a CSV file
            features_df.to_csv(csv_dest, index=False)
        except:
            print(f'Error:  {file}')
            continue
        
def make_sure_exist(folder):
    if not os.path.exists(folder):
        os.mkdir(folder)
    return folder


if __name__ == '__main__':    
    emotions = ['disgust', 'angry', 'happy', 'sad']
    tasks = ['imitation','text']
    #origin_folder = '//files.ubc.ca/team/PPRC/Camera/Booth_Processed/facial_expression/'
    origin_folder = 'D:\\Atefeh\\Datasets\\Parkinson\\booth\\'

    for emotion in emotions:
        for task in tasks:  
            for type in ['landmarks','action_units','emotions']:
                data_folder = f'{origin_folder}/facial_expression/{emotion}/{task}/{type}/'
                output_folder = f'{origin_folder}/facial_expression/{emotion}/{task}/time_series_features/{type}/'
                make_sure_exist(output_folder)
                time_series_features_folder(data_folder, output_folder,type) 