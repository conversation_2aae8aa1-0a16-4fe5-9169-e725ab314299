import os
import shutil

def copy_videos(src_directory, dest_directory, task, emotion, parent_task):
    for user_id in os.listdir(src_directory):
        user_path = os.path.join(src_directory, user_id)
        if os.path.isdir(user_path):
            for timestamp in os.listdir(user_path):
                timestamp_path = os.path.join(user_path, timestamp)
                if os.path.isdir(timestamp_path):
                    face_path = os.path.join(timestamp_path, parent_task)
                    if emotion or parent_task == 'eye_movement':
                        face_path = os.path.join(face_path, f'{task}')
                    if os.path.isdir(face_path):
                        for video in os.listdir(face_path):
                            video_path = os.path.join(face_path, video)
                            new_name = f"{user_id}_{timestamp}_{video}"
                            if os.path.isfile(video_path) and ((emotion is None and task in video) or f'{emotion}' in video):
                                dest_path = os.path.join(dest_directory, new_name)
                                if not os.path.exists(dest_path):
                                    shutil.copy(video_path, dest_path)
                                    print(f"Copying {video_path} to {dest_path}")
