import os
import pandas as pd
import matplotlib.pyplot as plt
from plotter import plot_time_series

BASE_PROCESSED_DIRECTORY = r'\\files.ubc.ca\team\PPRC\Camera\Booth_Processed'
BASE_RESULTS_DIRECTORY = r'\\files.ubc.ca\team\PPRC\Camera\Booth_Results'

def make_sure_dir_exists(directory):
    if not os.path.exists(directory):
        os.makedirs(directory)
        
def main():
    # Define file paths
    for task in ['left', 'right']:
        dataset_folder = fr'{BASE_PROCESSED_DIRECTORY}\finger_tapping\{task}\distances'
        output_main_directory = fr'{BASE_RESULTS_DIRECTORY}\finger_tapping_ws\Experiment2\plots'
        for file in os.listdir(dataset_folder):
            if file.endswith('.csv'):
                try:                    
                    df = pd.read_csv(f'{dataset_folder}/{file}')

                    # Extract columns (assuming 'frame_number' is a common column in all files)
                    frame_numbers = df['Frame']
                    angular_distances = df['Angular Distance']
                    finger_distances = df['Finger Distance']
                    normalized_finger_distances = df['Finger Normalized Distance']

                    # Plot and save the results
                    output_dir = f'{output_main_directory}/angular_distance'
                    make_sure_dir_exists(output_dir)
                    plot_time_series(f'angular_distance_{file}', angular_distances, x=frame_numbers, title='Angular Distance Over Time', output_dir=output_dir, ylabel='Angle (Degrees)')
                    output_dir = f'{output_main_directory}/finger_distance'
                    make_sure_dir_exists(output_dir)
                    plot_time_series(f'finger_distance_{file}', finger_distances, x=frame_numbers, title='Finger Distance Over Time', output_dir=output_dir, ylabel='Distance (Pixels)')
                    output_dir = f'{output_main_directory}/normalized_finger_distance'
                    make_sure_dir_exists(output_dir)
                    plot_time_series(f'normalized_finger_distance_{file}', normalized_finger_distances, x=frame_numbers, title='Normalized Finger Distance Over Time', output_dir=output_dir, ylabel='Normalized Distance')
                except Exception as e:
                    print(f'Error processing {file}: {str(e)}')
                    continue
if __name__ == "__main__":
    main()
