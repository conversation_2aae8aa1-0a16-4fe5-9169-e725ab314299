import pandas as pd
import numpy as np
import os
import plotly.graph_objects as go
from scipy.signal import butter, filtfilt, detrend
from scipy.fft import fft, fftfreq
import matplotlib.pyplot as plt

def show_fft(file_path, destination_path):
    df = pd.read_csv(file_path)
    user = file_path.split("\\")[-1].split("_")[0]

    # Ensure necessary columns
    required_columns = {'Frame', 'Yaw', 'Pitch', 'Roll', 'X_Position', 'Y_Position'}
    if not required_columns.issubset(df.columns):
        raise ValueError(f"CSV is missing columns: {required_columns - set(df.columns)}")

    # Calculate distance from center
    df["Distance_from_Center"] = np.sqrt(df["X_Position"]**2 + df["Y_Position"]**2) 
    df["Distance_from_Center"] = df["Distance_from_Center"] - df["Distance_from_Center"][0]

    # Define preprocessing functions
    def butter_lowpass_filter(data, cutoff=3, fs=60, order=3):
        return data
        """Applies a Butterworth low-pass filter to smooth the data."""
        nyquist = 0.5 * fs
        normal_cutoff = cutoff / nyquist
        b, a = butter(order, normal_cutoff, btype='low', analog=False)
        return filtfilt(b, a, data)

    # Apply preprocessing: Remove trend, then low-pass filter
    df["Yaw_Filtered"] = butter_lowpass_filter(detrend(df["Yaw"]))
    df["Pitch_Filtered"] = butter_lowpass_filter(detrend(df["Pitch"]))
    df["Roll_Filtered"] = butter_lowpass_filter(detrend(df["Roll"]))
    df["Distance_from_Center_Filtered"] = butter_lowpass_filter(detrend(df["Distance_from_Center"]))

    # Compute FFT after filtering
    def compute_fft(data, sampling_rate=60):
        """Computes FFT for frequency analysis after filtering."""
        n = len(data)
        freqs = fftfreq(n, d=1/sampling_rate)[:n//2]  # Positive frequencies only
        fft_values = np.abs(fft(data))[:n//2]
        return freqs, fft_values

    freqs, yaw_fft = compute_fft(df["Yaw_Filtered"].values)
    _, pitch_fft = compute_fft(df["Pitch_Filtered"].values)
    _, roll_fft = compute_fft(df["Roll_Filtered"].values)
    _, distance_fft = compute_fft(df["Distance_from_Center_Filtered"].values)

    # Plot Tremor Frequency (FFT) using Matplotlib
    plt.figure(figsize=(10, 6))
    plt.plot(freqs, yaw_fft, label='Yaw Frequency', color='blue')
    plt.plot(freqs, pitch_fft, label='Pitch Frequency', color='red')
    plt.plot(freqs, roll_fft, label='Roll Frequency', color='green')
    plt.plot(freqs, distance_fft, label='Distance Frequency', color='purple', linestyle='dotted')

    # Set labels and title
    plt.xlabel("Frequency (Hz)")
    plt.ylabel("Amplitude")
    plt.ylim(0, 300)  # Set Y-axis range
    plt.title(f"Tremor Frequency Analysis (Filtered FFT) for User {user}")
    plt.xlim(0.5, 7)  # Set X-axis range
    plt.legend()
    plt.grid(True)

    # Save the plot as an image
    path_save = os.path.join(destination_path, f"{user}_tremor_fft.png")
    plt.savefig(path_save, dpi=300)
    plt.close()
    
    print(f"Saved FFT plot for {user} to {path_save}")

    

import matplotlib.pyplot as plt

def show_head_rotations(csv_file, destin_path):
    df = pd.read_csv(csv_file)
    user = csv_file.split("\\")[-1].split("_")[0]

    # Ensure necessary columns exist
    required_columns = {'Frame', 'Yaw', 'Pitch', 'Roll', 'X_Position', 'Y_Position'}
    if not required_columns.issubset(df.columns):
        raise ValueError(f"CSV file is missing required columns: {required_columns - set(df.columns)}")

    # Calculate distance from center
    df["Distance_from_Center"] = np.sqrt(df["X_Position"]**2 + df["Y_Position"]**2) 
    df["Distance_from_Center"] -= df["Distance_from_Center"][0]
    df["Yaw"] -= df["Yaw"][0]
    df["Pitch"] -= df["Pitch"][0]
    df["Roll"] -= df["Roll"][0]
    
    # Define preprocessing function
    def butter_lowpass_filter(data, cutoff=3, fs=60, order=5):
        """Applies a Butterworth low-pass filter to smooth the data."""
        nyquist = 0.5 * fs
        normal_cutoff = cutoff / nyquist
        b, a = butter(order, normal_cutoff, btype='low', analog=False)
        return filtfilt(b, a, data)

    # Apply preprocessing: Remove trend, then low-pass filter
    df["Yaw"] = butter_lowpass_filter(detrend(df["Yaw"]))
    df["Pitch"] = butter_lowpass_filter(detrend(df["Pitch"]))
    df["Roll"] = butter_lowpass_filter(detrend(df["Roll"]))
    df["Distance_from_Center"] = butter_lowpass_filter(detrend(df["Distance_from_Center"]))

    # Plot head rotations
    fig, ax1 = plt.subplots(figsize=(10, 6))

    ax1.plot(df["Frame"], df["Yaw"], label='Yaw', color='blue')
    ax1.plot(df["Frame"], df["Pitch"], label='Pitch', color='red')
    ax1.plot(df["Frame"], df["Roll"], label='Roll', color='green')
    ax1.set_xlabel("Frame")
    ax1.set_ylabel("Angle (Degrees)")
    ax1.set_title(f"Head Pose Tremor Analysis (Yaw, Pitch, Roll) & Distance for User {user}")
    ax1.legend(loc="upper left")

    # Secondary y-axis for Distance from Center
    ax2 = ax1.twinx()
    ax2.plot(df["Frame"], df["Distance_from_Center"], label='Distance from Center', color='purple', linestyle='dotted')
    ax2.set_ylabel("Distance from Center")

    fig.legend(loc="upper right")
    fig.tight_layout()

    # Save plot as an image
    path_save = os.path.join(destin_path, f"{user}_head_rotations.png")
    plt.savefig(path_save, dpi=300)
    plt.close()

    print(f"Saved plot for {user} to {path_save}")

    
#generate and save picture for all files in folder
import os
folder_path = r"\\files.ubc.ca\team\PPRC\CAMERA\Booth_Processed\facial_expression\basic_facial\text\head_pose_features"
for file in os.listdir(folder_path):
    if file.endswith(".csv"):
        file_path = os.path.join(folder_path, file)
        destination_path = r"\\files.ubc.ca\team\PPRC\CAMERA\Booth_Processed\facial_expression\basic_facial\text\tremor_freq_charts"
        show_fft(file_path,destination_path)
        destination_path = r"\\files.ubc.ca\team\PPRC\CAMERA\Booth_Processed\facial_expression\basic_facial\text\tremor_charts"
        show_head_rotations(file_path,destination_path)