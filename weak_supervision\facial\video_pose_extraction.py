import cv2
import mediapipe as mp
import pandas as pd
import os
from multiprocessing import Pool
import logging
import numpy as np

logging.getLogger("mediapipe").setLevel(logging.ERROR)

# Initialize MediaPipe once globally
mp_face_mesh = mp.solutions.face_mesh

def process_frame(frame_data):
    """Extracts head pose (rotation & position) from a single frame."""
    frame_number, frame = frame_data
    face_mesh = mp_face_mesh.FaceMesh(static_image_mode=False, max_num_faces=1, refine_landmarks=True)

    frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
    results = face_mesh.process(frame_rgb)

    if results.multi_face_landmarks:
        for face_landmarks in results.multi_face_landmarks:
            # Extract key points
            nose = face_landmarks.landmark[1]
            left_eye = face_landmarks.landmark[33]
            right_eye = face_landmarks.landmark[263]
            chin = face_landmarks.landmark[199]
            forehead = face_landmarks.landmark[10]

            h, w, _ = frame.shape
            center_x, center_y = w // 2, h // 2  # Image center

            # Convert normalized coordinates to pixel values
            nose_pt = (int(nose.x * w), int(nose.y * h))
            left_eye_pt = (int(left_eye.x * w), int(left_eye.y * h))
            right_eye_pt = (int(right_eye.x * w), int(right_eye.y * h))
            chin_pt = (int(chin.x * w), int(chin.y * h))
            forehead_pt = (int(forehead.x * w), int(forehead.y * h))

            # Compute rotation angles
            dx = right_eye_pt[0] - left_eye_pt[0]
            dy = right_eye_pt[1] - left_eye_pt[1]
            roll = np.degrees(np.arctan2(dy, dx))  # Tilt angle (Roll)

            dx_nose = nose_pt[0] - (left_eye_pt[0] + right_eye_pt[0]) / 2
            yaw = np.degrees(np.arctan2(dx_nose, dx))  # Yaw angle (Left-Right)

            dy_nose = nose_pt[1] - forehead_pt[1]
            dy_chin = chin_pt[1] - forehead_pt[1]
            pitch = np.degrees(np.arctan2(dy_nose, dy_chin))  # Corrected Up-Down angle

            # Compute head position (X, Y, Z)
            head_x = nose_pt[0] - center_x  # Left (-) / Right (+)
            head_y = nose_pt[1] - center_y  # Up (-) / Down (+)
            head_z = np.linalg.norm(np.array(left_eye_pt) - np.array(right_eye_pt))  # Distance between eyes

            return [frame_number, yaw, pitch, roll, head_x, head_y, head_z]

    return None  # If no face detected, return None

class VideoHeadPoseExtractor:
    def __init__(self, video_path, output_csv_folder):
        self.video_path = video_path
        self.output_csv_folder = output_csv_folder

    def extract_head_pose(self):
        """Extracts head pose features (rotation & position) from the video."""
        video_name = os.path.splitext(os.path.basename(self.video_path))[0]
        head_pose_file = os.path.join(self.output_csv_folder, f'{video_name}_head_pose.csv')

        if os.path.exists(head_pose_file):
            return head_pose_file  # Skip if already processed

        cap = cv2.VideoCapture(self.video_path)

        frames = []
        frame_count = 0
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            frames.append((frame_count, frame))
            frame_count += 1

        cap.release()
        cv2.destroyAllWindows()

        # Use multiprocessing for faster processing
        with Pool(15) as pool:
            head_pose_data = pool.map(process_frame, frames)

        # Filter out None values
        head_pose_data = [pose for pose in head_pose_data if pose is not None]

        # Define column names
        columns = ['Frame', 'Yaw', 'Pitch', 'Roll', 'X_Position', 'Y_Position', 'Z_Depth']
        head_pose_df = pd.DataFrame(head_pose_data, columns=columns)

        # Save to CSV
        head_pose_df.to_csv(head_pose_file, index=False)
        return head_pose_file

class FolderHeadPoseExtractor:
    def __init__(self, video_folder_path, output_folder_path):
        self.video_folder_path = video_folder_path
        self.output_folder_path = output_folder_path

    def extract_head_pose(self):
        """Extract head pose features from all videos in the folder."""
        for video_file in os.listdir(self.video_folder_path):
            if video_file.endswith(('.mp4', '.avi', '.mov')):  # Ensure valid video files
                video_path = os.path.join(self.video_folder_path, video_file)
                output_csv_path = self.output_folder_path

                # Process each video
                video_pose_extractor = VideoHeadPoseExtractor(video_path, output_csv_path)
                video_pose_extractor.extract_head_pose()

if __name__ == '__main__':
    video_folder = r'\\files.ubc.ca\team\PPRC\CAMERA\Booth_Processed\facial_expression\basic_facial\text\videos'
    output_folder = r'\\files.ubc.ca\team\PPRC\CAMERA\Booth_Processed\facial_expression\basic_facial\text\head_pose_features'
    folder_extractor = FolderHeadPoseExtractor(video_folder, output_folder)
    folder_extractor.extract_head_pose()
