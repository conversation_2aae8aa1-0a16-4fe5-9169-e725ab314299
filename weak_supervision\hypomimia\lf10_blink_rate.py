import os
import pandas as pd
from sklearn.ensemble import RandomForestClassifier
import sys
sys.path.insert(0, 'd:\\Atefeh\\Codes\\Python\\ParkinsonAssessment')
from weak_supervision.LFBase import LFBaseClass
from datasets.load_data import load_labels_with_ID, load_emotion_task_ts_data, get_train_classifiers_set, merge_df_with_labels
from helpers.classification_analysis import perform_analysis_cv, save_trained_model
from scipy.signal import find_peaks

class LF10Class(LFBaseClass):
    def __init__(self, model_path = '.\\models\\best_lf10_v4.json', predict='Eye Bli0ki0g'):
        origin_folder = '\\\\files.ubc.ca\\team\\PPRC\\Camera\\Booth_Processed'
        super().__init__(model_path=model_path,predict=predict,origin_folder=origin_folder)

      
    def prepare_dataset(self):
            tasks = ['left', 'right']
            data = pd.DataFrame()  # Initialize an empty DataFrame to hold all data

            for task in tasks:
                root_folder = f'{self.origin_folder}\\finger_tapping\\{task}\\csvs'
                for file_name in os.listdir(root_folder):
                    if file_name == f'blink_results_{task}.csv':
                        temp_data = pd.read_csv(os.path.join(root_folder, file_name))
                        if 'ID' in temp_data.columns:
                            temp_data.set_index('ID', inplace=True)
                        else:
                            print(f"No 'ID' column found in {file_name}")

                        if data.empty:
                            data = temp_data  # First DataFrame to join, no need for suffixes
                        else:
                            data = data.join(temp_data, how='outer', rsuffix=f'_right', lsuffix=f'_left')

            data['Blinks'] = data['Blinks_left'] + data['Blinks_right']
            data['Duration'] = data['Duration_left'] + data['Duration_right']
            data['blink_rate_right'] = data['Blinks_right'] / data['Duration_right']
            data['blink_rate_left'] = data['Blinks_left'] / data['Duration_left']
            data.drop(columns=['Blinks_left', 'Blinks_right', 'Duration_left', 'Duration_right'], inplace=True)
            data['blink_rate'] = data['Blinks'] / data['Duration']

            # Load the feature DataFrame
            feature_df_l = pd.read_csv(f'{self.origin_folder}\\finger_tapping\\right\\csvs\\features_blinking_right.csv')
            feature_df_r = pd.read_csv(f'{self.origin_folder}\\finger_tapping\\left\\csvs\\features_blinking_left.csv')
            feature_df_l.drop(columns=['ID'], inplace=True)
            feature_df_r.drop(columns=['ID'], inplace=True)
            feature_df_l.set_index('label', inplace=True)
            feature_df_r.set_index('label', inplace=True)
            feature_df_l.columns = [f'{col}_feature' for col in feature_df_l.columns]
            feature_df_r.columns = [f'{col}_feature' for col in feature_df_r.columns]
            feature_df = feature_df_l.join(feature_df_r, how='outer',lsuffix='_right', rsuffix='_left')
            # Combine the data with the feature DataFrame
            data = data.join(feature_df, how='outer')

            # Load and prepare labels DataFrame
            booth_updrs_df = load_labels_with_ID(os.path.join(self.origin_folder, 'docs'), 'booth_updrs.csv')
            data.fillna(0, inplace=True)
            data['ID'] = data.index.astype(int)
            data.set_index('ID', inplace=True)

            # Merge with labels
            merged_df = pd.merge(booth_updrs_df[self.predict], data, on='ID', how='left')
            merged_df.dropna(subset=[self.predict], inplace=True)

            return merged_df

    def train_classifier(self):
        return super().train_classifier('blink_rate|_feature')
    
    def labeling_function(self, x):
        # prediction = super().labeling_function(x)
        # if prediction == 0:
        #     return 0
        # else:
        #     return 3
        
        if x['Duration'] <= 5:
            return -1
        if x['blink_rate'] <= 0 :
            return 4
        if x['blink_rate'] > 0 and x['blink_rate'] <= 0.2:
            return 3
        if x['blink_rate'] > 0.2 :
            return 0
        else:
            print(x['blink_rate'] )
            return -1


        
# model_path = f'.\\models\\best_LF10Class___v5.json'          
# lf = LF10Class(model_path=model_path)
# dataset = lf.prepare_dataset()
# # #draw blink_rate vs duration and color it with Facial_Expression_UPDRS (custom)
# # import matplotlib.pyplot as plt
# # import seaborn as sns
# # import numpy as np
# # sns.scatterplot(data=dataset, x='blink_rate', y='blink_rate_right', hue='Facial_Expression_UPDRS (custom)')
# # plt.show()

# classifier, selected_features =  lf.train_classifier()
# lf.save_model(classifier, selected_features)
# lf.evaluate_classifier(classifier, selected_features)



