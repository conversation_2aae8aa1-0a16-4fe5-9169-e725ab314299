
from matplotlib import pyplot as plt


def plot_signal_with_peaks(signal,peaks,min_y=0,max_y=100):
    # Plotting the signal and the peaks for visualization
    plt.figure(figsize=(10, 6))
    plt.plot(signal, label='Signal')
    plt.plot(peaks, signal[peaks], "x", label='Peaks')
    plt.title('Signal with Detected Peaks')
    plt.xlabel('Sample Index')
    #set max y axis to 100
    plt.ylim(min_y,max_y)
    plt.ylabel('Signal Amplitude')
    plt.legend()
    plt.show()
    
def plot_signal(signal,min_y=0,max_y=100):
    # Plotting the signal and the peaks for visualization
    plt.figure(figsize=(10, 6))
    plt.plot(signal, label='Signal')
    plt.title('Signal')
    plt.xlabel('Sample Index')
    #set max y axis to 100
    plt.ylim(min_y,max_y)
    plt.ylabel('Signal Amplitude')
    plt.legend()
    plt.show()