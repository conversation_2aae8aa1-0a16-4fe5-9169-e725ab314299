import os
import pandas as pd
from sklearn.ensemble import RandomForestClassifier
import sys
sys.path.insert(0, 'd:\\Atefeh\\Codes\\Python\\ParkinsonAssessment')
from time_series.calculator import analyze_signal_shape
from weak_supervision.LFBase import LFBaseClass
from datasets.load_data import load_labels_with_ID, load_emotion_task_ts_data, get_train_classifiers_set, merge_df_with_labels
from helpers.classification_analysis import perform_analysis_cv, save_trained_model


class LF5Class(LFBaseClass):
    def __init__(self, model_path = '.\\models\\best_lf5_v2.json',task='imitation',emotion='happy'):
        super().__init__()
        self.task = task
        self.emotion = emotion
        self.model_path = model_path
        
        
    

    def prepare_dataset(self):
        # Create an empty list to store the results
        results = []
        # Iterate over tasks and emotions
        
        root_folder = f'{self.origin_folder}\\facial_expression\\{self.emotion}\\{self.task}\\emotions'
        signal_analysis_results = []        
        # Iterate over all CSV files in the folder
        for file_name in os.listdir(root_folder):
            if file_name.endswith('.csv'):
                # Read the data from the CSV file
                file_path = os.path.join(root_folder, file_name)
                df = pd.read_csv(file_path)
                data =  df[self.emotion].rolling(window=10, center=True).mean()
                data.dropna(inplace=True)
                results = analyze_signal_shape(data)
                results['ID'] = file_name.split('_')[0]
                signal_analysis_results.append(results)

        # Create a DataFrame from the results
        data_df = pd.DataFrame(signal_analysis_results)

        # Pivot the DataFrame to get columns for each task_emotion combination
        data_df['ID'] = data_df['ID'].astype(int)
        data_df.set_index('ID', inplace=True)


        # Step 4: Merge the pivoted DataFrame with booth_updrs_df on the 'ID' column
        booth_updrs_df = load_labels_with_ID(os.path.join(self.origin_folder, 'docs'), 'booth_updrs.csv')
        merged_df = merge_df_with_labels(data_df, booth_updrs_df, self.predict)
        return merged_df

    def train_classifier(self):
        return super().train_classifier('signal_')

        
        
# lf = LF5Class(emotion='happy',task='text')
# classifier, selected_features =  lf.train_classifier()
# lf.save_model(classifier, selected_features)
# lf.evaluate_classifier(classifier, selected_features)


