{"cells": [{"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["from facial_dataset import FacialDataset\n", "au_sets = {\n", "    'basic_facial': ['AU01_r', 'AU02_r', 'AU04_r', 'AU05_r', 'AU06_r', 'AU07_r', 'AU09_r', 'AU10_r', 'AU12_r', 'AU14_r', 'AU15_r', 'AU17_r', 'AU20_r', 'AU23_r', 'AU25_r', 'AU26_r', 'AU45_r',\n", "                     'AU01_c', 'AU02_c', 'AU04_c', 'AU05_c', 'AU06_c', 'AU07_c', 'AU09_c', 'AU10_c', 'AU12_c', 'AU14_c', 'AU15_c', 'AU17_c', 'AU20_c', 'AU23_c', 'AU25_c', 'AU26_c', 'AU45_c'],\n", "}\n", "facial_data = FacialDataset(r'\\\\files.ubc.ca\\team\\PPRC\\CAMERA\\Booth_Processed', au_sets,emotions=['basic_facial'],regex_filter=None,task_type='text')"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_22800\\1001960833.py:10: FutureWarning:\n", "\n", "Downcasting behavior in `replace` is deprecated and will be removed in a future version. To retain the old behavior, explicitly call `result.infer_objects(copy=False)`. To opt-in to the future behavior, set `pd.set_option('future.no_silent_downcasting', True)`\n", "\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_22800\\1001960833.py:14: FutureWarning:\n", "\n", "Downcasting behavior in `replace` is deprecated and will be removed in a future version. To retain the old behavior, explicitly call `result.infer_objects(copy=False)`. To opt-in to the future behavior, set `pd.set_option('future.no_silent_downcasting', True)`\n", "\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_22800\\1001960833.py:16: FutureWarning:\n", "\n", "Downcasting behavior in `replace` is deprecated and will be removed in a future version. To retain the old behavior, explicitly call `result.infer_objects(copy=False)`. To opt-in to the future behavior, set `pd.set_option('future.no_silent_downcasting', True)`\n", "\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_22800\\1001960833.py:18: FutureWarning:\n", "\n", "Downcasting behavior in `replace` is deprecated and will be removed in a future version. To retain the old behavior, explicitly call `result.infer_objects(copy=False)`. To opt-in to the future behavior, set `pd.set_option('future.no_silent_downcasting', True)`\n", "\n"]}], "source": ["# Read lf data from a file\n", "import pandas as pd\n", "lf_path = r'\\\\files.ubc.ca\\team\\PPRC\\CAMERA\\Booth_Processed\\docs\\facial_labels.csv'\n", "df_lfs = pd.read_csv(lf_path)\n", "\n", "#Clean data\n", "df_lfs.set_index('ID', inplace=True)\n", "df_lfs = df_lfs.drop(columns=['Date'])\n", "df_lfs = df_lfs.replace('N', 0)\n", "df_lfs = df_lfs.replace('D', 1)\n", "df_lfs = df_lfs.replace('VERY POOR', 0)\n", "df_lfs = df_lfs.replace('POOR', 1)\n", "df_lfs = df_lfs.replace('FAIR', 2)\n", "df_lfs = df_lfs.replace('GOOD', 3)\n", "df_lfs = df_lfs.replace('SOME', 1)\n", "df_lfs = df_lfs.replace('MOST', 2)\n", "df_lfs = df_lfs.replace('PD', 1)\n", "df_lfs = df_lfs.replace('HC', 0)\n", "df_lfs.rename(columns = {'F_UPDRS_KW_1': 'label #1','F_UPDRS_KW_2': 'label #2', 'F_UPDRS_TM': 'label #3' },inplace=True)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["df_lfs['label_binary'] = df_lfs['label #3'].apply(lambda x: 1 if x > 0 else 0)\n", "facial_data.set_labels(df_lfs['label_binary'].rename('Label'),drpna=True)"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                                               Feature     Score\n", "44                       catch22_CO_FirstMin_ac_AU15_c  6.627646\n", "468                                         min_AU14_c  5.447421\n", "434                                        mean_AU14_c  4.206710\n", "485                                         rms_AU14_c  3.849132\n", "451                                      median_AU14_c  3.237329\n", "239           catch22_PD_PeriodicityWang_th0_01_AU02_c  3.132976\n", "318  catch22_SB_TransitionMatrix_3ac_sumdiagcov_AU20_c  3.110642\n", "216  catch22_IN_AutoMutualInfoStats_40_gaussian_fmm...  3.050121\n", "472                                         min_AU23_c  2.867181\n", "462                                         min_AU05_c  2.853503\n", "456                                      median_AU25_c  2.803571\n", "420                                         max_AU20_c  2.621876\n", "12                                    amplitude_AU20_c  2.621876\n", "417                                         max_AU14_c  2.538680\n", "373    catch22_SP_Summaries_welch_rect_area_5_1_AU45_c  2.489972\n", "356  catch22_SC_FluctAnal_2_rsrangefit_50_1_logi_pr...  2.486132\n", "38                       catch22_CO_FirstMin_ac_AU06_c  2.477348\n", "267    catch22_SB_BinaryStats_diff_longstretch0_AU20_c  2.333331\n", "450                                      median_AU12_c  2.320519\n", "505                                    skewness_AU20_c  2.263355\n"]}], "source": ["import numpy as np\n", "import pandas as pd\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "from sklearn.feature_selection import SelectKBest, chi2\n", "from sklearn.preprocessing import MinMaxScaler\n", "\n", "# Get the data and labels\n", "X = facial_data.get_data()\n", "y = facial_data.get_labels()\n", "\n", "# Store column names before transformation\n", "feature_names = X.columns  \n", "\n", "# Fill NaN values before transformation\n", "<PERSON><PERSON>fillna(0, inplace=True)\n", "\n", "# Use MinMaxScaler to ensure all values are non-negative\n", "scaler = MinMaxScaler()\n", "X_scaled = scaler.fit_transform(X)  # X is now a NumPy array\n", "\n", "# Feature selection\n", "bestfeatures = SelectKBest(score_func=chi2, k='all')\n", "fit = bestfeatures.fit(X_scaled, y)\n", "\n", "# Convert results to DataFrame\n", "dfscores = pd.DataFrame(fit.scores_, columns=[\"Score\"])\n", "dfcolumns = pd.DataFrame(feature_names, columns=[\"Feature\"])  # Use original feature names\n", "\n", "# Combine and display top 20 features\n", "feature_scores = pd.concat([dfcolumns, dfscores], axis=1)\n", "feature_scores = feature_scores.sort_values(by=\"Score\", ascending=False)\n", "\n", "print(feature_scores.nlargest(20, 'Score'))\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                                                Feature   p-value\n", "543     catch22_SB_BinaryStats_diff_longstretch0_AU45_r  0.000274\n", "587            catch22_SB_MotifThree_quantile_hh_AU06_r  0.000442\n", "373       catch22_FC_LocalSimple_mean1_tauresrat_AU45_r  0.001507\n", "389          catch22_FC_LocalSimple_mean3_stderr_AU10_r  0.001961\n", "581            catch22_SB_MotifThree_quantile_hh_AU02_r  0.002221\n", "...                                                 ...       ...\n", "405          catch22_FC_LocalSimple_mean3_stderr_AU26_r  0.048547\n", "739     catch22_SP_Summaries_welch_rect_area_5_1_AU20_r  0.048930\n", "67    catch22_CO_Embed2_Dist_tau_d_expfit_meandiff_A...  0.049398\n", "1035                          standard_deviation_AU10_r  0.049534\n", "300        catch22_DN_OutlierInclude_n_001_mdrmd_AU25_c  0.049538\n", "\n", "[137 rows x 2 columns]\n"]}], "source": ["import numpy as np\n", "import pandas as pd\n", "from scipy.stats import ttest_ind\n", "\n", "# Get the data and labels\n", "X = facial_data.get_data()  # Features\n", "y = facial_data.get_labels()  # Labels (assumed binary: 0 and 1)\n", "\n", "# Convert to DataFrame for easier handling\n", "X = pd.DataFrame(X)\n", "<PERSON>.fillna(0, inplace=True)  # Fill NaN values\n", "# Define feature groups based on their Action Unit (AU) category\n", "# Ensure y is a NumPy array\n", "y = np.array(y)\n", "\n", "# Split X into two groups based on y labels\n", "group1 = X[y == 0]  # Subset where y == 0\n", "group2 = X[y == 1]  # Subset where y == 1\n", "\n", "# Perform t-test for each feature\n", "p_values = []\n", "feature_names = X.columns  # Store original feature names\n", "\n", "for feature in feature_names:\n", "    t_stat, p_val = ttest_ind(group1[feature], group2[feature], equal_var=False)  # <PERSON>’s t-test\n", "    p_values.append(p_val)\n", "\n", "# Convert results to DataFrame\n", "t_test_results = pd.DataFrame({\n", "    \"Feature\": feature_names,\n", "    \"p-value\": p_values\n", "})\n", "\n", "# Sort by p-value in ascending order\n", "t_test_results = t_test_results.sort_values(by=\"p-value\")\n", "\n", "# Display significant features (p < 0.05)\n", "significant_features = t_test_results[t_test_results[\"p-value\"] < 0.05]\n", "print(significant_features)"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [], "source": ["import plotly.express as px\n", "import pandas as pd\n", "import numpy as np\n", "from scipy.stats import ttest_ind\n", "import plotly.io as pio\n", "pio.renderers.default = \"browser\"\n", "# Change the renderer\n", "facial_data.set_labels(df_lfs['label_binary'].rename('Label'),drpna=True)\n", "# Get the data and labels\n", "X = facial_data.get_data()  # Features\n", "y = facial_data.get_labels()  # Labels (assumed binary: 0 and 1)\n", "\n", "\n", "# Ensure y is a NumPy array\n", "y = np.array(y)\n", "\n", "# Split X into two groups based on y labels\n", "group1 = X[y == 0]  # Subset where y == 0\n", "group2 = X[y == 1]  # Subset where y == 1\n", "\n", "# Perform t-test for each feature\n", "p_values = []\n", "feature_names = X.columns  # Store original feature names\n", "\n", "for feature in feature_names:\n", "    t_stat, p_val = ttest_ind(group1[feature], group2[feature], equal_var=False)  # <PERSON>’s t-test\n", "    p_values.append(p_val)\n", "\n", "# Convert results to DataFrame\n", "t_test_results = pd.DataFrame({\n", "    \"Feature\": feature_names,\n", "    \"p-value\": p_values\n", "})\n", "\n", "# Sort by p-value in ascending order\n", "t_test_results = t_test_results.sort_values(by=\"p-value\")\n", "\n", "# Display significant features (p < 0.05)\n", "significant_features = t_test_results[t_test_results[\"p-value\"] < 0.05]\n", "\n", "# Selecting top significant features for visualization\n", "top_features = significant_features[\"Feature\"].head(5).tolist()  # Selecting top 5 significant features\n", "\n", "# Prepare data for visualization\n", "X_selected = X[top_features].copy()\n", "X_selected[\"Label\"] = y  # Add label column\n", "\n", "# Convert to long format for plotly\n", "X_long = X_selected.melt(id_vars=\"Label\", var_name=\"Feature\", value_name=\"Value\")\n", "\n", "# Create violin plot using plotly\n", "fig = px.violin(X_long, x=\"Feature\", y=\"Value\", color=X_long[\"Label\"].astype(str),\n", "                box=True, points=\"all\", title=\"Violin Plot of Top Significant Features\",\n", "                labels={\"Label\": \"Group\", \"Value\": \"Feature Value\"})\n", "\n", "fig.show()\n"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\booth_reports\\Lib\\site-packages\\sklearn\\feature_selection\\_univariate_selection.py:111: UserWarning:\n", "\n", "Features [886 896 914 916 918 920 930 938 940 942 946 948 950] are constant.\n", "\n", "c:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\booth_reports\\Lib\\site-packages\\sklearn\\feature_selection\\_univariate_selection.py:112: RuntimeWarning:\n", "\n", "invalid value encountered in divide\n", "\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Accuracy: 0.7317\n", "Confusion Matrix:\n", "[[ 3  7]\n", " [ 4 27]]\n", "Classification Report:\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.43      0.30      0.35        10\n", "           1       0.79      0.87      0.83        31\n", "\n", "    accuracy                           0.73        41\n", "   macro avg       0.61      0.59      0.59        41\n", "weighted avg       0.70      0.73      0.71        41\n", "\n"]}], "source": ["import numpy as np\n", "import pandas as pd\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.metrics import accuracy_score, classification_report, confusion_matrix\n", "from sklearn.feature_selection import SelectFromModel\n", "from sklearn.feature_selection import SelectKBest, chi2\n", "\n", "\n", "# Get the data and labels\n", "facial_data.set_labels(df_lfs['label_binary'].rename('Label'),drpna=True)\n", "X = facial_data.get_data()  # Features\n", "y = facial_data.get_labels()  # Labels (Binary: 0 or 1)\n", "\n", "# Convert X to DataFrame and fill NaN values\n", "X = pd.DataFrame(X)\n", "<PERSON>.fillna(0, inplace=True)  # Replace NaN with 0\n", "\n", "# Ensure y is a NumPy array\n", "y = np.array(y)\n", "\n", "# Split data into training (80%) and testing (20%) sets\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y)\n", "\n", "# Use SelectFromModel to keep only important features\n", "selector = SelectKBest(k=100)  # Select top 10 features\n", "X_train_selected = selector.fit_transform(X_train,y_train)\n", "X_test_selected = selector.transform(X_test)\n", "\n", "# Train a new Random Forest with the selected features\n", "clf_final = RandomForestClassifier(n_estimators=300, random_state=22)\n", "clf_final.fit(X_train_selected, y_train)\n", "\n", "# Predictions\n", "y_pred = clf_final.predict(X_test_selected)\n", "\n", "# Evaluate the model\n", "accuracy = accuracy_score(y_test, y_pred)\n", "conf_matrix = confusion_matrix(y_test, y_pred)\n", "class_report = classification_report(y_test, y_pred)\n", "\n", "# Get selected feature names\n", "selected_feature_indices = selector.get_support(indices=True)\n", "selected_features = X.columns[selected_feature_indices]  # Retrieve feature names\n", "\n", "# Display results\n", "print(f\"Accuracy: {accuracy:.4f}\")\n", "print(\"Confusion Matrix:\")\n", "print(conf_matrix)\n", "print(\"Classification Report:\")\n", "print(class_report)\n", "\n", "# Convert results to DataFrame for better visualization\n", "selected_features_df = pd.DataFrame({\"Selected Features\": selected_features})\n", "\n", "\n", "conf_matrix_df = pd.DataFrame(conf_matrix, index=[\"Actual 0\", \"Actual 1\"], columns=[\"Predicted 0\", \"Predicted 1\"])\n", "\n"]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\booth_reports\\Lib\\site-packages\\sklearn\\utils\\validation.py:2732: UserWarning: X has feature names, but SelectFromModel was fitted without feature names\n", "  warnings.warn(\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.feature_selection import SelectFromModel\n", "from sklearn.decomposition import PCA\n", "from sklearn.manifold import TSNE\n", "from sklearn.model_selection import train_test_split\n", "import seaborn as sns\n", "\n", "\n", "# Get the data and labels\n", "X = facial_data.get_data()  # Features\n", "y = facial_data.get_labels()  # Labels (Binary: 0 or 1)\n", "\n", "# Convert X to DataFrame and fill NaN values\n", "X = pd.DataFrame(X)\n", "<PERSON>.fillna(0, inplace=True)  # Replace NaN with 0\n", "\n", "# Ensure y is a NumPy array\n", "y = np.array(y)\n", "\n", "# Split data into training (80%) and testing (20%) sets\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y)\n", "\n", "# First, train a Random Forest to determine feature importance\n", "clf_initial = RandomForestClassifier(n_estimators=100, random_state=42)\n", "clf_initial.fit(X_train, y_train)\n", "\n", "# Use SelectFromModel to keep only important features\n", "selector = SelectFromModel(clf_initial, prefit=True)\n", "X_selected = selector.transform(X)  # Apply selection to full dataset\n", "\n", "# Apply PCA to reduce dimensionality to 50 components\n", "pca = PCA(n_components=50)\n", "X_pca = pca.fit_transform(X_selected)\n", "\n", "# Apply t-SNE to further reduce to 2D for visualization\n", "tsne = TSNE(n_components=2, perplexity=20, random_state=42)\n", "X_tsne = tsne.fit_transform(X_selected)\n", "\n", "# Convert to DataFrame for visualization\n", "df_tsne = pd.DataFrame(X_tsne, columns=[\"TSNE-1\", \"TSNE-2\"])\n", "df_tsne[\"Label\"] = y  # Add class labels\n", "\n", "# Plot t-SNE results\n", "plt.figure(figsize=(10, 6))\n", "sns.scatterplot(data=df_tsne,x=\"TSNE-1\", y=\"TSNE-2\", hue=df_tsne[\"Label\"], palette=\"viridis\", alpha=0.7)\n", "plt.title(\"t-SNE Visualization of Selected Features\")\n", "plt.legend(title=\"Class\")\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from snorkel.labeling.model.label_model import LabelModel\n", "combined_lfs = pd.read_csv(r'\\\\files.ubc.ca\\team\\PPRC\\CAMERA\\Booth_Processed\\facial_expression\\docs\\combined_lfs.csv')\n", "combined_lfs.set_index('ID', inplace=True)"]}, {"cell_type": "code", "execution_count": 57, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Mean Accuracy (Stratified K-Fold CV): 0.5598\n", "Overall Confusion Matrix (summed across folds):\n", " [[49.  5. 23.  4.]\n", " [ 4.  2. 12.  1.]\n", " [25.  4. 60.  1.]\n", " [ 3.  1.  6.  2.]]\n"]}], "source": ["from sklearn.model_selection import StratifiedKFold\n", "from sklearn.feature_selection import SelectKBest, f_classif\n", "from sklearn.metrics import accuracy_score, confusion_matrix\n", "import numpy as np\n", "import pandas as pd\n", "from sklearn.feature_selection import VarianceThreshold\n", "from imblearn.combine import SMOTETomek\n", "from xgboost import XGBClassifier\n", "from sklearn.decomposition import PCA\n", "\n", "# Get the data and labels\n", "facial_data.set_labels(combined_lfs['Prediction'].rename('Label'),drpna=True)\n", "X = facial_data.get_data()  # Features\n", "y = facial_data.get_labels().astype(int)  # Labels (Multi-class: 0, 1, 2, 3)\n", "X = X[y>=0]\n", "y = y[y>=0]\n", "y[y>=1]=1\n", "y= y.astype(int)\n", "\n", "# Convert X to DataFrame and fill NaN values\n", "X = pd.DataFrame(X)\n", "<PERSON>.fillna(0, inplace=True)  # Replace NaN with 0\n", "\n", "# Prepare y_combined (assuming y is a pandas Series)\n", "y_combined = y.copy()\n", "y_combined[y_combined == 4] = 3  # Merging class 4 into class 3 if needed\n", "\n", "# Feature selection (remove or handle constant features if you have any)\n", "vt = VarianceThreshold(threshold=0.0)  # Remove features with 0 variance\n", "X_selected = vt.fit_transform(X)\n", "selector = SelectKBest(score_func=f_classif, k=24)\n", "X_selected = selector.fit_transform(X_selected, y_combined)\n", "\n", "\n", "# Initialize the StratifiedKFold cross-validator\n", "# Adjust n_splits as desired (e.g., 5 or 10)\n", "skf = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)\n", "\n", "# Define your classifier\n", "xgb_clf = XGBClassifier(\n", "    objective='multi:softmax',\n", "    num_class=4,\n", "    random_state=42, \n", "    subsample=0.8,\n", "    n_estimators=300, \n", "    max_depth=10, \n", "    learning_rate=0.05,\n", "    colsample_bytree=1.0,\n", ")\n", "# Arrays to store results\n", "accuracies = []\n", "cm = np.zeros((4, 4))  # Confusion matrix for 4 classes\n", "\n", "for train_index, test_index in skf.split(X_selected, y_combined):\n", "    X_train, X_test = X_selected[train_index], X_selected[test_index]\n", "    y_train, y_test = y_combined.iloc[train_index], y_combined.iloc[test_index]\n", "    \n", "    # Oversample/undersample using SMOTETomek\n", "    sm = SMOTETomek(random_state=42,sampling_strategy={0:700,1:500,2:700,3:500})\n", "    X_train_res, y_train_res = sm.fit_resample(X_train, y_train)\n", "    \n", "    # Train the XGBoost classifier\n", "    xgb_clf.fit(X_train_res, y_train_res)\n", "    \n", "    # Predict on the test set\n", "    y_pred = xgb_clf.predict(X_test)\n", "    \n", "    # Calculate accuracy\n", "    accuracy = accuracy_score(y_test, y_pred)\n", "    accuracies.append(accuracy)\n", "    \n", "    # Update the confusion matrix (summing across folds)\n", "    for true_label, pred_label in zip(y_test, y_pred):\n", "        cm[true_label, pred_label] += 1\n", "\n", "mean_accuracy = np.mean(accuracies)\n", "\n", "print(f\"Mean Accuracy (Stratified K-Fold CV): {mean_accuracy:.4f}\")\n", "print(\"Overall Confusion Matrix (summed across folds):\\n\", cm)\n"]}, {"cell_type": "code", "execution_count": 58, "metadata": {}, "outputs": [{"data": {"image/png": "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******************************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", "text/plain": ["<Figure size 800x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#plot CM\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "\n", "# Plot the confusion matrix\n", "plt.figure(figsize=(8, 6))\n", "sns.heatmap(cm, annot=True, fmt='g', cmap='Blues')\n", "plt.xlabel('Predicted')\n", "plt.ylabel('Actual')\n", "plt.title('Confusion Matrix (Stratified K-Fold CV)')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["amplitude_AU01_c\n", "amplitude_AU01_r\n", "amplitude_AU02_c\n", "amplitude_AU02_r\n", "amplitude_AU04_c\n", "amplitude_AU04_r\n", "amplitude_AU05_c\n", "amplitude_AU05_r\n", "amplitude_AU06_c\n", "amplitude_AU06_r\n", "amplitude_AU07_c\n", "amplitude_AU07_r\n", "amplitude_AU09_c\n", "amplitude_AU09_r\n", "amplitude_AU10_c\n", "amplitude_AU10_r\n", "amplitude_AU12_c\n", "amplitude_AU12_r\n", "amplitude_AU14_c\n", "amplitude_AU14_r\n", "amplitude_AU15_c\n", "amplitude_AU15_r\n", "amplitude_AU17_c\n", "amplitude_AU17_r\n", "amplitude_AU20_c\n", "amplitude_AU20_r\n", "amplitude_AU23_c\n", "amplitude_AU23_r\n", "amplitude_AU25_c\n", "amplitude_AU25_r\n", "amplitude_AU26_c\n", "amplitude_AU26_r\n", "amplitude_AU45_c\n", "amplitude_AU45_r\n", "catch22_CO_Embed2_Dist_tau_d_expfit_meandiff_AU01_c\n", "catch22_CO_Embed2_Dist_tau_d_expfit_meandiff_AU01_r\n", "catch22_CO_Embed2_Dist_tau_d_expfit_meandiff_AU02_c\n", "catch22_CO_Embed2_Dist_tau_d_expfit_meandiff_AU02_r\n", "catch22_CO_Embed2_Dist_tau_d_expfit_meandiff_AU04_c\n", "catch22_CO_Embed2_Dist_tau_d_expfit_meandiff_AU04_r\n", "catch22_CO_Embed2_Dist_tau_d_expfit_meandiff_AU05_c\n", "catch22_CO_Embed2_Dist_tau_d_expfit_meandiff_AU05_r\n", "catch22_CO_Embed2_Dist_tau_d_expfit_meandiff_AU06_c\n", "catch22_CO_Embed2_Dist_tau_d_expfit_meandiff_AU06_r\n", "catch22_CO_Embed2_Dist_tau_d_expfit_meandiff_AU07_c\n", "catch22_CO_Embed2_Dist_tau_d_expfit_meandiff_AU07_r\n", "catch22_CO_Embed2_Dist_tau_d_expfit_meandiff_AU09_c\n", "catch22_CO_Embed2_Dist_tau_d_expfit_meandiff_AU09_r\n", "catch22_CO_Embed2_Dist_tau_d_expfit_meandiff_AU10_c\n", "catch22_CO_Embed2_Dist_tau_d_expfit_meandiff_AU10_r\n", "catch22_CO_Embed2_Dist_tau_d_expfit_meandiff_AU12_c\n", "catch22_CO_Embed2_Dist_tau_d_expfit_meandiff_AU12_r\n", "catch22_CO_Embed2_Dist_tau_d_expfit_meandiff_AU14_c\n", "catch22_CO_Embed2_Dist_tau_d_expfit_meandiff_AU14_r\n", "catch22_CO_Embed2_Dist_tau_d_expfit_meandiff_AU15_c\n", "catch22_CO_Embed2_Dist_tau_d_expfit_meandiff_AU15_r\n", "catch22_CO_Embed2_Dist_tau_d_expfit_meandiff_AU17_c\n", "catch22_CO_Embed2_Dist_tau_d_expfit_meandiff_AU17_r\n", "catch22_CO_Embed2_Dist_tau_d_expfit_meandiff_AU20_c\n", "catch22_CO_Embed2_Dist_tau_d_expfit_meandiff_AU20_r\n", "catch22_CO_Embed2_Dist_tau_d_expfit_meandiff_AU23_c\n", "catch22_CO_Embed2_Dist_tau_d_expfit_meandiff_AU23_r\n", "catch22_CO_Embed2_Dist_tau_d_expfit_meandiff_AU25_c\n", "catch22_CO_Embed2_Dist_tau_d_expfit_meandiff_AU25_r\n", "catch22_CO_Embed2_Dist_tau_d_expfit_meandiff_AU26_c\n", "catch22_CO_Embed2_Dist_tau_d_expfit_meandiff_AU26_r\n", "catch22_CO_Embed2_Dist_tau_d_expfit_meandiff_AU45_c\n", "catch22_CO_Embed2_Dist_tau_d_expfit_meandiff_AU45_r\n", "catch22_CO_FirstMin_ac_AU01_c\n", "catch22_CO_FirstMin_ac_AU01_r\n", "catch22_CO_FirstMin_ac_AU02_c\n", "catch22_CO_FirstMin_ac_AU02_r\n", "catch22_CO_FirstMin_ac_AU04_c\n", "catch22_CO_FirstMin_ac_AU04_r\n", "catch22_CO_FirstMin_ac_AU05_c\n", "catch22_CO_FirstMin_ac_AU05_r\n", "catch22_CO_FirstMin_ac_AU06_c\n", "catch22_CO_FirstMin_ac_AU06_r\n", "catch22_CO_FirstMin_ac_AU07_c\n", "catch22_CO_FirstMin_ac_AU07_r\n", "catch22_CO_FirstMin_ac_AU09_c\n", "catch22_CO_FirstMin_ac_AU09_r\n", "catch22_CO_FirstMin_ac_AU10_c\n", "catch22_CO_FirstMin_ac_AU10_r\n", "catch22_CO_FirstMin_ac_AU12_c\n", "catch22_CO_FirstMin_ac_AU12_r\n", "catch22_CO_FirstMin_ac_AU14_c\n", "catch22_CO_FirstMin_ac_AU14_r\n", "catch22_CO_FirstMin_ac_AU15_c\n", "catch22_CO_FirstMin_ac_AU15_r\n", "catch22_CO_FirstMin_ac_AU17_c\n", "catch22_CO_FirstMin_ac_AU17_r\n", "catch22_CO_FirstMin_ac_AU20_c\n", "catch22_CO_FirstMin_ac_AU20_r\n", "catch22_CO_FirstMin_ac_AU23_c\n", "catch22_CO_FirstMin_ac_AU23_r\n", "catch22_CO_FirstMin_ac_AU25_c\n", "catch22_CO_FirstMin_ac_AU25_r\n", "catch22_CO_FirstMin_ac_AU26_c\n", "catch22_CO_FirstMin_ac_AU26_r\n", "catch22_CO_FirstMin_ac_AU45_c\n", "catch22_CO_FirstMin_ac_AU45_r\n", "catch22_CO_HistogramAMI_even_2_5_AU01_c\n", "catch22_CO_HistogramAMI_even_2_5_AU01_r\n", "catch22_CO_HistogramAMI_even_2_5_AU02_c\n", "catch22_CO_HistogramAMI_even_2_5_AU02_r\n", "catch22_CO_HistogramAMI_even_2_5_AU04_c\n", "catch22_CO_HistogramAMI_even_2_5_AU04_r\n", "catch22_CO_HistogramAMI_even_2_5_AU05_c\n", "catch22_CO_HistogramAMI_even_2_5_AU05_r\n", "catch22_CO_HistogramAMI_even_2_5_AU06_c\n", "catch22_CO_HistogramAMI_even_2_5_AU06_r\n", "catch22_CO_HistogramAMI_even_2_5_AU07_c\n", "catch22_CO_HistogramAMI_even_2_5_AU07_r\n", "catch22_CO_HistogramAMI_even_2_5_AU09_c\n", "catch22_CO_HistogramAMI_even_2_5_AU09_r\n", "catch22_CO_HistogramAMI_even_2_5_AU10_c\n", "catch22_CO_HistogramAMI_even_2_5_AU10_r\n", "catch22_CO_HistogramAMI_even_2_5_AU12_c\n", "catch22_CO_HistogramAMI_even_2_5_AU12_r\n", "catch22_CO_HistogramAMI_even_2_5_AU14_c\n", "catch22_CO_HistogramAMI_even_2_5_AU14_r\n", "catch22_CO_HistogramAMI_even_2_5_AU15_c\n", "catch22_CO_HistogramAMI_even_2_5_AU15_r\n", "catch22_CO_HistogramAMI_even_2_5_AU17_c\n", "catch22_CO_HistogramAMI_even_2_5_AU17_r\n", "catch22_CO_HistogramAMI_even_2_5_AU20_c\n", "catch22_CO_HistogramAMI_even_2_5_AU20_r\n", "catch22_CO_HistogramAMI_even_2_5_AU23_c\n", "catch22_CO_HistogramAMI_even_2_5_AU23_r\n", "catch22_CO_HistogramAMI_even_2_5_AU25_c\n", "catch22_CO_HistogramAMI_even_2_5_AU25_r\n", "catch22_CO_HistogramAMI_even_2_5_AU26_c\n", "catch22_CO_HistogramAMI_even_2_5_AU26_r\n", "catch22_CO_HistogramAMI_even_2_5_AU45_c\n", "catch22_CO_HistogramAMI_even_2_5_AU45_r\n", "catch22_CO_f1ecac_AU01_c\n", "catch22_CO_f1ecac_AU01_r\n", "catch22_CO_f1ecac_AU02_c\n", "catch22_CO_f1ecac_AU02_r\n", "catch22_CO_f1ecac_AU04_c\n", "catch22_CO_f1ecac_AU04_r\n", "catch22_CO_f1ecac_AU05_c\n", "catch22_CO_f1ecac_AU05_r\n", "catch22_CO_f1ecac_AU06_c\n", "catch22_CO_f1ecac_AU06_r\n", "catch22_CO_f1ecac_AU07_c\n", "catch22_CO_f1ecac_AU07_r\n", "catch22_CO_f1ecac_AU09_c\n", "catch22_CO_f1ecac_AU09_r\n", "catch22_CO_f1ecac_AU10_c\n", "catch22_CO_f1ecac_AU10_r\n", "catch22_CO_f1ecac_AU12_c\n", "catch22_CO_f1ecac_AU12_r\n", "catch22_CO_f1ecac_AU14_c\n", "catch22_CO_f1ecac_AU14_r\n", "catch22_CO_f1ecac_AU15_c\n", "catch22_CO_f1ecac_AU15_r\n", "catch22_CO_f1ecac_AU17_c\n", "catch22_CO_f1ecac_AU17_r\n", "catch22_CO_f1ecac_AU20_c\n", "catch22_CO_f1ecac_AU20_r\n", "catch22_CO_f1ecac_AU23_c\n", "catch22_CO_f1ecac_AU23_r\n", "catch22_CO_f1ecac_AU25_c\n", "catch22_CO_f1ecac_AU25_r\n", "catch22_CO_f1ecac_AU26_c\n", "catch22_CO_f1ecac_AU26_r\n", "catch22_CO_f1ecac_AU45_c\n", "catch22_CO_f1ecac_AU45_r\n", "catch22_CO_trev_1_num_AU01_c\n", "catch22_CO_trev_1_num_AU01_r\n", "catch22_CO_trev_1_num_AU02_c\n", "catch22_CO_trev_1_num_AU02_r\n", "catch22_CO_trev_1_num_AU04_c\n", "catch22_CO_trev_1_num_AU04_r\n", "catch22_CO_trev_1_num_AU05_c\n", "catch22_CO_trev_1_num_AU05_r\n", "catch22_CO_trev_1_num_AU06_c\n", "catch22_CO_trev_1_num_AU06_r\n", "catch22_CO_trev_1_num_AU07_c\n", "catch22_CO_trev_1_num_AU07_r\n", "catch22_CO_trev_1_num_AU09_c\n", "catch22_CO_trev_1_num_AU09_r\n", "catch22_CO_trev_1_num_AU10_c\n", "catch22_CO_trev_1_num_AU10_r\n", "catch22_CO_trev_1_num_AU12_c\n", "catch22_CO_trev_1_num_AU12_r\n", "catch22_CO_trev_1_num_AU14_c\n", "catch22_CO_trev_1_num_AU14_r\n", "catch22_CO_trev_1_num_AU15_c\n", "catch22_CO_trev_1_num_AU15_r\n", "catch22_CO_trev_1_num_AU17_c\n", "catch22_CO_trev_1_num_AU17_r\n", "catch22_CO_trev_1_num_AU20_c\n", "catch22_CO_trev_1_num_AU20_r\n", "catch22_CO_trev_1_num_AU23_c\n", "catch22_CO_trev_1_num_AU23_r\n", "catch22_CO_trev_1_num_AU25_c\n", "catch22_CO_trev_1_num_AU25_r\n", "catch22_CO_trev_1_num_AU26_c\n", "catch22_CO_trev_1_num_AU26_r\n", "catch22_CO_trev_1_num_AU45_c\n", "catch22_CO_trev_1_num_AU45_r\n", "catch22_DN_HistogramMode_10_AU01_c\n", "catch22_DN_HistogramMode_10_AU01_r\n", "catch22_DN_HistogramMode_10_AU02_c\n", "catch22_DN_HistogramMode_10_AU02_r\n", "catch22_DN_HistogramMode_10_AU04_c\n", "catch22_DN_HistogramMode_10_AU04_r\n", "catch22_DN_HistogramMode_10_AU05_c\n", "catch22_DN_HistogramMode_10_AU05_r\n", "catch22_DN_HistogramMode_10_AU06_c\n", "catch22_DN_HistogramMode_10_AU06_r\n", "catch22_DN_HistogramMode_10_AU07_c\n", "catch22_DN_HistogramMode_10_AU07_r\n", "catch22_DN_HistogramMode_10_AU09_c\n", "catch22_DN_HistogramMode_10_AU09_r\n", "catch22_DN_HistogramMode_10_AU10_c\n", "catch22_DN_HistogramMode_10_AU10_r\n", "catch22_DN_HistogramMode_10_AU12_c\n", "catch22_DN_HistogramMode_10_AU12_r\n", "catch22_DN_HistogramMode_10_AU14_c\n", "catch22_DN_HistogramMode_10_AU14_r\n", "catch22_DN_HistogramMode_10_AU15_c\n", "catch22_DN_HistogramMode_10_AU15_r\n", "catch22_DN_HistogramMode_10_AU17_c\n", "catch22_DN_HistogramMode_10_AU17_r\n", "catch22_DN_HistogramMode_10_AU20_c\n", "catch22_DN_HistogramMode_10_AU20_r\n", "catch22_DN_HistogramMode_10_AU23_c\n", "catch22_DN_HistogramMode_10_AU23_r\n", "catch22_DN_HistogramMode_10_AU25_c\n", "catch22_DN_HistogramMode_10_AU25_r\n", "catch22_DN_HistogramMode_10_AU26_c\n", "catch22_DN_HistogramMode_10_AU26_r\n", "catch22_DN_HistogramMode_10_AU45_c\n", "catch22_DN_HistogramMode_10_AU45_r\n", "catch22_DN_HistogramMode_5_AU01_c\n", "catch22_DN_HistogramMode_5_AU01_r\n", "catch22_DN_HistogramMode_5_AU02_c\n", "catch22_DN_HistogramMode_5_AU02_r\n", "catch22_DN_HistogramMode_5_AU04_c\n", "catch22_DN_HistogramMode_5_AU04_r\n", "catch22_DN_HistogramMode_5_AU05_c\n", "catch22_DN_HistogramMode_5_AU05_r\n", "catch22_DN_HistogramMode_5_AU06_c\n", "catch22_DN_HistogramMode_5_AU06_r\n", "catch22_DN_HistogramMode_5_AU07_c\n", "catch22_DN_HistogramMode_5_AU07_r\n", "catch22_DN_HistogramMode_5_AU09_c\n", "catch22_DN_HistogramMode_5_AU09_r\n", "catch22_DN_HistogramMode_5_AU10_c\n", "catch22_DN_HistogramMode_5_AU10_r\n", "catch22_DN_HistogramMode_5_AU12_c\n", "catch22_DN_HistogramMode_5_AU12_r\n", "catch22_DN_HistogramMode_5_AU14_c\n", "catch22_DN_HistogramMode_5_AU14_r\n", "catch22_DN_HistogramMode_5_AU15_c\n", "catch22_DN_HistogramMode_5_AU15_r\n", "catch22_DN_HistogramMode_5_AU17_c\n", "catch22_DN_HistogramMode_5_AU17_r\n", "catch22_DN_HistogramMode_5_AU20_c\n", "catch22_DN_HistogramMode_5_AU20_r\n", "catch22_DN_HistogramMode_5_AU23_c\n", "catch22_DN_HistogramMode_5_AU23_r\n", "catch22_DN_HistogramMode_5_AU25_c\n", "catch22_DN_HistogramMode_5_AU25_r\n", "catch22_DN_HistogramMode_5_AU26_c\n", "catch22_DN_HistogramMode_5_AU26_r\n", "catch22_DN_HistogramMode_5_AU45_c\n", "catch22_DN_HistogramMode_5_AU45_r\n", "catch22_DN_OutlierInclude_n_001_mdrmd_AU01_c\n", "catch22_DN_OutlierInclude_n_001_mdrmd_AU01_r\n", "catch22_DN_OutlierInclude_n_001_mdrmd_AU02_c\n", "catch22_DN_OutlierInclude_n_001_mdrmd_AU02_r\n", "catch22_DN_OutlierInclude_n_001_mdrmd_AU04_c\n", "catch22_DN_OutlierInclude_n_001_mdrmd_AU04_r\n", "catch22_DN_OutlierInclude_n_001_mdrmd_AU05_c\n", "catch22_DN_OutlierInclude_n_001_mdrmd_AU05_r\n", "catch22_DN_OutlierInclude_n_001_mdrmd_AU06_c\n", "catch22_DN_OutlierInclude_n_001_mdrmd_AU06_r\n", "catch22_DN_OutlierInclude_n_001_mdrmd_AU07_c\n", "catch22_DN_OutlierInclude_n_001_mdrmd_AU07_r\n", "catch22_DN_OutlierInclude_n_001_mdrmd_AU09_c\n", "catch22_DN_OutlierInclude_n_001_mdrmd_AU09_r\n", "catch22_DN_OutlierInclude_n_001_mdrmd_AU10_c\n", "catch22_DN_OutlierInclude_n_001_mdrmd_AU10_r\n", "catch22_DN_OutlierInclude_n_001_mdrmd_AU12_c\n", "catch22_DN_OutlierInclude_n_001_mdrmd_AU12_r\n", "catch22_DN_OutlierInclude_n_001_mdrmd_AU14_c\n", "catch22_DN_OutlierInclude_n_001_mdrmd_AU14_r\n", "catch22_DN_OutlierInclude_n_001_mdrmd_AU15_c\n", "catch22_DN_OutlierInclude_n_001_mdrmd_AU15_r\n", "catch22_DN_OutlierInclude_n_001_mdrmd_AU17_c\n", "catch22_DN_OutlierInclude_n_001_mdrmd_AU17_r\n", "catch22_DN_OutlierInclude_n_001_mdrmd_AU20_c\n", "catch22_DN_OutlierInclude_n_001_mdrmd_AU20_r\n", "catch22_DN_OutlierInclude_n_001_mdrmd_AU23_c\n", "catch22_DN_OutlierInclude_n_001_mdrmd_AU23_r\n", "catch22_DN_OutlierInclude_n_001_mdrmd_AU25_c\n", "catch22_DN_OutlierInclude_n_001_mdrmd_AU25_r\n", "catch22_DN_OutlierInclude_n_001_mdrmd_AU26_c\n", "catch22_DN_OutlierInclude_n_001_mdrmd_AU26_r\n", "catch22_DN_OutlierInclude_n_001_mdrmd_AU45_c\n", "catch22_DN_OutlierInclude_n_001_mdrmd_AU45_r\n", "catch22_DN_OutlierInclude_p_001_mdrmd_AU01_c\n", "catch22_DN_OutlierInclude_p_001_mdrmd_AU01_r\n", "catch22_DN_OutlierInclude_p_001_mdrmd_AU02_c\n", "catch22_DN_OutlierInclude_p_001_mdrmd_AU02_r\n", "catch22_DN_OutlierInclude_p_001_mdrmd_AU04_c\n", "catch22_DN_OutlierInclude_p_001_mdrmd_AU04_r\n", "catch22_DN_OutlierInclude_p_001_mdrmd_AU05_c\n", "catch22_DN_OutlierInclude_p_001_mdrmd_AU05_r\n", "catch22_DN_OutlierInclude_p_001_mdrmd_AU06_c\n", "catch22_DN_OutlierInclude_p_001_mdrmd_AU06_r\n", "catch22_DN_OutlierInclude_p_001_mdrmd_AU07_c\n", "catch22_DN_OutlierInclude_p_001_mdrmd_AU07_r\n", "catch22_DN_OutlierInclude_p_001_mdrmd_AU09_c\n", "catch22_DN_OutlierInclude_p_001_mdrmd_AU09_r\n", "catch22_DN_OutlierInclude_p_001_mdrmd_AU10_c\n", "catch22_DN_OutlierInclude_p_001_mdrmd_AU10_r\n", "catch22_DN_OutlierInclude_p_001_mdrmd_AU12_c\n", "catch22_DN_OutlierInclude_p_001_mdrmd_AU12_r\n", "catch22_DN_OutlierInclude_p_001_mdrmd_AU14_c\n", "catch22_DN_OutlierInclude_p_001_mdrmd_AU14_r\n", "catch22_DN_OutlierInclude_p_001_mdrmd_AU15_c\n", "catch22_DN_OutlierInclude_p_001_mdrmd_AU15_r\n", "catch22_DN_OutlierInclude_p_001_mdrmd_AU17_c\n", "catch22_DN_OutlierInclude_p_001_mdrmd_AU17_r\n", "catch22_DN_OutlierInclude_p_001_mdrmd_AU20_c\n", "catch22_DN_OutlierInclude_p_001_mdrmd_AU20_r\n", "catch22_DN_OutlierInclude_p_001_mdrmd_AU23_c\n", "catch22_DN_OutlierInclude_p_001_mdrmd_AU23_r\n", "catch22_DN_OutlierInclude_p_001_mdrmd_AU25_c\n", "catch22_DN_OutlierInclude_p_001_mdrmd_AU25_r\n", "catch22_DN_OutlierInclude_p_001_mdrmd_AU26_c\n", "catch22_DN_OutlierInclude_p_001_mdrmd_AU26_r\n", "catch22_DN_OutlierInclude_p_001_mdrmd_AU45_c\n", "catch22_DN_OutlierInclude_p_001_mdrmd_AU45_r\n", "catch22_FC_LocalSimple_mean1_tauresrat_AU01_c\n", "catch22_FC_LocalSimple_mean1_tauresrat_AU01_r\n", "catch22_FC_LocalSimple_mean1_tauresrat_AU02_c\n", "catch22_FC_LocalSimple_mean1_tauresrat_AU02_r\n", "catch22_FC_LocalSimple_mean1_tauresrat_AU04_c\n", "catch22_FC_LocalSimple_mean1_tauresrat_AU04_r\n", "catch22_FC_LocalSimple_mean1_tauresrat_AU05_c\n", "catch22_FC_LocalSimple_mean1_tauresrat_AU05_r\n", "catch22_FC_LocalSimple_mean1_tauresrat_AU06_c\n", "catch22_FC_LocalSimple_mean1_tauresrat_AU06_r\n", "catch22_FC_LocalSimple_mean1_tauresrat_AU07_c\n", "catch22_FC_LocalSimple_mean1_tauresrat_AU07_r\n", "catch22_FC_LocalSimple_mean1_tauresrat_AU09_c\n", "catch22_FC_LocalSimple_mean1_tauresrat_AU09_r\n", "catch22_FC_LocalSimple_mean1_tauresrat_AU10_c\n", "catch22_FC_LocalSimple_mean1_tauresrat_AU10_r\n", "catch22_FC_LocalSimple_mean1_tauresrat_AU12_c\n", "catch22_FC_LocalSimple_mean1_tauresrat_AU12_r\n", "catch22_FC_LocalSimple_mean1_tauresrat_AU14_c\n", "catch22_FC_LocalSimple_mean1_tauresrat_AU14_r\n", "catch22_FC_LocalSimple_mean1_tauresrat_AU15_c\n", "catch22_FC_LocalSimple_mean1_tauresrat_AU15_r\n", "catch22_FC_LocalSimple_mean1_tauresrat_AU17_c\n", "catch22_FC_LocalSimple_mean1_tauresrat_AU17_r\n", "catch22_FC_LocalSimple_mean1_tauresrat_AU20_c\n", "catch22_FC_LocalSimple_mean1_tauresrat_AU20_r\n", "catch22_FC_LocalSimple_mean1_tauresrat_AU23_c\n", "catch22_FC_LocalSimple_mean1_tauresrat_AU23_r\n", "catch22_FC_LocalSimple_mean1_tauresrat_AU25_c\n", "catch22_FC_LocalSimple_mean1_tauresrat_AU25_r\n", "catch22_FC_LocalSimple_mean1_tauresrat_AU26_c\n", "catch22_FC_LocalSimple_mean1_tauresrat_AU26_r\n", "catch22_FC_LocalSimple_mean1_tauresrat_AU45_c\n", "catch22_FC_LocalSimple_mean1_tauresrat_AU45_r\n", "catch22_FC_LocalSimple_mean3_stderr_AU01_c\n", "catch22_FC_LocalSimple_mean3_stderr_AU01_r\n", "catch22_FC_LocalSimple_mean3_stderr_AU02_c\n", "catch22_FC_LocalSimple_mean3_stderr_AU02_r\n", "catch22_FC_LocalSimple_mean3_stderr_AU04_c\n", "catch22_FC_LocalSimple_mean3_stderr_AU04_r\n", "catch22_FC_LocalSimple_mean3_stderr_AU05_c\n", "catch22_FC_LocalSimple_mean3_stderr_AU05_r\n", "catch22_FC_LocalSimple_mean3_stderr_AU06_c\n", "catch22_FC_LocalSimple_mean3_stderr_AU06_r\n", "catch22_FC_LocalSimple_mean3_stderr_AU07_c\n", "catch22_FC_LocalSimple_mean3_stderr_AU07_r\n", "catch22_FC_LocalSimple_mean3_stderr_AU09_c\n", "catch22_FC_LocalSimple_mean3_stderr_AU09_r\n", "catch22_FC_LocalSimple_mean3_stderr_AU10_c\n", "catch22_FC_LocalSimple_mean3_stderr_AU10_r\n", "catch22_FC_LocalSimple_mean3_stderr_AU12_c\n", "catch22_FC_LocalSimple_mean3_stderr_AU12_r\n", "catch22_FC_LocalSimple_mean3_stderr_AU14_c\n", "catch22_FC_LocalSimple_mean3_stderr_AU14_r\n", "catch22_FC_LocalSimple_mean3_stderr_AU15_c\n", "catch22_FC_LocalSimple_mean3_stderr_AU15_r\n", "catch22_FC_LocalSimple_mean3_stderr_AU17_c\n", "catch22_FC_LocalSimple_mean3_stderr_AU17_r\n", "catch22_FC_LocalSimple_mean3_stderr_AU20_c\n", "catch22_FC_LocalSimple_mean3_stderr_AU20_r\n", "catch22_FC_LocalSimple_mean3_stderr_AU23_c\n", "catch22_FC_LocalSimple_mean3_stderr_AU23_r\n", "catch22_FC_LocalSimple_mean3_stderr_AU25_c\n", "catch22_FC_LocalSimple_mean3_stderr_AU25_r\n", "catch22_FC_LocalSimple_mean3_stderr_AU26_c\n", "catch22_FC_LocalSimple_mean3_stderr_AU26_r\n", "catch22_FC_LocalSimple_mean3_stderr_AU45_c\n", "catch22_FC_LocalSimple_mean3_stderr_AU45_r\n", "catch22_IN_AutoMutualInfoStats_40_gaussian_fmmi_AU01_c\n", "catch22_IN_AutoMutualInfoStats_40_gaussian_fmmi_AU01_r\n", "catch22_IN_AutoMutualInfoStats_40_gaussian_fmmi_AU02_c\n", "catch22_IN_AutoMutualInfoStats_40_gaussian_fmmi_AU02_r\n", "catch22_IN_AutoMutualInfoStats_40_gaussian_fmmi_AU04_c\n", "catch22_IN_AutoMutualInfoStats_40_gaussian_fmmi_AU04_r\n", "catch22_IN_AutoMutualInfoStats_40_gaussian_fmmi_AU05_c\n", "catch22_IN_AutoMutualInfoStats_40_gaussian_fmmi_AU05_r\n", "catch22_IN_AutoMutualInfoStats_40_gaussian_fmmi_AU06_c\n", "catch22_IN_AutoMutualInfoStats_40_gaussian_fmmi_AU06_r\n", "catch22_IN_AutoMutualInfoStats_40_gaussian_fmmi_AU07_c\n", "catch22_IN_AutoMutualInfoStats_40_gaussian_fmmi_AU07_r\n", "catch22_IN_AutoMutualInfoStats_40_gaussian_fmmi_AU09_c\n", "catch22_IN_AutoMutualInfoStats_40_gaussian_fmmi_AU09_r\n", "catch22_IN_AutoMutualInfoStats_40_gaussian_fmmi_AU10_c\n", "catch22_IN_AutoMutualInfoStats_40_gaussian_fmmi_AU10_r\n", "catch22_IN_AutoMutualInfoStats_40_gaussian_fmmi_AU12_c\n", "catch22_IN_AutoMutualInfoStats_40_gaussian_fmmi_AU12_r\n", "catch22_IN_AutoMutualInfoStats_40_gaussian_fmmi_AU14_c\n", "catch22_IN_AutoMutualInfoStats_40_gaussian_fmmi_AU14_r\n", "catch22_IN_AutoMutualInfoStats_40_gaussian_fmmi_AU15_c\n", "catch22_IN_AutoMutualInfoStats_40_gaussian_fmmi_AU15_r\n", "catch22_IN_AutoMutualInfoStats_40_gaussian_fmmi_AU17_c\n", "catch22_IN_AutoMutualInfoStats_40_gaussian_fmmi_AU17_r\n", "catch22_IN_AutoMutualInfoStats_40_gaussian_fmmi_AU20_c\n", "catch22_IN_AutoMutualInfoStats_40_gaussian_fmmi_AU20_r\n", "catch22_IN_AutoMutualInfoStats_40_gaussian_fmmi_AU23_c\n", "catch22_IN_AutoMutualInfoStats_40_gaussian_fmmi_AU23_r\n", "catch22_IN_AutoMutualInfoStats_40_gaussian_fmmi_AU25_c\n", "catch22_IN_AutoMutualInfoStats_40_gaussian_fmmi_AU25_r\n", "catch22_IN_AutoMutualInfoStats_40_gaussian_fmmi_AU26_c\n", "catch22_IN_AutoMutualInfoStats_40_gaussian_fmmi_AU26_r\n", "catch22_IN_AutoMutualInfoStats_40_gaussian_fmmi_AU45_c\n", "catch22_IN_AutoMutualInfoStats_40_gaussian_fmmi_AU45_r\n", "catch22_MD_hrv_classic_pnn40_AU01_c\n", "catch22_MD_hrv_classic_pnn40_AU01_r\n", "catch22_MD_hrv_classic_pnn40_AU02_c\n", "catch22_MD_hrv_classic_pnn40_AU02_r\n", "catch22_MD_hrv_classic_pnn40_AU04_c\n", "catch22_MD_hrv_classic_pnn40_AU04_r\n", "catch22_MD_hrv_classic_pnn40_AU05_c\n", "catch22_MD_hrv_classic_pnn40_AU05_r\n", "catch22_MD_hrv_classic_pnn40_AU06_c\n", "catch22_MD_hrv_classic_pnn40_AU06_r\n", "catch22_MD_hrv_classic_pnn40_AU07_c\n", "catch22_MD_hrv_classic_pnn40_AU07_r\n", "catch22_MD_hrv_classic_pnn40_AU09_c\n", "catch22_MD_hrv_classic_pnn40_AU09_r\n", "catch22_MD_hrv_classic_pnn40_AU10_c\n", "catch22_MD_hrv_classic_pnn40_AU10_r\n", "catch22_MD_hrv_classic_pnn40_AU12_c\n", "catch22_MD_hrv_classic_pnn40_AU12_r\n", "catch22_MD_hrv_classic_pnn40_AU14_c\n", "catch22_MD_hrv_classic_pnn40_AU14_r\n", "catch22_MD_hrv_classic_pnn40_AU15_c\n", "catch22_MD_hrv_classic_pnn40_AU15_r\n", "catch22_MD_hrv_classic_pnn40_AU17_c\n", "catch22_MD_hrv_classic_pnn40_AU17_r\n", "catch22_MD_hrv_classic_pnn40_AU20_c\n", "catch22_MD_hrv_classic_pnn40_AU20_r\n", "catch22_MD_hrv_classic_pnn40_AU23_c\n", "catch22_MD_hrv_classic_pnn40_AU23_r\n", "catch22_MD_hrv_classic_pnn40_AU25_c\n", "catch22_MD_hrv_classic_pnn40_AU25_r\n", "catch22_MD_hrv_classic_pnn40_AU26_c\n", "catch22_MD_hrv_classic_pnn40_AU26_r\n", "catch22_MD_hrv_classic_pnn40_AU45_c\n", "catch22_MD_hrv_classic_pnn40_AU45_r\n", "catch22_PD_PeriodicityWang_th0_01_AU01_c\n", "catch22_PD_PeriodicityWang_th0_01_AU01_r\n", "catch22_PD_PeriodicityWang_th0_01_AU02_c\n", "catch22_PD_PeriodicityWang_th0_01_AU02_r\n", "catch22_PD_PeriodicityWang_th0_01_AU04_c\n", "catch22_PD_PeriodicityWang_th0_01_AU04_r\n", "catch22_PD_PeriodicityWang_th0_01_AU05_c\n", "catch22_PD_PeriodicityWang_th0_01_AU05_r\n", "catch22_PD_PeriodicityWang_th0_01_AU06_c\n", "catch22_PD_PeriodicityWang_th0_01_AU06_r\n", "catch22_PD_PeriodicityWang_th0_01_AU07_c\n", "catch22_PD_PeriodicityWang_th0_01_AU07_r\n", "catch22_PD_PeriodicityWang_th0_01_AU09_c\n", "catch22_PD_PeriodicityWang_th0_01_AU09_r\n", "catch22_PD_PeriodicityWang_th0_01_AU10_c\n", "catch22_PD_PeriodicityWang_th0_01_AU10_r\n", "catch22_PD_PeriodicityWang_th0_01_AU12_c\n", "catch22_PD_PeriodicityWang_th0_01_AU12_r\n", "catch22_PD_PeriodicityWang_th0_01_AU14_c\n", "catch22_PD_PeriodicityWang_th0_01_AU14_r\n", "catch22_PD_PeriodicityWang_th0_01_AU15_c\n", "catch22_PD_PeriodicityWang_th0_01_AU15_r\n", "catch22_PD_PeriodicityWang_th0_01_AU17_c\n", "catch22_PD_PeriodicityWang_th0_01_AU17_r\n", "catch22_PD_PeriodicityWang_th0_01_AU20_c\n", "catch22_PD_PeriodicityWang_th0_01_AU20_r\n", "catch22_PD_PeriodicityWang_th0_01_AU23_c\n", "catch22_PD_PeriodicityWang_th0_01_AU23_r\n", "catch22_PD_PeriodicityWang_th0_01_AU25_c\n", "catch22_PD_PeriodicityWang_th0_01_AU25_r\n", "catch22_PD_PeriodicityWang_th0_01_AU26_c\n", "catch22_PD_PeriodicityWang_th0_01_AU26_r\n", "catch22_PD_PeriodicityWang_th0_01_AU45_c\n", "catch22_PD_PeriodicityWang_th0_01_AU45_r\n", "catch22_SB_BinaryStats_diff_longstretch0_AU01_c\n", "catch22_SB_BinaryStats_diff_longstretch0_AU01_r\n", "catch22_SB_BinaryStats_diff_longstretch0_AU02_c\n", "catch22_SB_BinaryStats_diff_longstretch0_AU02_r\n", "catch22_SB_BinaryStats_diff_longstretch0_AU04_c\n", "catch22_SB_BinaryStats_diff_longstretch0_AU04_r\n", "catch22_SB_BinaryStats_diff_longstretch0_AU05_c\n", "catch22_SB_BinaryStats_diff_longstretch0_AU05_r\n", "catch22_SB_BinaryStats_diff_longstretch0_AU06_c\n", "catch22_SB_BinaryStats_diff_longstretch0_AU06_r\n", "catch22_SB_BinaryStats_diff_longstretch0_AU07_c\n", "catch22_SB_BinaryStats_diff_longstretch0_AU07_r\n", "catch22_SB_BinaryStats_diff_longstretch0_AU09_c\n", "catch22_SB_BinaryStats_diff_longstretch0_AU09_r\n", "catch22_SB_BinaryStats_diff_longstretch0_AU10_c\n", "catch22_SB_BinaryStats_diff_longstretch0_AU10_r\n", "catch22_SB_BinaryStats_diff_longstretch0_AU12_c\n", "catch22_SB_BinaryStats_diff_longstretch0_AU12_r\n", "catch22_SB_BinaryStats_diff_longstretch0_AU14_c\n", "catch22_SB_BinaryStats_diff_longstretch0_AU14_r\n", "catch22_SB_BinaryStats_diff_longstretch0_AU15_c\n", "catch22_SB_BinaryStats_diff_longstretch0_AU15_r\n", "catch22_SB_BinaryStats_diff_longstretch0_AU17_c\n", "catch22_SB_BinaryStats_diff_longstretch0_AU17_r\n", "catch22_SB_BinaryStats_diff_longstretch0_AU20_c\n", "catch22_SB_BinaryStats_diff_longstretch0_AU20_r\n", "catch22_SB_BinaryStats_diff_longstretch0_AU23_c\n", "catch22_SB_BinaryStats_diff_longstretch0_AU23_r\n", "catch22_SB_BinaryStats_diff_longstretch0_AU25_c\n", "catch22_SB_BinaryStats_diff_longstretch0_AU25_r\n", "catch22_SB_BinaryStats_diff_longstretch0_AU26_c\n", "catch22_SB_BinaryStats_diff_longstretch0_AU26_r\n", "catch22_SB_BinaryStats_diff_longstretch0_AU45_c\n", "catch22_SB_BinaryStats_diff_longstretch0_AU45_r\n", "catch22_SB_BinaryStats_mean_longstretch1_AU01_c\n", "catch22_SB_BinaryStats_mean_longstretch1_AU01_r\n", "catch22_SB_BinaryStats_mean_longstretch1_AU02_c\n", "catch22_SB_BinaryStats_mean_longstretch1_AU02_r\n", "catch22_SB_BinaryStats_mean_longstretch1_AU04_c\n", "catch22_SB_BinaryStats_mean_longstretch1_AU04_r\n", "catch22_SB_BinaryStats_mean_longstretch1_AU05_c\n", "catch22_SB_BinaryStats_mean_longstretch1_AU05_r\n", "catch22_SB_BinaryStats_mean_longstretch1_AU06_c\n", "catch22_SB_BinaryStats_mean_longstretch1_AU06_r\n", "catch22_SB_BinaryStats_mean_longstretch1_AU07_c\n", "catch22_SB_BinaryStats_mean_longstretch1_AU07_r\n", "catch22_SB_BinaryStats_mean_longstretch1_AU09_c\n", "catch22_SB_BinaryStats_mean_longstretch1_AU09_r\n", "catch22_SB_BinaryStats_mean_longstretch1_AU10_c\n", "catch22_SB_BinaryStats_mean_longstretch1_AU10_r\n", "catch22_SB_BinaryStats_mean_longstretch1_AU12_c\n", "catch22_SB_BinaryStats_mean_longstretch1_AU12_r\n", "catch22_SB_BinaryStats_mean_longstretch1_AU14_c\n", "catch22_SB_BinaryStats_mean_longstretch1_AU14_r\n", "catch22_SB_BinaryStats_mean_longstretch1_AU15_c\n", "catch22_SB_BinaryStats_mean_longstretch1_AU15_r\n", "catch22_SB_BinaryStats_mean_longstretch1_AU17_c\n", "catch22_SB_BinaryStats_mean_longstretch1_AU17_r\n", "catch22_SB_BinaryStats_mean_longstretch1_AU20_c\n", "catch22_SB_BinaryStats_mean_longstretch1_AU20_r\n", "catch22_SB_BinaryStats_mean_longstretch1_AU23_c\n", "catch22_SB_BinaryStats_mean_longstretch1_AU23_r\n", "catch22_SB_BinaryStats_mean_longstretch1_AU25_c\n", "catch22_SB_BinaryStats_mean_longstretch1_AU25_r\n", "catch22_SB_BinaryStats_mean_longstretch1_AU26_c\n", "catch22_SB_BinaryStats_mean_longstretch1_AU26_r\n", "catch22_SB_BinaryStats_mean_longstretch1_AU45_c\n", "catch22_SB_BinaryStats_mean_longstretch1_AU45_r\n", "catch22_SB_MotifThree_quantile_hh_AU01_c\n", "catch22_SB_MotifThree_quantile_hh_AU01_r\n", "catch22_SB_MotifThree_quantile_hh_AU02_c\n", "catch22_SB_MotifThree_quantile_hh_AU02_r\n", "catch22_SB_MotifThree_quantile_hh_AU04_c\n", "catch22_SB_MotifThree_quantile_hh_AU04_r\n", "catch22_SB_MotifThree_quantile_hh_AU05_c\n", "catch22_SB_MotifThree_quantile_hh_AU05_r\n", "catch22_SB_MotifThree_quantile_hh_AU06_c\n", "catch22_SB_MotifThree_quantile_hh_AU06_r\n", "catch22_SB_MotifThree_quantile_hh_AU07_c\n", "catch22_SB_MotifThree_quantile_hh_AU07_r\n", "catch22_SB_MotifThree_quantile_hh_AU09_c\n", "catch22_SB_MotifThree_quantile_hh_AU09_r\n", "catch22_SB_MotifThree_quantile_hh_AU10_c\n", "catch22_SB_MotifThree_quantile_hh_AU10_r\n", "catch22_SB_MotifThree_quantile_hh_AU12_c\n", "catch22_SB_MotifThree_quantile_hh_AU12_r\n", "catch22_SB_MotifThree_quantile_hh_AU14_c\n", "catch22_SB_MotifThree_quantile_hh_AU14_r\n", "catch22_SB_MotifThree_quantile_hh_AU15_c\n", "catch22_SB_MotifThree_quantile_hh_AU15_r\n", "catch22_SB_MotifThree_quantile_hh_AU17_c\n", "catch22_SB_MotifThree_quantile_hh_AU17_r\n", "catch22_SB_MotifThree_quantile_hh_AU20_c\n", "catch22_SB_MotifThree_quantile_hh_AU20_r\n", "catch22_SB_MotifThree_quantile_hh_AU23_c\n", "catch22_SB_MotifThree_quantile_hh_AU23_r\n", "catch22_SB_MotifThree_quantile_hh_AU25_c\n", "catch22_SB_MotifThree_quantile_hh_AU25_r\n", "catch22_SB_MotifThree_quantile_hh_AU26_c\n", "catch22_SB_MotifThree_quantile_hh_AU26_r\n", "catch22_SB_MotifThree_quantile_hh_AU45_c\n", "catch22_SB_MotifThree_quantile_hh_AU45_r\n", "catch22_SB_TransitionMatrix_3ac_sumdiagcov_AU01_c\n", "catch22_SB_TransitionMatrix_3ac_sumdiagcov_AU01_r\n", "catch22_SB_TransitionMatrix_3ac_sumdiagcov_AU02_c\n", "catch22_SB_TransitionMatrix_3ac_sumdiagcov_AU02_r\n", "catch22_SB_TransitionMatrix_3ac_sumdiagcov_AU04_c\n", "catch22_SB_TransitionMatrix_3ac_sumdiagcov_AU04_r\n", "catch22_SB_TransitionMatrix_3ac_sumdiagcov_AU05_c\n", "catch22_SB_TransitionMatrix_3ac_sumdiagcov_AU05_r\n", "catch22_SB_TransitionMatrix_3ac_sumdiagcov_AU06_c\n", "catch22_SB_TransitionMatrix_3ac_sumdiagcov_AU06_r\n", "catch22_SB_TransitionMatrix_3ac_sumdiagcov_AU07_c\n", "catch22_SB_TransitionMatrix_3ac_sumdiagcov_AU07_r\n", "catch22_SB_TransitionMatrix_3ac_sumdiagcov_AU09_c\n", "catch22_SB_TransitionMatrix_3ac_sumdiagcov_AU09_r\n", "catch22_SB_TransitionMatrix_3ac_sumdiagcov_AU10_c\n", "catch22_SB_TransitionMatrix_3ac_sumdiagcov_AU10_r\n", "catch22_SB_TransitionMatrix_3ac_sumdiagcov_AU12_c\n", "catch22_SB_TransitionMatrix_3ac_sumdiagcov_AU12_r\n", "catch22_SB_TransitionMatrix_3ac_sumdiagcov_AU14_c\n", "catch22_SB_TransitionMatrix_3ac_sumdiagcov_AU14_r\n", "catch22_SB_TransitionMatrix_3ac_sumdiagcov_AU15_c\n", "catch22_SB_TransitionMatrix_3ac_sumdiagcov_AU15_r\n", "catch22_SB_TransitionMatrix_3ac_sumdiagcov_AU17_c\n", "catch22_SB_TransitionMatrix_3ac_sumdiagcov_AU17_r\n", "catch22_SB_TransitionMatrix_3ac_sumdiagcov_AU20_c\n", "catch22_SB_TransitionMatrix_3ac_sumdiagcov_AU20_r\n", "catch22_SB_TransitionMatrix_3ac_sumdiagcov_AU23_c\n", "catch22_SB_TransitionMatrix_3ac_sumdiagcov_AU23_r\n", "catch22_SB_TransitionMatrix_3ac_sumdiagcov_AU25_c\n", "catch22_SB_TransitionMatrix_3ac_sumdiagcov_AU25_r\n", "catch22_SB_TransitionMatrix_3ac_sumdiagcov_AU26_c\n", "catch22_SB_TransitionMatrix_3ac_sumdiagcov_AU26_r\n", "catch22_SB_TransitionMatrix_3ac_sumdiagcov_AU45_c\n", "catch22_SB_TransitionMatrix_3ac_sumdiagcov_AU45_r\n", "catch22_SC_FluctAnal_2_dfa_50_1_2_logi_prop_r1_AU01_c\n", "catch22_SC_FluctAnal_2_dfa_50_1_2_logi_prop_r1_AU01_r\n", "catch22_SC_FluctAnal_2_dfa_50_1_2_logi_prop_r1_AU02_c\n", "catch22_SC_FluctAnal_2_dfa_50_1_2_logi_prop_r1_AU02_r\n", "catch22_SC_FluctAnal_2_dfa_50_1_2_logi_prop_r1_AU04_c\n", "catch22_SC_FluctAnal_2_dfa_50_1_2_logi_prop_r1_AU04_r\n", "catch22_SC_FluctAnal_2_dfa_50_1_2_logi_prop_r1_AU05_c\n", "catch22_SC_FluctAnal_2_dfa_50_1_2_logi_prop_r1_AU05_r\n", "catch22_SC_FluctAnal_2_dfa_50_1_2_logi_prop_r1_AU06_c\n", "catch22_SC_FluctAnal_2_dfa_50_1_2_logi_prop_r1_AU06_r\n", "catch22_SC_FluctAnal_2_dfa_50_1_2_logi_prop_r1_AU07_c\n", "catch22_SC_FluctAnal_2_dfa_50_1_2_logi_prop_r1_AU07_r\n", "catch22_SC_FluctAnal_2_dfa_50_1_2_logi_prop_r1_AU09_c\n", "catch22_SC_FluctAnal_2_dfa_50_1_2_logi_prop_r1_AU09_r\n", "catch22_SC_FluctAnal_2_dfa_50_1_2_logi_prop_r1_AU10_c\n", "catch22_SC_FluctAnal_2_dfa_50_1_2_logi_prop_r1_AU10_r\n", "catch22_SC_FluctAnal_2_dfa_50_1_2_logi_prop_r1_AU12_c\n", "catch22_SC_FluctAnal_2_dfa_50_1_2_logi_prop_r1_AU12_r\n", "catch22_SC_FluctAnal_2_dfa_50_1_2_logi_prop_r1_AU14_c\n", "catch22_SC_FluctAnal_2_dfa_50_1_2_logi_prop_r1_AU14_r\n", "catch22_SC_FluctAnal_2_dfa_50_1_2_logi_prop_r1_AU15_c\n", "catch22_SC_FluctAnal_2_dfa_50_1_2_logi_prop_r1_AU15_r\n", "catch22_SC_FluctAnal_2_dfa_50_1_2_logi_prop_r1_AU17_c\n", "catch22_SC_FluctAnal_2_dfa_50_1_2_logi_prop_r1_AU17_r\n", "catch22_SC_FluctAnal_2_dfa_50_1_2_logi_prop_r1_AU20_c\n", "catch22_SC_FluctAnal_2_dfa_50_1_2_logi_prop_r1_AU20_r\n", "catch22_SC_FluctAnal_2_dfa_50_1_2_logi_prop_r1_AU23_c\n", "catch22_SC_FluctAnal_2_dfa_50_1_2_logi_prop_r1_AU23_r\n", "catch22_SC_FluctAnal_2_dfa_50_1_2_logi_prop_r1_AU25_c\n", "catch22_SC_FluctAnal_2_dfa_50_1_2_logi_prop_r1_AU25_r\n", "catch22_SC_FluctAnal_2_dfa_50_1_2_logi_prop_r1_AU26_c\n", "catch22_SC_FluctAnal_2_dfa_50_1_2_logi_prop_r1_AU26_r\n", "catch22_SC_FluctAnal_2_dfa_50_1_2_logi_prop_r1_AU45_c\n", "catch22_SC_FluctAnal_2_dfa_50_1_2_logi_prop_r1_AU45_r\n", "catch22_SC_FluctAnal_2_rsrangefit_50_1_logi_prop_r1_AU01_c\n", "catch22_SC_FluctAnal_2_rsrangefit_50_1_logi_prop_r1_AU01_r\n", "catch22_SC_FluctAnal_2_rsrangefit_50_1_logi_prop_r1_AU02_c\n", "catch22_SC_FluctAnal_2_rsrangefit_50_1_logi_prop_r1_AU02_r\n", "catch22_SC_FluctAnal_2_rsrangefit_50_1_logi_prop_r1_AU04_c\n", "catch22_SC_FluctAnal_2_rsrangefit_50_1_logi_prop_r1_AU04_r\n", "catch22_SC_FluctAnal_2_rsrangefit_50_1_logi_prop_r1_AU05_c\n", "catch22_SC_FluctAnal_2_rsrangefit_50_1_logi_prop_r1_AU05_r\n", "catch22_SC_FluctAnal_2_rsrangefit_50_1_logi_prop_r1_AU06_c\n", "catch22_SC_FluctAnal_2_rsrangefit_50_1_logi_prop_r1_AU06_r\n", "catch22_SC_FluctAnal_2_rsrangefit_50_1_logi_prop_r1_AU07_c\n", "catch22_SC_FluctAnal_2_rsrangefit_50_1_logi_prop_r1_AU07_r\n", "catch22_SC_FluctAnal_2_rsrangefit_50_1_logi_prop_r1_AU09_c\n", "catch22_SC_FluctAnal_2_rsrangefit_50_1_logi_prop_r1_AU09_r\n", "catch22_SC_FluctAnal_2_rsrangefit_50_1_logi_prop_r1_AU10_c\n", "catch22_SC_FluctAnal_2_rsrangefit_50_1_logi_prop_r1_AU10_r\n", "catch22_SC_FluctAnal_2_rsrangefit_50_1_logi_prop_r1_AU12_c\n", "catch22_SC_FluctAnal_2_rsrangefit_50_1_logi_prop_r1_AU12_r\n", "catch22_SC_FluctAnal_2_rsrangefit_50_1_logi_prop_r1_AU14_c\n", "catch22_SC_FluctAnal_2_rsrangefit_50_1_logi_prop_r1_AU14_r\n", "catch22_SC_FluctAnal_2_rsrangefit_50_1_logi_prop_r1_AU15_c\n", "catch22_SC_FluctAnal_2_rsrangefit_50_1_logi_prop_r1_AU15_r\n", "catch22_SC_FluctAnal_2_rsrangefit_50_1_logi_prop_r1_AU17_c\n", "catch22_SC_FluctAnal_2_rsrangefit_50_1_logi_prop_r1_AU17_r\n", "catch22_SC_FluctAnal_2_rsrangefit_50_1_logi_prop_r1_AU20_c\n", "catch22_SC_FluctAnal_2_rsrangefit_50_1_logi_prop_r1_AU20_r\n", "catch22_SC_FluctAnal_2_rsrangefit_50_1_logi_prop_r1_AU23_c\n", "catch22_SC_FluctAnal_2_rsrangefit_50_1_logi_prop_r1_AU23_r\n", "catch22_SC_FluctAnal_2_rsrangefit_50_1_logi_prop_r1_AU25_c\n", "catch22_SC_FluctAnal_2_rsrangefit_50_1_logi_prop_r1_AU25_r\n", "catch22_SC_FluctAnal_2_rsrangefit_50_1_logi_prop_r1_AU26_c\n", "catch22_SC_FluctAnal_2_rsrangefit_50_1_logi_prop_r1_AU26_r\n", "catch22_SC_FluctAnal_2_rsrangefit_50_1_logi_prop_r1_AU45_c\n", "catch22_SC_FluctAnal_2_rsrangefit_50_1_logi_prop_r1_AU45_r\n", "catch22_SP_Summaries_welch_rect_area_5_1_AU01_c\n", "catch22_SP_Summaries_welch_rect_area_5_1_AU01_r\n", "catch22_SP_Summaries_welch_rect_area_5_1_AU02_c\n", "catch22_SP_Summaries_welch_rect_area_5_1_AU02_r\n", "catch22_SP_Summaries_welch_rect_area_5_1_AU04_c\n", "catch22_SP_Summaries_welch_rect_area_5_1_AU04_r\n", "catch22_SP_Summaries_welch_rect_area_5_1_AU05_c\n", "catch22_SP_Summaries_welch_rect_area_5_1_AU05_r\n", "catch22_SP_Summaries_welch_rect_area_5_1_AU06_c\n", "catch22_SP_Summaries_welch_rect_area_5_1_AU06_r\n", "catch22_SP_Summaries_welch_rect_area_5_1_AU07_c\n", "catch22_SP_Summaries_welch_rect_area_5_1_AU07_r\n", "catch22_SP_Summaries_welch_rect_area_5_1_AU09_c\n", "catch22_SP_Summaries_welch_rect_area_5_1_AU09_r\n", "catch22_SP_Summaries_welch_rect_area_5_1_AU10_c\n", "catch22_SP_Summaries_welch_rect_area_5_1_AU10_r\n", "catch22_SP_Summaries_welch_rect_area_5_1_AU12_c\n", "catch22_SP_Summaries_welch_rect_area_5_1_AU12_r\n", "catch22_SP_Summaries_welch_rect_area_5_1_AU14_c\n", "catch22_SP_Summaries_welch_rect_area_5_1_AU14_r\n", "catch22_SP_Summaries_welch_rect_area_5_1_AU15_c\n", "catch22_SP_Summaries_welch_rect_area_5_1_AU15_r\n", "catch22_SP_Summaries_welch_rect_area_5_1_AU17_c\n", "catch22_SP_Summaries_welch_rect_area_5_1_AU17_r\n", "catch22_SP_Summaries_welch_rect_area_5_1_AU20_c\n", "catch22_SP_Summaries_welch_rect_area_5_1_AU20_r\n", "catch22_SP_Summaries_welch_rect_area_5_1_AU23_c\n", "catch22_SP_Summaries_welch_rect_area_5_1_AU23_r\n", "catch22_SP_Summaries_welch_rect_area_5_1_AU25_c\n", "catch22_SP_Summaries_welch_rect_area_5_1_AU25_r\n", "catch22_SP_Summaries_welch_rect_area_5_1_AU26_c\n", "catch22_SP_Summaries_welch_rect_area_5_1_AU26_r\n", "catch22_SP_Summaries_welch_rect_area_5_1_AU45_c\n", "catch22_SP_Summaries_welch_rect_area_5_1_AU45_r\n", "catch22_SP_Summaries_welch_rect_centroid_AU01_c\n", "catch22_SP_Summaries_welch_rect_centroid_AU01_r\n", "catch22_SP_Summaries_welch_rect_centroid_AU02_c\n", "catch22_SP_Summaries_welch_rect_centroid_AU02_r\n", "catch22_SP_Summaries_welch_rect_centroid_AU04_c\n", "catch22_SP_Summaries_welch_rect_centroid_AU04_r\n", "catch22_SP_Summaries_welch_rect_centroid_AU05_c\n", "catch22_SP_Summaries_welch_rect_centroid_AU05_r\n", "catch22_SP_Summaries_welch_rect_centroid_AU06_c\n", "catch22_SP_Summaries_welch_rect_centroid_AU06_r\n", "catch22_SP_Summaries_welch_rect_centroid_AU07_c\n", "catch22_SP_Summaries_welch_rect_centroid_AU07_r\n", "catch22_SP_Summaries_welch_rect_centroid_AU09_c\n", "catch22_SP_Summaries_welch_rect_centroid_AU09_r\n", "catch22_SP_Summaries_welch_rect_centroid_AU10_c\n", "catch22_SP_Summaries_welch_rect_centroid_AU10_r\n", "catch22_SP_Summaries_welch_rect_centroid_AU12_c\n", "catch22_SP_Summaries_welch_rect_centroid_AU12_r\n", "catch22_SP_Summaries_welch_rect_centroid_AU14_c\n", "catch22_SP_Summaries_welch_rect_centroid_AU14_r\n", "catch22_SP_Summaries_welch_rect_centroid_AU15_c\n", "catch22_SP_Summaries_welch_rect_centroid_AU15_r\n", "catch22_SP_Summaries_welch_rect_centroid_AU17_c\n", "catch22_SP_Summaries_welch_rect_centroid_AU17_r\n", "catch22_SP_Summaries_welch_rect_centroid_AU20_c\n", "catch22_SP_Summaries_welch_rect_centroid_AU20_r\n", "catch22_SP_Summaries_welch_rect_centroid_AU23_c\n", "catch22_SP_Summaries_welch_rect_centroid_AU23_r\n", "catch22_SP_Summaries_welch_rect_centroid_AU25_c\n", "catch22_SP_Summaries_welch_rect_centroid_AU25_r\n", "catch22_SP_Summaries_welch_rect_centroid_AU26_c\n", "catch22_SP_Summaries_welch_rect_centroid_AU26_r\n", "catch22_SP_Summaries_welch_rect_centroid_AU45_c\n", "catch22_SP_Summaries_welch_rect_centroid_AU45_r\n", "kurtosis_AU01_c\n", "kurtosis_AU01_r\n", "kurtosis_AU02_c\n", "kurtosis_AU02_r\n", "kurtosis_AU04_c\n", "kurtosis_AU04_r\n", "kurtosis_AU05_c\n", "kurtosis_AU05_r\n", "kurtosis_AU06_c\n", "kurtosis_AU06_r\n", "kurtosis_AU07_c\n", "kurtosis_AU07_r\n", "kurtosis_AU09_c\n", "kurtosis_AU09_r\n", "kurtosis_AU10_c\n", "kurtosis_AU10_r\n", "kurtosis_AU12_c\n", "kurtosis_AU12_r\n", "kurtosis_AU14_c\n", "kurtosis_AU14_r\n", "kurtosis_AU15_c\n", "kurtosis_AU15_r\n", "kurtosis_AU17_c\n", "kurtosis_AU17_r\n", "kurtosis_AU20_c\n", "kurtosis_AU20_r\n", "kurtosis_AU23_c\n", "kurtosis_AU23_r\n", "kurtosis_AU25_c\n", "kurtosis_AU25_r\n", "kurtosis_AU26_c\n", "kurtosis_AU26_r\n", "kurtosis_AU45_c\n", "kurtosis_AU45_r\n", "max_AU01_c\n", "max_AU01_r\n", "max_AU02_c\n", "max_AU02_r\n", "max_AU04_c\n", "max_AU04_r\n", "max_AU05_c\n", "max_AU05_r\n", "max_AU06_c\n", "max_AU06_r\n", "max_AU07_c\n", "max_AU07_r\n", "max_AU09_c\n", "max_AU09_r\n", "max_AU10_c\n", "max_AU10_r\n", "max_AU12_c\n", "max_AU12_r\n", "max_AU14_c\n", "max_AU14_r\n", "max_AU15_c\n", "max_AU15_r\n", "max_AU17_c\n", "max_AU17_r\n", "max_AU20_c\n", "max_AU20_r\n", "max_AU23_c\n", "max_AU23_r\n", "max_AU25_c\n", "max_AU25_r\n", "max_AU26_c\n", "max_AU26_r\n", "max_AU45_c\n", "max_AU45_r\n", "mean_AU01_c\n", "mean_AU01_r\n", "mean_AU02_c\n", "mean_AU02_r\n", "mean_AU04_c\n", "mean_AU04_r\n", "mean_AU05_c\n", "mean_AU05_r\n", "mean_AU06_c\n", "mean_AU06_r\n", "mean_AU07_c\n", "mean_AU07_r\n", "mean_AU09_c\n", "mean_AU09_r\n", "mean_AU10_c\n", "mean_AU10_r\n", "mean_AU12_c\n", "mean_AU12_r\n", "mean_AU14_c\n", "mean_AU14_r\n", "mean_AU15_c\n", "mean_AU15_r\n", "mean_AU17_c\n", "mean_AU17_r\n", "mean_AU20_c\n", "mean_AU20_r\n", "mean_AU23_c\n", "mean_AU23_r\n", "mean_AU25_c\n", "mean_AU25_r\n", "mean_AU26_c\n", "mean_AU26_r\n", "mean_AU45_c\n", "mean_AU45_r\n", "median_AU01_c\n", "median_AU01_r\n", "median_AU02_c\n", "median_AU02_r\n", "median_AU04_c\n", "median_AU04_r\n", "median_AU05_c\n", "median_AU05_r\n", "median_AU06_c\n", "median_AU06_r\n", "median_AU07_c\n", "median_AU07_r\n", "median_AU09_c\n", "median_AU09_r\n", "median_AU10_c\n", "median_AU10_r\n", "median_AU12_c\n", "median_AU12_r\n", "median_AU14_c\n", "median_AU14_r\n", "median_AU15_c\n", "median_AU15_r\n", "median_AU17_c\n", "median_AU17_r\n", "median_AU20_c\n", "median_AU20_r\n", "median_AU23_c\n", "median_AU23_r\n", "median_AU25_c\n", "median_AU25_r\n", "median_AU26_c\n", "median_AU26_r\n", "median_AU45_c\n", "median_AU45_r\n", "min_AU01_c\n", "min_AU01_r\n", "min_AU02_c\n", "min_AU02_r\n", "min_AU04_c\n", "min_AU04_r\n", "min_AU05_c\n", "min_AU05_r\n", "min_AU06_c\n", "min_AU06_r\n", "min_AU07_c\n", "min_AU07_r\n", "min_AU09_c\n", "min_AU09_r\n", "min_AU10_c\n", "min_AU10_r\n", "min_AU12_c\n", "min_AU12_r\n", "min_AU14_c\n", "min_AU14_r\n", "min_AU15_c\n", "min_AU15_r\n", "min_AU17_c\n", "min_AU17_r\n", "min_AU20_c\n", "min_AU20_r\n", "min_AU23_c\n", "min_AU23_r\n", "min_AU25_c\n", "min_AU25_r\n", "min_AU26_c\n", "min_AU26_r\n", "min_AU45_c\n", "min_AU45_r\n", "rms_AU01_c\n", "rms_AU01_r\n", "rms_AU02_c\n", "rms_AU02_r\n", "rms_AU04_c\n", "rms_AU04_r\n", "rms_AU05_c\n", "rms_AU05_r\n", "rms_AU06_c\n", "rms_AU06_r\n", "rms_AU07_c\n", "rms_AU07_r\n", "rms_AU09_c\n", "rms_AU09_r\n", "rms_AU10_c\n", "rms_AU10_r\n", "rms_AU12_c\n", "rms_AU12_r\n", "rms_AU14_c\n", "rms_AU14_r\n", "rms_AU15_c\n", "rms_AU15_r\n", "rms_AU17_c\n", "rms_AU17_r\n", "rms_AU20_c\n", "rms_AU20_r\n", "rms_AU23_c\n", "rms_AU23_r\n", "rms_AU25_c\n", "rms_AU25_r\n", "rms_AU26_c\n", "rms_AU26_r\n", "rms_AU45_c\n", "rms_AU45_r\n", "skewness_AU01_c\n", "skewness_AU01_r\n", "skewness_AU02_c\n", "skewness_AU02_r\n", "skewness_AU04_c\n", "skewness_AU04_r\n", "skewness_AU05_c\n", "skewness_AU05_r\n", "skewness_AU06_c\n", "skewness_AU06_r\n", "skewness_AU07_c\n", "skewness_AU07_r\n", "skewness_AU09_c\n", "skewness_AU09_r\n", "skewness_AU10_c\n", "skewness_AU10_r\n", "skewness_AU12_c\n", "skewness_AU12_r\n", "skewness_AU14_c\n", "skewness_AU14_r\n", "skewness_AU15_c\n", "skewness_AU15_r\n", "skewness_AU17_c\n", "skewness_AU17_r\n", "skewness_AU20_c\n", "skewness_AU20_r\n", "skewness_AU23_c\n", "skewness_AU23_r\n", "skewness_AU25_c\n", "skewness_AU25_r\n", "skewness_AU26_c\n", "skewness_AU26_r\n", "skewness_AU45_c\n", "skewness_AU45_r\n", "standard_deviation_AU01_c\n", "standard_deviation_AU01_r\n", "standard_deviation_AU02_c\n", "standard_deviation_AU02_r\n", "standard_deviation_AU04_c\n", "standard_deviation_AU04_r\n", "standard_deviation_AU05_c\n", "standard_deviation_AU05_r\n", "standard_deviation_AU06_c\n", "standard_deviation_AU06_r\n", "standard_deviation_AU07_c\n", "standard_deviation_AU07_r\n", "standard_deviation_AU09_c\n", "standard_deviation_AU09_r\n", "standard_deviation_AU10_c\n", "standard_deviation_AU10_r\n", "standard_deviation_AU12_c\n", "standard_deviation_AU12_r\n", "standard_deviation_AU14_c\n", "standard_deviation_AU14_r\n", "standard_deviation_AU15_c\n", "standard_deviation_AU15_r\n", "standard_deviation_AU17_c\n", "standard_deviation_AU17_r\n", "standard_deviation_AU20_c\n", "standard_deviation_AU20_r\n", "standard_deviation_AU23_c\n", "standard_deviation_AU23_r\n", "standard_deviation_AU25_c\n", "standard_deviation_AU25_r\n", "standard_deviation_AU26_c\n", "standard_deviation_AU26_r\n", "standard_deviation_AU45_c\n", "standard_deviation_AU45_r\n", "variance_AU01_c\n", "variance_AU01_r\n", "variance_AU02_c\n", "variance_AU02_r\n", "variance_AU04_c\n", "variance_AU04_r\n", "variance_AU05_c\n", "variance_AU05_r\n", "variance_AU06_c\n", "variance_AU06_r\n", "variance_AU07_c\n", "variance_AU07_r\n", "variance_AU09_c\n", "variance_AU09_r\n", "variance_AU10_c\n", "variance_AU10_r\n", "variance_AU12_c\n", "variance_AU12_r\n", "variance_AU14_c\n", "variance_AU14_r\n", "variance_AU15_c\n", "variance_AU15_r\n", "variance_AU17_c\n", "variance_AU17_r\n", "variance_AU20_c\n", "variance_AU20_r\n", "variance_AU23_c\n", "variance_AU23_r\n", "variance_AU25_c\n", "variance_AU25_r\n", "variance_AU26_c\n", "variance_AU26_r\n", "variance_AU45_c\n", "variance_AU45_r\n"]}], "source": ["#print all columns names\n", "for col in facial_data.get_data().columns:\n", "    print(col)"]}], "metadata": {"kernelspec": {"display_name": "booth_reports", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.5"}}, "nbformat": 4, "nbformat_minor": 2}