#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to generate prompts for test set users using a template and create a CSV with prompts and image paths.

This script:
1. Reads the merged_output.csv file with user information
2. Reads the test-set-balanced.csv to identify test set users
3. Loads a prompt template
4. Fills the template with user data for each test set user
5. Creates a new CSV with file names, prompts, and image file paths
"""

import pandas as pd
import os
from pathlib import Path
import argparse
from typing import Dict, List, Optional


def load_prompt_template(template_path: str) -> str:
    """
    Load the prompt template from file.
    
    Args:
        template_path: Path to the prompt template file
        
    Returns:
        Template string
    """
    try:
        with open(template_path, 'r', encoding='utf-8') as file:
            template = file.read()
        return template
    except FileNotFoundError:
        print(f"Template file not found: {template_path}")
        raise
    except Exception as e:
        print(f"Error reading template file: {e}")
        raise


def fill_template(template: str, row: pd.Series) -> str:
    """
    Fill the template with data from a row.
    
    Args:
        template: Template string with placeholders
        row: Pandas Series containing the data
        
    Returns:
        Filled template string
    """
    try:
        # Convert row to dictionary and format template
        return template.format(**row.to_dict())
    except KeyError as e:
        print(f"Missing key in template: {e}")
        print(f"Available keys: {list(row.index)}")
        raise
    except Exception as e:
        print(f"Error filling template: {e}")
        raise


def load_test_set_users(test_set_path: str) -> set:
    """
    Load the test set user IDs from CSV file.
    
    Args:
        test_set_path: Path to the test set CSV file
        
    Returns:
        Set of test set user IDs or file names
    """
    try:
        test_df = pd.read_csv(test_set_path)
        
        # Try different possible column names for the identifier
        possible_id_columns = ['ID', 'id', 'file_name', 'filename', 'user_id', 'File', 'file']
        
        id_column = None
        for col in possible_id_columns:
            if col in test_df.columns:
                id_column = col
                break
        
        if id_column is None:
            print(f"Available columns in test set file: {list(test_df.columns)}")
            raise ValueError("Could not find ID column in test set file")
        
        test_set = set(test_df[id_column].astype(str))
        print(f"Loaded {len(test_set)} test set entries from column '{id_column}'")
        return test_set
        
    except Exception as e:
        print(f"Error loading test set file: {e}")
        raise


def generate_image_path(file_id: str, base_image_path: str = "") -> str:
    """
    Generate the image file path based on the file ID.
    
    Args:
        file_id: File identifier (e.g., "user_id_date_hand")
        base_image_path: Base path for images
        
    Returns:
        Full path to the image file
    """
    try:
        # Parse the file ID to extract components
        parts = file_id.split("_")
        if len(parts) >= 3:
            user_id = parts[0]
            date = parts[1]
            hand = parts[2]
            
            # Generate image filename
            image_filename = f"{user_id}_{date}_distance_full_chart_{hand}.png"
            
            if base_image_path:
                # If base path provided, construct full path
                image_path = os.path.join(base_image_path, user_id, 'finger_tapping', 'images', image_filename)
            else:
                # Just return the filename
                image_path = image_filename
                
            return image_path
        else:
            # If parsing fails, use a default pattern
            return f"{file_id}_distance_full_chart.png"
            
    except Exception as e:
        print(f"Error generating image path for {file_id}: {e}")
        return f"{file_id}_distance_full_chart.png"


def process_users_and_generate_prompts(
    merged_output_path: str,
    test_set_path: str,
    template_path: str,
    output_csv_path: str,
    base_image_path: str = ""
) -> None:
    """
    Main processing function to generate prompts for test set users.
    
    Args:
        merged_output_path: Path to merged_output.csv
        test_set_path: Path to test-set-balanced.csv
        template_path: Path to prompt template file
        output_csv_path: Path for output CSV file
        base_image_path: Base path for image files
    """
    
    # Load data
    print("Loading merged output data...")
    merged_df = pd.read_csv(merged_output_path)
    print(f"Loaded {len(merged_df)} records from merged output")
    
    print("Loading test set...")
    test_set = load_test_set_users(test_set_path)
    
    print("Loading prompt template...")
    template = load_prompt_template(template_path)
    
    # Filter for test set users
    # Try to match on different possible ID columns
    possible_id_columns = ['ID', 'id', 'file_name', 'filename', 'user_id', 'File', 'file']
    
    id_column = None
    for col in possible_id_columns:
        if col in merged_df.columns:
            id_column = col
            break
    
    if id_column is None:
        print(f"Available columns in merged output: {list(merged_df.columns)}")
        raise ValueError("Could not find ID column in merged output file")
    
    print(f"Using '{id_column}' column for matching")
    
    # Filter for test set
    test_mask = merged_df[id_column].astype(str).isin(test_set)
    test_df = merged_df[test_mask].copy()
    
    print(f"Found {len(test_df)} test set records in merged output")
    
    if len(test_df) == 0:
        print("Warning: No test set records found in merged output!")
        print("Sample test set IDs:", list(test_set)[:5])
        print("Sample merged output IDs:", list(merged_df[id_column].astype(str))[:5])
    
    # Generate prompts and image paths
    results = []
    
    for index, row in test_df.iterrows():
        try:
            file_id = str(row[id_column])
            
            # Fill template with row data
            filled_prompt = fill_template(template, row)
            
            # Generate image path
            image_path = generate_image_path(file_id, base_image_path)
            
            # Add to results
            results.append({
                'file_name': file_id,
                'prompt': filled_prompt,
                'image_file': image_path
            })
            
            print(f"Processed: {file_id}")
            
        except Exception as e:
            print(f"Error processing row {index}: {e}")
            continue
    
    # Create output DataFrame and save
    output_df = pd.DataFrame(results)
    output_df.to_csv(output_csv_path, index=False,encoding='utf-8-sig')
    
    print(f"\nGenerated prompts for {len(results)} test set users")
    print(f"Output saved to: {output_csv_path}")
    
    # Show sample of results
    if len(results) > 0:
        print(f"\nSample output:")
        print(f"File name: {results[0]['file_name']}")
        print(f"Image file: {results[0]['image_file']}")
        print(f"Prompt preview: {results[0]['prompt'][:200]}...")


def main():
    """Main function to handle command line arguments."""
    parser = argparse.ArgumentParser(
        description="Generate prompts for test set users using template"
    )
    
    parser.add_argument(
        "--merged-output",
        default=r"G:\My Drive\Datasets\XAI\merged_output.csv",
        help="Path to merged_output.csv file"
    )
    
    parser.add_argument(
        "--test-set",
        default=r"G:\My Drive\Datasets\XAI\test-set-balanced.csv",
        help="Path to test-set-balanced.csv file"
    )
    
    parser.add_argument(
        "--template",
        default=r"D:\Codes\Python\ParkinsonAssessment\report_generation\Foundation_models\report_prompt.txt",
        help="Path to prompt template file"
    )
    
    parser.add_argument(
        "--output",
        default=r"G:\My Drive\Datasets\XAI\test_set_prompts.csv",
        help="Path for output CSV file"
    )
    
    parser.add_argument(
        "--image-base-path",
        default=r"G:\My Drive\Datasets\XAI\images\full-chart",
        help="Base path for image files"
    )
    
    args = parser.parse_args()
    
    # Process and generate prompts
    process_users_and_generate_prompts(
        args.merged_output,
        args.test_set,
        args.template,
        args.output,
        args.image_base_path
    )


if __name__ == "__main__":
    main()

