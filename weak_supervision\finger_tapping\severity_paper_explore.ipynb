import os
import pandas as pd

# Function to calculate hand width and height based on x and y landmarks
def calculate_hand_dimensions(landmarks):
    # Separate the x and y coordinates
    x_coords = [eval(landmark)[0] for landmark in landmarks]
    y_coords = [eval(landmark)[1] for landmark in landmarks]
    
    # Calculate width (max_x - min_x) and height (max_y - min_y)
    hand_width = max(x_coords) - min(x_coords)
    hand_height = max(y_coords) - min(y_coords)
    
    return hand_width, hand_height

# Define the directory paths
input_directory = r'D:\Datasets\Parkinson\Finger Tapping Data\Finger Tapping\output'  # Replace with the actual input directory path
output_directory = 'D:\Datasets\Parkinson\Finger Tapping Data\pose'  # Replace with the actual output directory path

# Ensure the output directory exists
os.makedirs(output_directory, exist_ok=True)

# Process files that end with 'out_original.csv'
for filename in os.listdir(input_directory):
    if filename.endswith('out_original.csv'):
        input_file = os.path.join(input_directory, filename)
        
        # Load the CSV file
        df = pd.read_csv(input_file)
        
        # Create a list to store the reformatted rows
        frame_results = []

        # Process each frame in the original CSV
        for index, row in df.iterrows():
            frame_number = row.values[0]  # Extract frame_number from the row
            hand_id = 0  # Add 0 for hand_id
            hand_label = 'left'  # Set hand_label to 'left'

            # Extract landmarks from the row
            landmarks = row.values[1:].tolist()
            
            # Calculate hand width and height
            hand_width, hand_height = calculate_hand_dimensions(landmarks)


            # Split landmarks into x, y, z columns
            new_row = [frame_number, hand_id, hand_label, hand_width, hand_height]  # Start with frame_number, hand_id, hand_label
            for landmark in landmarks:
                x, y, z = eval(landmark)  # Convert the string "(x, y, z)" into tuple
                new_row.extend([x, y, z])  # Add x, y, z to the new row

            frame_results.append(new_row)

        # Define new column names
        columns = ['frame_number', 'hand_id', 'hand_label', 'hand_width', 'hand_height']
        for i in range(21):
            columns.extend([f'x_{i}', f'y_{i}', f'z_{i}'])

        # Create DataFrame for the new format
        df_new = pd.DataFrame(frame_results, columns=columns)

        # Save the reformatted DataFrame to the output directory
        output_file = os.path.join(output_directory, filename)
        df_new.to_csv(output_file, index=False)

        print(f"Processed {filename} and saved to {output_file}")




import pandas as pd
severity_ratings_df = pd.read_csv(r'D:\Datasets\Parkinson\Finger Tapping Data\severity_dataset_ratings.csv')
severity_ratings_df['filename'] = severity_ratings_df['filename'].apply(lambda x: x.split('-task2')[0])
severity_ratings_df['filename']



features_df = pd.read_csv(r"D:\Datasets\Parkinson\Finger Tapping Data\finger_tapping\left\csvs\left_features.csv")
features_df['filename'] = features_df['file_name'].apply(lambda x: x.split('-task2')[0][1:])
features_df['filename']

#combine the two dataframes
features_df = features_df.merge(severity_ratings_df, on='filename', how='inner')
#print severity ratings df filename column which are not in features df

severity_ratings_df[~severity_ratings_df['filename'].isin(features_df['filename'])]['filename']
#run again features and distances o zero difference
features_df['filename']

dd =severity_ratings_df[severity_ratings_df['filename'] == 'NIHYM875FLXFF']