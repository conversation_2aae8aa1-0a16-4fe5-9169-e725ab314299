import os
from copy_videos import copy_videos
from config import BASE_RAW_DIRECTORY, BASE_PROCESSED_DIRECTORY

emotions = ['disgust', 'angry', 'happy', 'sad']
tasks = ['text', 'imitation']

parent_task = 'facial_expression'
for task in tasks:
    for emotion in emotions:
        dest_directory = os.path.join(BASE_PROCESSED_DIRECTORY, f'{parent_task}\\{emotion}\\{task}\\videos')
        if not os.path.exists(dest_directory):
            os.makedirs(dest_directory)
        copy_videos(BASE_RAW_DIRECTORY, dest_directory, task, emotion, parent_task)

emotion = 'basic_facial'
dest_directory = os.path.join(BASE_PROCESSED_DIRECTORY, f'{parent_task}\\{emotion}\\videos')
if not os.path.exists(dest_directory):
    os.makedirs(dest_directory)
copy_videos(BASE_RAW_DIRECTORY, dest_directory, emotion, 'neutral', parent_task)
