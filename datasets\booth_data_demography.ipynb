{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name '__file__' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "\u001b[1;32m~\\AppData\\Local\\Temp/ipykernel_70324/1047166465.py\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[0;32m      2\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0msys\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      3\u001b[0m \u001b[1;31m# Adjust the working directory to the project root\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m----> 4\u001b[1;33m \u001b[0mcurrent_dir\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mos\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mpath\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mdirname\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mos\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mpath\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mabspath\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0m__file__\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m      5\u001b[0m \u001b[0mproject_root\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mos\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mpath\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mjoin\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mcurrent_dir\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;34m'..'\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      6\u001b[0m \u001b[0msys\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mpath\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0minsert\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;36m0\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mproject_root\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;31mNameError\u001b[0m: name '__file__' is not defined"]}], "source": ["import os\n", "import pandas as pd\n", "from config import BASE_RAW_DIRECTORY\n", "\n", "def analyze_videos(src_directory, task, emotion, parent_task, data_summary, unique_users):\n", "    # Iterate over each user's folder in the source directory\n", "    for user_id in os.listdir(src_directory):\n", "        user_path = os.path.join(src_directory, user_id)\n", "        # Check if it's a directory (to handle user folders)\n", "        if os.path.isdir(user_path):\n", "            unique_users.add(user_id)\n", "            # Iterate over each timestamp folder in the user's folder\n", "            for timestamp in os.listdir(user_path):\n", "                timestamp_path = os.path.join(user_path, timestamp)\n", "                # Check if it's a directory (to handle timestamp folders)\n", "                if os.path.isdir(timestamp_path):\n", "                    face_path = os.path.join(timestamp_path, parent_task)\n", "                    if emotion:\n", "                        face_path = os.path.join(face_path, f'{task}')\n", "                    # Check if the target folder exists\n", "                    if os.path.isdir(face_path):\n", "                        # Count all videos in the target folder\n", "                        for video in os.listdir(face_path):\n", "                            video_path = os.path.join(face_path, video)\n", "                            # Check if it's a file (to handle only video files)\n", "                            if os.path.isfile(video_path) and ((emotion is None and task in video) or f'{emotion}' in video) and '.mp4' in video: \n", "                                # Update data summary\n", "                                task_key = f\"{parent_task}_{task if task else 'neutral'}_{emotion if emotion else 'NA'}\"\n", "                                data_summary[task_key]['data_count'] += 1\n", "                                data_summary[task_key]['users'].add(user_id)\n", "\n", "def initialize_data_summary(tasks, emotions, parent_task):\n", "    data_summary = {}\n", "    for task in tasks:\n", "        if emotions:\n", "            for emotion in emotions:\n", "                key = f\"{parent_task}_{task}_{emotion}\"\n", "                data_summary[key] = {'data_count': 0, 'users': set()}\n", "        else:\n", "            key = f\"{parent_task}_{task}_NA\"\n", "            data_summary[key] = {'data_count': 0, 'users': set()}\n", "    return data_summary\n", "\n", "if __name__ == '__main__':\n", "    # Connect to shared drive and analyze videos\n", "    base_directory = BASE_RAW_DIRECTORY\n", "    \n", "    parent_tasks = {\n", "        'facial_expression': {'tasks': ['text', 'imitation'], 'emotions': ['disgust', 'angry', 'happy', 'sad']},\n", "        'finger_tapping': {'tasks': ['left', 'right'], 'emotions': None},\n", "        'hand_movement': {'tasks': ['both_still', 'left_open_close', 'left_up_down', 'right_open_close', 'right_up_down'], 'emotions': None},\n", "    }\n", "\n", "    # Initialize data summary dictionary\n", "    data_summary = {}\n", "    unique_users = set()\n", "\n", "    for parent_task, details in parent_tasks.items():\n", "        data_summary.update(initialize_data_summary(details['tasks'], details['emotions'], parent_task))\n", "\n", "    for parent_task, details in parent_tasks.items():\n", "        tasks = details['tasks']\n", "        emotions = details['emotions']\n", "        for task in tasks:\n", "            if emotions:\n", "                for emotion in emotions:\n", "                    analyze_videos(base_directory, task, emotion, parent_task, data_summary, unique_users)\n", "            else:\n", "                analyze_videos(base_directory, task, None, parent_task, data_summary, unique_users)\n", "\n", "    # # Additional facial expression task\n", "    # parent_task = 'facial_expression'\n", "    # emotion = 'basic_facial'\n", "    # task = 'neutral'\n", "    # analyze_videos(base_directory, emotion, task, parent_task, data_summary, unique_users)\n", "\n", "    # Convert data summary to DataFrame\n", "    data_summary_df = pd.DataFrame([\n", "        {'Task': key, 'Data Count': value['data_count'], 'Unique Users': len(value['users'])}\n", "        for key, value in data_summary.items()\n", "    ])\n", "\n", "data_summary_df\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Healthy Control : 49\n", "People with PD : 146\n", "Total Unique Users : 195\n"]}], "source": ["#Count number of healthy unique Ids that start with 07\n", "count = 0\n", "for user in unique_users:\n", "    if user.startswith('07'):\n", "        count += 1\n", "\n", "#remove not number ids\n", "unique_users = [user for user in unique_users if user.isnumeric()]\n", "        \n", "print(f'Healthy Control : {count}')\n", "print(f'People with PD : {len(unique_users) - count}')\n", "print(f'Total Unique Users : {len(unique_users)}')"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'07488', '24798', '36407', '36532', '24352', '24860', '07452', '07928', '18372', '35816', '07610', '16883', '26407', '34965', '39712', 'HealthyControl', '07288', '07415', '29570', '07417', '07762', '16219', '35747', '18198', '07571', '16827', '28225', '07776', '26546', '31100', '07814', '33166', '07629', '38141', '07795', '19091', '33151', '30148', '31182', '36220', '38050', '33164', '35246', '07709', '07652', '07681', '36581', '17000', '07855', '18317', '28715', '07566', '07152', '28350', '29157', '07735', '14555', '38210', '17200', '17599', '07755', '23160', '30893', '29377', '28641', '07225', '36297', '07819', '25439', '21696', '39685', '34914', '40046', '38073', '28637', '31231', '07536', '28408', '07771', '34142', '33927', '07628', '22224', '07888', '25352', '25793', '07634', '07580', '38100', '30593', '07587', '28411', '30961', '35154', '29880', '07794', '15813', '32853', '38902', '32594', '07284', '30982', '26145', '28726', '19015', '33527', '07727', '28630', '30279', '36436', '21401', '31848', '07518', '24622', '17202', '32519', '24757', '23284', '20959', '29079', '31769', 'folder_template', '07597', 'logs', '19124', '07155', '33430', '38215', '07609', '23191', '07677', '07578', '38046', '07987', '31092', '35623', '07900', '32200', '25533', '39528', '38519', '28813', '07182', '24318', '27425', '34492', '24601', '07985', '25957', '31457', '22178', '28321', '25260', '07640', '07822', '17434', '34417', '20715', '32282', '26692', '30414', '31318', '07137', '30104', '40613', '24192', '28731', '17980', '07852', '24889', '10039', '33023', '25934', '39200', '28550', '32541', '31319', '31240', '39274', '38256', '31437', '27423', '07334', '32160', '28615', '24475', '36564', '25779', '30009', '27123', '34509', '36660', 'processed', '07501', '33749', '38255', '15377', '31961', '39686'}\n"]}], "source": ["print(unique_users)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 2}