

from jinja2 import Template
from config import ADMI<PERSON>, DOCTOR, PATIENT
from report_generation.base_report_generation.html_report_builder import HTMLReportBuilder
import pandas as pd
import argparse

class FingerTappingReportBuilder(HTMLReportBuilder):
    def __init__(self, yaml_file, template_file, output_file, user_role):
        self.user_role = user_role
        super().__init__(yaml_file, template_file, output_file)

    def read_references_values(self):
        # Read the CSV file
        references = pd.read_csv(r"\\files.ubc.ca\team\PPRC\Camera\Booth_Results\reports\csvs\references.csv")
        
        # Create a tuple for the reference range and set it as the dictionary value
        references['range'] = list(zip(references['min'], references['max']))
        
        # Drop unnecessary columns and set 'feature' as index, convert to dictionary format
        references = references.set_index('feature')['range'].to_dict()
        
        return references
           
        
    def generate_report(self):
        # Load YAML data and template
        data = self.load_yaml()
        template_content = self.load_template()

        # Create Jinja2 template object
        template = Template(template_content)

        # Convert images to base64
        left_chart_base64 = self.image_to_base64(data['images']['left_distances'])
        right_chart_base64 = self.image_to_base64(data['images']['right_distances'])
        left_percentile_chart_base64 = self.image_to_base64(data['images']['left_percentile'])
        right_percentile_chart_base64 = self.image_to_base64(data['images']['right_percentile'])
        left_min = self.image_to_base64(data['images']['left_min'])
        right_min = self.image_to_base64(data['images']['right_min'])
        left_max = self.image_to_base64(data['images']['left_max'])
        right_max = self.image_to_base64(data['images']['right_max'])
        
        references = self.read_references_values()
        
        # Determine role-specific CSS class
        role_class = (
            "is-admin" if self.user_role == ADMIN else
            "is-patient" if self.user_role == PATIENT else
            "is-doctor" if self.user_role == DOCTOR else ""
        )

        # Render the template with base64 images and YAML data
        rendered_html = template.render(
            left_chart=left_chart_base64,
            left_percentile_chart=left_percentile_chart_base64,
            right_chart=right_chart_base64,
            right_percentile_chart=right_percentile_chart_base64,
            left_min_image = left_min,
            right_min_image = right_min,
            left_max_image = left_max,
            right_max_image = right_max,
            left_features=data['features']['left'],
            right_features=data['features']['right'],
            references = references,
            role_class=role_class
        )

        # Call the base class method to save the report
        super().generate_report(rendered_html)



if __name__ == "__main__":
    # Set up argument parser
    parser = argparse.ArgumentParser(description="Generate a finger tapping report for a specific user.")
    parser.add_argument("user_id", type=str, help="User ID of the report recipient.")
    parser.add_argument("date", type=str, help="Date of the report in YYYYMMDD format.")
    parser.add_argument("role", type=str, choices=["patient", "doctor", "admin"], help="Role of the user (e.g., PATIENT, DOCTOR, ADMIN).")

    # Parse arguments
    args = parser.parse_args()

    # Construct file paths based on arguments
    yaml_file_path = fr"\\files.ubc.ca\team\PPRC\Camera\Booth_Results\reports\users\{args.user_id}\finger_tapping\{args.user_id}_{args.date}.yml"
    template_file_path = r".\report_generation\design\finger_tapping_template.html"
    output_file_path = fr".\report_generation\design\output_report_{args.user_id}_{args.role}.html"

    # Create the report generator instance with user arguments
    generator = FingerTappingReportBuilder(
        yaml_file=yaml_file_path,
        template_file=template_file_path,
        output_file=output_file_path,
        user_role=args.role
    ).generate_report()
