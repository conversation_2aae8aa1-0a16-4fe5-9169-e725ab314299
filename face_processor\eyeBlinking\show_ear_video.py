import cv2
import dlib
import numpy as np
from scipy.spatial import distance
import matplotlib.pyplot as plt
from matplotlib.animation import FuncAnimation

# Load the shape predictor model
predictor_path = './libs/OpenFace_2.2.0/shape_predictor_68_face_landmarks.dat'
detector = dlib.get_frontal_face_detector()
predictor = dlib.shape_predictor(predictor_path)

# Constants
EYE_AR_THRESH = 0.8
EYE_AR_CONSEC_FRAMES = 4
MOVING_AVERAGE_WINDOW = 6

# Function to compute Eye Aspect Ratio (EAR)
def eye_aspect_ratio(eye):
    A = distance.euclidean(eye[1], eye[5])
    B = distance.euclidean(eye[2], eye[4])
    C = distance.euclidean(eye[0], eye[3])
    return (A + B) / (2.0 * C)

# Function to convert dlib shape to numpy array
def shape_to_np(shape, dtype="int"):
    coords = np.zeros((68, 2), dtype=dtype)
    for i in range(0, 68):
        coords[i] = (shape.part(i).x, shape.part(i).y)
    return coords

# Video capture
video_path = r'\\files.ubc.ca\team\PPRC\Camera\Booth_Processed\finger_tapping\right\videos\07610_20231205_right_finger_tapping.mp4'
cap = cv2.VideoCapture(video_path)

# Create plots for EAR
fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(10, 6))
x_data, y_data_left, y_data_right = [], [], []
line_left, = ax1.plot([], [], label='Left EAR')
line_right, = ax1.plot([], [], label='Right EAR')

ax1.set_xlim(0, 1)
ax1.set_ylim(0.0, 1.0)
ax1.set_title('Eye Aspect Ratio (EAR) Over Time')
ax1.set_xlabel('Time (frames)')
ax1.set_ylabel('EAR')
ax1.legend()

# Initialize video window
cv2.namedWindow('Frame', cv2.WINDOW_NORMAL)

# Update function for the animation
def update(frame_num):
    ret, frame = cap.read()
    if not ret:
        return line_left, line_right

    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
    rects = detector(gray, 0)

    for rect in rects:
        shape = predictor(gray, rect)
        shape = shape_to_np(shape)

        left_eye = shape[36:42]
        right_eye = shape[42:48]

        left_ear = eye_aspect_ratio(left_eye)
        right_ear = eye_aspect_ratio(right_eye)

        x_data.append(len(x_data))
        y_data_left.append(left_ear)
        y_data_right.append(right_ear)

        # Draw landmarks on the frame
        for (x, y) in np.concatenate((left_eye, right_eye), axis=0):
            cv2.circle(frame, (x, y), 1, (0, 255, 0), -1)

        # Draw the convex hull for the left and right eye
        left_hull = cv2.convexHull(left_eye)
        right_hull = cv2.convexHull(right_eye)
        cv2.drawContours(frame, [left_hull], -1, (0, 255, 0), 1)
        cv2.drawContours(frame, [right_hull], -1, (0, 255, 0), 1)

    if len(x_data) > 200:
        x_data.pop(0)
        y_data_left.pop(0)
        y_data_right.pop(0)

    line_left.set_data(x_data, y_data_left)
    line_right.set_data(x_data, y_data_right)

    ax1.set_xlim(0, len(x_data))
    
    # Show the video frame
    cv2.imshow('Frame', frame)
    if cv2.waitKey(1) & 0xFF == ord('q'):
        cap.release()
        cv2.destroyAllWindows()
        plt.close('all')
        return line_left, line_right

    return line_left, line_right

ani = FuncAnimation(fig, update, interval=20, blit=True, cache_frame_data=False)
plt.show()

# Release the capture and close windows when done
cap.release()
cv2.destroyAllWindows()
