from pathlib import Path
import pandas as pd
from hand_processor.hand_pose import Hand<PERSON>oseExtractor
from hand_processor.finger_tapping_distances import DistanceCalculator  # Import the new DistanceCalculator
from hand_processor.finger_tapping_features import FeatureExtractor
from report_generation.base_report_generation.base_processor import BaseProcessor  # Import the combined FeatureExtractor

class FingerTappingProcessor(BaseProcessor):
    def __init__(self, base_processed_directory):
        """
        Initialize the FingerTappingProcessor with the base directory.
        """
        super().__init__(base_processed_directory)
        self.hand_pose_extractor = HandPoseExtractor(base_processed_directory=base_processed_directory)
        self.distance_calculator = DistanceCalculator(base_processed_directory=base_processed_directory)  # Initialize DistanceCalculator
        self.feature_extractor = FeatureExtractor(base_processed_directory=base_processed_directory)  # Initialize FeatureExtractor

    def process(self, user_id, date, hand, video_path, output_path):
        """
        Process a specific video using the HandPoseExtractor, calculate distances, and extract features.
        """
        print(f"Processing video for: {video_path}")

        if not video_path.exists():
            print(f"Video file not found: {video_path}")
            return

        # Pose extraction
        pose_output_path = self.pose_extraction(video_path,user_id, date, hand)

        # Distance calculations
        # TODO - Update the distance_calculator to use pose df as input
        dis_output_path =self.distance_calculator.calculate_distances(pose_output_path)

        # Feature extraction for all videos in the dataset folder
        self.feature_extraction(dis_output_path,video_path)
        print(f"Process finished for video: {video_path}")


    def pose_extraction(self, video_path,user_id, date, hand, output_path=None):
        """
        Extract hand pose data from a video.
        """
        if output_path is None:
            output_path = self.base_processed_directory / 'finger_tapping' / f'{hand}'/ 'pose' / f'{user_id}_{date}_{video_path.name.replace(".mp4", ".csv")}'

        self.hand_pose_extractor.process_video(video_path, output_path)
        return output_path
        
    def feature_extraction(self, dis_output_path , video_path, recalculate=False):
        """
        Process the dataset folder and calculate features for each file.
        If output_file already exists, only calculate features for rows that are not in the output_file.
        """
        distances = self.distance_calculator.read_distances(dis_output_path)
        features_dir =  Path(dis_output_path).parent.parent / 'features'
        features_dir.mkdir(parents=True, exist_ok=True)
        features_output_path = features_dir / f'{dis_output_path.stem.replace('_distances','')}_features.csv'
        features = self.feature_extractor.extract_features_from_distances(distances,features_output_path)
        
    def cleanup(self):
        """
        Clean up resources after processing.
        """
        self.hand_pose_extractor.close()
