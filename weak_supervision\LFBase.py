import os
import sys
sys.path.insert(0, 'd:\\Codes\\Python\\ParkinsonAssessment')
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
from datasets.load_data import get_validations, load_labels_with_ID, load_emotion_task_ts_data, get_train_classifiers_set, merge_df_with_labels
from helpers.classification_analysis import load_trained_model, perform_analysis_cv, save_trained_model


class LFBaseClass:
    def __init__(self, origin_folder= "D:\\Atefeh\\Datasets\\Parkinson\\booth", predict='Facial_Expression_UPDRS (custom)', model_path = '.\\models\\best_lf_v1.json',frame_rate=6, tasks = ['imitation', 'text'], emotions=['happy', 'disgust', 'sad', 'angry']):
        self.origin_folder = origin_folder
        self.predict = predict
        self.model_path = model_path
        self.tasks = tasks
        self.frame_rate=frame_rate
        self.emotions = emotions

    def prepare_dataset(self):
        pass

    def train_classifier(self,features):
        merged_df = self.prepare_dataset()
        merged_df = get_train_classifiers_set(merged_df)
        y = merged_df[self.predict]
        X = merged_df.filter(regex=features)
        X.fillna(0, inplace=True)
        classifier = RandomForestClassifier(random_state=33)
        scores, mean_score, selected_features = perform_analysis_cv(X, y, classifier=classifier, n_features_to_select=18, feature_selection_method='RFECV')
        classifier.fit(X[selected_features].values, y)
        print(f'Cross-validation scores: {scores}')
        print(f'Mean cross-validation score: {mean_score}')
        return classifier, selected_features
    
    def save_model(self,classifier, selected_features):
        save_trained_model(classifier, selected_features, self.model_path)
    
    def load_model(self):
        return load_trained_model(self.model_path)
    
    def evaluate_classifier(self,classifier=None, selected_features=None ):
        merged_df = self.prepare_dataset()
        merged_df = get_validations(merged_df)
        if(classifier is None):
            classifier, selected_features = self.load_model()
        y = merged_df[self.predict]
        X = merged_df[selected_features]
        X.fillna(0, inplace=True)
        y_pred = classifier.predict(X)
        accuracy = accuracy_score(y, y_pred)
        report = classification_report(y, y_pred)
        cm = confusion_matrix(y, y_pred)
        print(f'Accuracy: {accuracy}')
        print('Classification Report:')
        print(report)
        print('Confusion Matrix:')
        print(cm)
        
    def labeling_function(self, x):
        # Instantiate the lfBase class with specific parameters
        classifier, selected_features = self.load_model()
        features = x[selected_features]  # Assuming 'x' is a dataframe or a similar structure
        prediction = classifier.predict([features])[0]  # Predict and get the first result
        
        return prediction

