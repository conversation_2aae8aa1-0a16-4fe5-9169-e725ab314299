import os
import pandas as pd

def check_user_data_facial_expression(src_directory, tasks, emotions):
    # Create column headers for each task/emotion combination
    columns = [f'{task}/{emotion}' for task in tasks for emotion in emotions]
    # Initialize a DataFrame with an index of user IDs and columns of task/emotion combinations
    data = pd.DataFrame(columns=columns)

    # Iterate over each user's folder in the source directory
    for user_id in os.listdir(src_directory):
        user_path = os.path.join(src_directory, user_id)
        # Initialize user data availability to False
        user_data = {column: False for column in columns}
        if os.path.isdir(user_path):
            for timestamp in os.listdir(user_path):
                timestamp_path = os.path.join(user_path, timestamp)
                for task in tasks:
                    # Construct the path to the specific emotion video within the task directory
                    data_path = os.path.join(timestamp_path, 'facial_expression', task)
                    for emotion in emotions: 
                        if os.path.isdir(data_path):
                            for video in os.listdir(data_path):
                                video_path = os.path.join(data_path, video)
                                # Update the DataFrame if the video file exists
                                if os.path.isfile(video_path) and video_path.endswith(f'{emotion}.mp4'):
                                    user_data[f'{task}/{emotion}'] = True
        # Append the user's data availability to the DataFrame
        data = data.append(pd.Series(user_data, name=user_id))

    return data

def check_user_data_hand_movement(base_directory, tasks):
    # Create column headers for each task
    columns = tasks
    # Initialize a DataFrame with an index of user IDs and columns of tasks
    data = pd.DataFrame(columns=columns)

    # Iterate over each user's folder in the source directory
    for user_id in os.listdir(base_directory):
        user_path = os.path.join(base_directory, user_id)
        # Initialize user data availability to False
        user_data = {column: False for column in columns}
        if os.path.isdir(user_path):
            for timestamp in os.listdir(user_path):
                timestamp_path = os.path.join(user_path, timestamp)
                if not os.path.isdir(timestamp_path):
                    continue
                data_path = os.path.join(timestamp_path, 'eye_movement','okn')
                if not os.path.isdir(data_path):
                    continue
                for task in tasks:
                    for video in os.listdir(data_path):
                        video_path = os.path.join(data_path, video)
                        # Update the DataFrame if the video file exists
                        if os.path.isfile(video_path) and video_path.endswith(f'{task}.mp4'):
                            user_data[f'{task}'] = True
        # Append the user's data availability to the DataFrame
        data = data.append(pd.Series(user_data, name=user_id))

    return data

if __name__ == '__main__':
    base_directory = '\\\\files.ubc.ca\\team\\PPRC\\Camera\\CAMERA Booth Data\\Booth\\'
    emotions = ['angry', 'disgust', 'happy', 'sad']
    tasks = ['imitation', 'text']

    # Check user data and get DataFrame
    #user_data_df = check_user_data_facial_expression(base_directory, tasks, emotions)
    
    #tasks=['both_still','left_open_close','right_open_close','left_up_down','right_up_down']
    #tasks=['left_finger_tapping','right_finger_tapping']
    tasks=['OKN_Video1']
    user_data_df = check_user_data_hand_movement(base_directory, tasks)
    # save in csv
    user_data_df.to_csv('./csv/user_data_availability_eye.csv')
    print(user_data_df)
