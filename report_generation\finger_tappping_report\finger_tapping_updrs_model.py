from joblib import load

from report_generation.base_report_generation.updrs_model import UPDRSModel

class FingerTappingModel(UPDRSModel):
    def __init__(self, classifier_pipeline_filename, regressor_model_filename=None):
        """
        Initialize the UPDRSModel with classifier pipeline and optional regressor model.
        """
        super().__init__(regressor_model_filename , classifier_pipeline_filename)
        self.classifier_pipeline = None
        self.regressor_model = None
        self.load_model_and_pipeline()


    def load_model_and_pipeline(self):
        """
        Load the classifier pipeline and regressor model.
        """
        # Load the classifier pipeline
        if self.pipeline_filename:   
            self.classifier_pipeline = load(self.pipeline_filename)
            print(f"Loaded classifier pipeline from {self.pipeline_filename}")

        # Load the regressor if provided
        if self.model_filename:
            self.regressor_model = load(self.model_filename)
            print(f"Loaded regressor model from {self.model_filename}")
        else:
            self.regressor_model = None
            print("No regressor model provided")

    def predict(self, features):
        """
        First predict the class using the classifier pipeline. 
        If the predicted class is > 1, use the regressor for finer prediction.
        """
        # Predict the class first
        predicted_class = self.classifier_pipeline.predict(features)[0]
        print(f"Predicted class: {predicted_class}")

        # If predicted class is greater than 1, use the regressor for prediction
        if predicted_class > 1 and self.regressor_model:
            regressor_prediction = self.regressor_model.predict(features)[0]
            print(f"Regressor predicted score: {regressor_prediction}")
            return regressor_prediction
        else:
            return predicted_class

