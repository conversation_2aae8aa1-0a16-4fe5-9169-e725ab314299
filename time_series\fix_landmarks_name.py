import os
import pandas as pd
import re

# Specify the directory containing the CSV files
origin_folder = 'D:\\Atefeh\\Datasets\\Parkinson\\booth\\'
directory = f'{origin_folder}/facial_expression/happy/text/landmarks/'

# Iterate over each file in the directory
for filename in os.listdir(directory):
    if filename.endswith('.csv'):
        file_path = os.path.join(directory, filename)
        
        # Read the CSV file
        df = pd.read_csv(file_path)
        
        # Prepare new column names
        new_columns = []
        for col in df.columns:
            # Match columns that are numbers followed by '_x' or '_y'
            match = re.match(r'^(\d+)_(x|y)', col)
            if match:
                # Construct new column name based on the match
                new_col = f'Landmark_{match.group(1)}_{match.group(2)}'
                new_columns.append(new_col)
            else:
                new_columns.append(col)
        
        # Update the DataFrame's columns
        df.columns = new_columns
        
        # Save the modified DataFrame back to CSV
        df.to_csv(file_path, index=False)
        print(f'Updated columns in {filename}')

print('All files have been processed.')