import os
import pandas as pd

# Specify the directory containing the CSV files
directory = "\\\\files.ubc.ca\\team\\PPRC\\Camera\\Booth_Processed\\facial_expression\\happy\\imitation\\landmarks\\"
#directory = "\\\\files.ubc.ca\\team\\PPRC\\Camera\\Booth_Processed\\finger_tapping\\left\\landmarks\\"

# Loop through the files in the directory
for filename in os.listdir(directory):
    if filename.endswith(".csv"):
        # Create the full path to the file
        file_path = os.path.join(directory, filename)
        
        # Load the CSV file into a DataFrame
        df = pd.read_csv(file_path)
        
        # Modify column names if they not start with 'Landmark_' add 'Landmark_'
        df.columns = ['Landmark_' + col if not col.startswith('Landmark_') else col for col in df.columns]
        #set the first column name 'Frame'
        df.columns.values[0] = 'Frame'
        
        # Optionally modify any data that starts with 'Landmark_'
        # Uncomment and modify the following line if needed
        # df.replace(to_replace=r'^Landmark_', value='', regex=True, inplace=True)
        
        # Save the modified DataFrame back to CSV
        df.to_csv(file_path, index=False)
        print(f"Processed and saved {filename}")
