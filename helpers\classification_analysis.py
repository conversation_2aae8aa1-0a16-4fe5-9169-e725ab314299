import json
import sys
import joblib
from matplotlib import pyplot as plt
import numpy as np
from sklearn.metrics import accuracy_score
from sklearn.ensemble import RandomForestClassifier
from sklearn.feature_selection import RFE, RFECV, SelectKBest, f_classif
from sklearn.metrics import classification_report, confusion_matrix
from sklearn.model_selection import StratifiedKFold, cross_val_score, train_test_split


def select_features(X, y, method='RFE', classifier=None, n_features_to_select=8):
    if method == 'RFE':
        if classifier is None:
            classifier = RandomForestClassifier(random_state=42)
        selector = RFE(classifier, n_features_to_select=n_features_to_select)
    elif method == 'SelectKBest':
        selector = SelectKBest(score_func=f_classif, k=n_features_to_select)
    elif method == 'RFECV':
        selector = RFECV(estimator=classifier, step=1, cv=5, min_features_to_select=n_features_to_select)
    else:
        raise ValueError("Unsupported feature selection method")
    if len(X.columns) < n_features_to_select:
        selected_features = X.columns
    else:
        selector = selector.fit(X, y)
        selected_features = X.columns[selector.get_support()]
    return X[selected_features], selected_features

def train_classifier(X_train, y_train, classifier=None):
    if classifier is None:
        classifier = RandomForestClassifier(random_state=42)
    classifier.fit(X_train, y_train)
    return classifier

def evaluate_classifier(classifier, X_test, y_test):
    y_pred = classifier.predict(X_test)
    accuracy = accuracy_score(y_test, y_pred)
    report = classification_report(y_test, y_pred)
    cm = confusion_matrix(y_test, y_pred)
    
    print(f'Accuracy: {accuracy}')
    print('Classification Report:')
    print(report)
    
    plt.figure(figsize=(10, 8))
    sys.heatmap(cm, annot=True, cmap='coolwarm', fmt='g')
    plt.title('Confusion Matrix')
    plt.xlabel('Predicted')
    plt.ylabel('Actual')
    plt.show()
    
    return accuracy, report, cm

def perform_analysis(X, y, classifier=None, feature_selection_method='RFE', n_features_to_select=8, test_size=0.2, random_state=32):
    # Feature selection
    X_selected, selected_features = select_features(X, y, method=feature_selection_method, classifier=classifier, n_features_to_select=n_features_to_select)
    print("Selected Features:", selected_features)
    
    # Split the data into training and testing sets
    X_train, X_test, y_train, y_test = train_test_split(X_selected, y, test_size=test_size, random_state=random_state)
    
    # Train the classifier
    classifier = train_classifier(X_train, y_train, classifier)
    
    # Evaluate the classifier
    accuracy, report, cm = evaluate_classifier(classifier, X_test, y_test)
    
    return accuracy, report, cm, selected_features

def perform_analysis_cv(X, y, classifier=None, feature_selection_method='RFE', n_features_to_select=8, cv=5, random_state=32):
    # Feature selection
    X_selected, selected_features = select_features(X, y, method=feature_selection_method, classifier=RandomForestClassifier(random_state=random_state), n_features_to_select=n_features_to_select)
    print("Selected Features:", selected_features)
    
    # Initialize StratifiedKFold for cross-validation
    skf = StratifiedKFold(n_splits=cv, shuffle=True, random_state=random_state)
    
    # Train and evaluate the classifier using cross-validation
    scores = cross_val_score(classifier, X_selected, y, cv=skf)
    
    return scores, np.mean(scores),selected_features

def save_trained_model(model, feature_names, model_path):
    model_data = {
        'model': model,
        'features': feature_names
    }
    joblib.dump(model_data, model_path)
    print(f'Trained model saved to {model_path}')
    
# Load the model
def load_trained_model(model_path):
    model_data = joblib.load(model_path)
    return model_data['model'], model_data['features']