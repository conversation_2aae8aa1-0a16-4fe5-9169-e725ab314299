import os
import pandas as pd
import os
import sys
# Adjust the working directory to the project root
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.join(current_dir, '..')
project_root = os.path.join(project_root, '..')
sys.path.insert(0, project_root)
os.chdir(project_root)
from datasets.load_data import load_emotion_task_ts_data

class FacialDataset:
    def __init__(self, folder_path, au_set , emotions=['happy','angry','sad','disgust'], df_labels=None,data_type='action_units',task_type = 'imitation',regex_filter='^(?!.*catch22).*'):
        self.folder_path = folder_path
        self.au_set = au_set
        self.emotions=emotions
        self.regex_filter = regex_filter
        self.task_type = task_type
        self.data_type = data_type
        self.data = self._load_data()
        
        
        if df_labels is not None:
            self.set_labels(df_labels)

    def _load_data(self):
        return load_emotion_task_ts_data(self.folder_path, task=self.data_type, emotions=self.emotions, type=self.task_type,au_sets=self.au_set,regex_filter=self.regex_filter)

    def get_data(self):
        return self.data
    
    def get_labels(self):
        return self.labels
    
    def set_labels(self, df_labels, drpna=True):
        combined_df = pd.merge(self.data, df_labels, on='ID') 
        if drpna: 
            combined_df.dropna(subset=['Label'], inplace=True)
        combined_df = combined_df[~combined_df.index.duplicated(keep='first')]
        self.data = combined_df[combined_df.columns.difference(['Label'])]
        self.labels = combined_df['Label']
        
            
            