import dlib
import cv2
import numpy as np

class OpenFace:
    def __init__(self, image):
        # Load the image
        self.face_image = image
        self.gray_face_image = cv2.cvtColor(self.face_image, cv2.COLOR_BGR2GRAY)
        
        # Load the face detector and shape predictor if they aren't loaded
        if not hasattr(OpenFace, 'face_detector'):
            OpenFace.face_detector = dlib.get_frontal_face_detector()

        if not hasattr(OpenFace, 'face_predictor'):
            face_landmark_path = './libs/OpenFace_2.2.0/shape_predictor_68_face_landmarks.dat'
            OpenFace.face_predictor = dlib.shape_predictor(face_landmark_path)

        # Detect face landmarks and crop face
        self.face_rect = self._find_face_rectangle()
        self.landmarks = self._find_landmarks()
        

    # Find face rectangle
    def _find_face_rectangle(self):
        detector = OpenFace.face_detector
        rects = detector(self.gray_face_image, 1)
        return rects[0] 
  

    def _find_landmarks(self):
        rect = OpenFace.face_detector(self.face_image, 1)[0]
        landmarks = OpenFace.face_predictor(self.face_image, rect)
        landmarks_coords = [(p.x, p.y) for p in landmarks.parts()]
        return landmarks_coords
    


