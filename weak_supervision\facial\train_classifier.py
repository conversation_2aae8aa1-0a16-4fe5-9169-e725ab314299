import warnings
warnings.simplefilter(action='ignore', category=FutureWarning)
warnings.simplefilter(action='ignore', category=UserWarning)
warnings.simplefilter(action='ignore', category=RuntimeWarning)

from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor
from sklearn.model_selection import LeaveOneOut
from sklearn.metrics import accuracy_score, mean_absolute_error, mean_squared_error
from sklearn.feature_selection import SelectKBest, f_classif
import numpy as np
import pandas as pd



class TwoStepClassifier:
    def __init__(self, classifier, regressor, threshold):
        self.classifier = classifier
        self.regressor = regressor
    
    def leave_one_out(self, X, y):
        loo = LeaveOneOut()
        y_predicted = pd.Series(index=y.index, dtype=float)
        
        for i, (train_index, test_index) in enumerate(loo.split(X, y)):
            X_train, X_test = X[train_index,:], X[test_index,:]
            y_train, y_test = y.iloc[train_index], y.iloc[test_index]


            y_train_binary, y_test_binary = y_train >= 1, y_test >= 1
            self.classifier.fit(X_train, y_train_binary)
            y_pred_class = self.classifier.predict(X_test)
            
            if y_pred_class[0] >= 1:
                X_train_regressor, y_train_regressor = X_train, y_train
                self.regressor.fit(X_train_regressor, y_train_regressor)
                y_pred_regressor = self.regressor.predict(X_test)
                y_predicted.iloc[test_index[0]] = y_pred_regressor[0]
            else:
                y_predicted.iloc[test_index[0]] = 0
        
        return y_predicted
    
    def predict_loo(self, X, y):
        self.selector = SelectKBest(score_func=f_classif, k=70)
        X = self.selector.fit_transform(X, y)
        predicted = self.leave_one_out(X, y)
        return predicted
    
    def predict(self, X):
        X = self.selector.transform(X)
        y_classifier = self.classifier.predict(X)
        y_reg  = self.regressor.predict(X)
        y_predicted = y_classifier*y_reg
        return y_predicted
         
from xgboost import XGBClassifier, XGBRegressor

from sklearn.model_selection import LeaveOneOut
from sklearn.feature_selection import SelectKBest, f_classif
from xgboost import XGBClassifier, XGBRegressor

class XGBoostTwoStepClassifier:
    def __init__(self, classifier=None, regressor=None, threshold=1):
        self.classifier = classifier if classifier else XGBClassifier(
            objective='multi:softmax',  # or 'multi:softprob'
            num_class=4,  # If you have 5 classes: 0..4
            random_state=42, 
            subsample=0.8,
            n_estimators=300, 
            max_depth=7, 
            learning_rate=0.2,
            colsample_bytree=1.0
        )
        self.regressor = regressor if regressor else XGBRegressor()
        self.threshold = threshold
        self.selector = None
    
    def leave_one_out(self, X, y):
        loo = LeaveOneOut()
        y_predicted = pd.Series(index=y.index, dtype=float)
        
        for train_index, test_index in loo.split(X, y):
            X_train, X_test = X[train_index, :], X[test_index, :]
            y_train, y_test = y.iloc[train_index], y.iloc[test_index]

            y_train_binary = (y_train >= self.threshold).astype(int)
            self.classifier.fit(X_train, y_train_binary)
            y_pred_class = self.classifier.predict(X_test)
            
            if y_pred_class[0] >= 1:
                self.regressor.fit(X_train, y_train)
                y_pred_regressor = self.regressor.predict(X_test)
                y_predicted.iloc[test_index[0]] = y_pred_regressor[0]
            else:
                y_predicted.iloc[test_index[0]] = 0
        
        return y_predicted
    
    def predict_loo(self, X, y, k=70):
        self.selector = SelectKBest(score_func=f_classif, k=k)
        X_selected = self.selector.fit_transform(X, y)
        return self.leave_one_out(X_selected, y)
    
    def predict(self, X):
        if self.selector is None:
            raise ValueError("Feature selector not fitted. Call 'predict_loo' first.")
        
        X_selected = self.selector.transform(X)
        y_classifier = self.classifier.predict(X_selected)
        y_regressor = self.regressor.predict(X_selected)
        y_predicted = y_classifier * y_regressor
        
        return y_predicted

# # Read lf data from a file
# lf_path = r'\\files.ubc.ca\team\PPRC\CAMERA\Booth_Processed\docs\facial_labels.csv'
# df_lfs = pd.read_csv(lf_path)

# # Clean data
# df_lfs.set_index('ID', inplace=True)
# df_lfs = df_lfs.drop(columns=['Date'])
# df_lfs.replace({'N': 0, 'D': 1, 'VERY POOR': 0, 'POOR': 1, 'FAIR': 2, 'GOOD': 3, 'SOME': 1, 'MOST': 2, 'PD': 1, 'HC': 0}, inplace=True)

# lf_3 = df_lfs['F_UPDRS_TM'].rename('Label')
# lf_3 = lf_3[~lf_3.index.duplicated(keep='first')]
lf_path = r'D:\Codes\Python\ParkinsonAssessment\csv\4_label.csv'
df_lfs = pd.read_csv(lf_path)
df_lfs.set_index('ID', inplace=True)
lf_3 = df_lfs['0'].rename('Label')

from facial_dataset import FacialDataset

au_sets = {
    'happy': [' AU06_r', ' AU12_r',' AU09_r', ' AU15_r', ' AU25_r',' AU01_r', ' AU04_r',' AU05_r', ' AU07_r', ' AU23_r'],
    'angry': [' AU06_r', ' AU12_r',' AU09_r', ' AU15_r', ' AU25_r',' AU01_r', ' AU04_r',' AU05_r', ' AU07_r', ' AU23_r'],
    'sad': [' AU06_r', ' AU12_r',' AU09_r', ' AU15_r', ' AU25_r',' AU01_r', ' AU04_r',' AU05_r', ' AU07_r', ' AU23_r'],
    'disgust': [' AU06_r', ' AU12_r',' AU09_r', ' AU15_r', ' AU25_r',' AU01_r', ' AU04_r',' AU05_r', ' AU07_r', ' AU23_r']
}
facial_data = FacialDataset(r'\\files.ubc.ca\team\PPRC\CAMERA\Booth_Processed', au_sets, emotions=['happy'], regex_filter=None)
final_results_df = pd.DataFrame(index=facial_data.get_data().index)

for emotion in au_sets.keys():
    print(f"Processing {emotion} emotion")
    facial_data = FacialDataset(r'\\files.ubc.ca\team\PPRC\CAMERA\Booth_Processed', au_sets, emotions=[emotion], regex_filter=None)
    facial_data.set_labels(lf_3,False)
    X = facial_data.get_data()
    y = facial_data.get_labels()
    X.fillna(0, inplace=True)
    
    if X.shape[1] < 24:
        print(f"Skipping {emotion} due to insufficient features.")
        continue

    classifier = XGBoostTwoStepClassifier()
    y_predicted = classifier.predict_loo(X[y >= 0], y[y >= 0])
    #call predict function for Xs that are not in y_predicted
    # Filter X for rows where y is NaN
    X_no_y = X[y.isna() | (y == -1)]
    # Predict for X values that don't have corresponding y values
    if X_no_y.shape[0] > 0:
        y_pred_no_y = classifier.predict(X_no_y)

        # Combine the predictions
        y_predicted = y_predicted.combine_first(pd.Series(y_pred_no_y, index=X_no_y.index))
    
    final_results_df = final_results_df.join(y_predicted.rename(f'Predicted_{emotion}'), how='left')
    y_nn_index = y[~y.isna()].index
    mse = mean_squared_error(y[y_nn_index], y_predicted[y_nn_index])
    mae = mean_absolute_error(y[y_nn_index], y_predicted[y_nn_index])
    accuracy = np.mean(y[y_nn_index] == np.round(y_predicted[y_nn_index]))
    print(f"{emotion} - LOO MSE: {mse:.4f}, MAE: {mae:.4f}, Accuracy: {accuracy:.4f}")

au_sets = {
    'happy': [' AU06_r', ' AU12_r'],
    'disgust': [' AU09_r', ' AU15_r', ' AU25_r'],
    'sad': [' AU01_r', ' AU04_r', ' AU15_r'],
    'angry': [' AU04_r', ' AU05_r', ' AU07_r', ' AU23_r']
}
facial_data = FacialDataset(r'\\files.ubc.ca\team\PPRC\CAMERA\Booth_Processed', au_sets,emotions=['happy','angry','sad','disgust'],regex_filter=None,task_type='imitation')
facial_data.set_labels(lf_3,False)
X = facial_data.get_data()
y = facial_data.get_labels()
X.fillna(0, inplace=True)
classifier = XGBoostTwoStepClassifier()
y_predicted = classifier.predict_loo(X[y >= 0], y[y >= 0])
y_predicted = classifier.predict_loo(X[y >= 0], y[y >= 0])
#call predict function for Xs that are not in y_predicted
# Filter X for rows where y is NaN
X_no_y = X[y.isna() | (y == -1)]
# Predict for X values that don't have corresponding y values
if X_no_y.shape[0] > 0:
    y_pred_no_y = classifier.predict(X_no_y)

    # Combine the predictions
    y_predicted = y_predicted.combine_first(pd.Series(y_pred_no_y, index=X_no_y.index))
    
final_results_df = final_results_df.join(y_predicted.rename(f'Predicted_all'), how='left')
y_nn_index = y[~y.isna()].index
mse = mean_squared_error(y[y_nn_index], y_predicted[y_nn_index])
mae = mean_absolute_error(y[y_nn_index], y_predicted[y_nn_index])
accuracy = np.mean(y[y_nn_index] == np.round(y_predicted[y_nn_index]))
print(f"{emotion} - LOO MSE: {mse:.4f}, MAE: {mae:.4f}, Accuracy: {accuracy:.4f}")
final_results_df.to_csv('loo_predictions_all_emotions_6.csv', index=True)
