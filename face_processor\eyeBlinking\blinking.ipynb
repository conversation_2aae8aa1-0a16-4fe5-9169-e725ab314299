{"cells": [{"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ID: 7182, Blinks: 3\n", "ID: 7225, Blinks: 1\n", "ID: 7288, Blinks: 4\n", "ID: 7415, Blinks: 2\n", "ID: 7417, Blinks: 1\n", "ID: 7452, Blinks: 0\n", "ID: 7452, Blinks: 1\n", "ID: 7488, Blinks: 3\n", "ID: 7518, Blinks: 1\n", "ID: 7571, Blinks: 9\n", "ID: 7578, Blinks: 2\n", "ID: 7580, Blinks: 0\n", "ID: 7597, Blinks: 2\n", "ID: 7609, Blinks: 0\n", "ID: 7610, Blinks: 5\n", "ID: 7628, Blinks: 0\n", "ID: 7629, Blinks: 0\n", "ID: 7634, Blinks: 6\n", "ID: 7640, Blinks: 10\n", "ID: 7652, Blinks: 5\n", "ID: 7735, Blinks: 0\n", "ID: 7755, Blinks: 0\n", "ID: 7795, Blinks: 1\n", "ID: 7814, Blinks: 3\n", "ID: 7822, Blinks: 1\n", "ID: 7852, Blinks: 0\n", "ID: 7888, Blinks: 1\n", "ID: 7985, Blinks: 7\n", "ID: 7987, Blinks: 10\n", "ID: 14555, Blinks: 2\n", "ID: 15377, Blinks: 0\n", "ID: 15813, Blinks: 0\n", "ID: 16219, Blinks: 4\n", "ID: 16827, Blinks: 5\n", "ID: 16883, Blinks: 0\n", "ID: 17000, Blinks: 0\n", "ID: 17200, Blinks: 0\n", "ID: 17202, Blinks: 1\n", "ID: 17434, Blinks: 9\n", "ID: 17599, Blinks: 1\n", "ID: 17980, Blinks: 0\n", "ID: 18198, Blinks: 1\n", "ID: 18317, Blinks: 3\n", "ID: 19015, Blinks: 1\n", "ID: 19091, Blinks: 6\n", "ID: 19124, Blinks: 0\n", "ID: 20959, Blinks: 0\n", "ID: 21401, Blinks: 2\n", "ID: 21696, Blinks: 0\n", "ID: 23160, Blinks: 0\n", "ID: 23191, Blinks: 2\n", "ID: 23284, Blinks: 1\n", "ID: 23284, Blinks: 1\n", "ID: 24192, Blinks: 0\n", "ID: 24318, Blinks: 0\n", "ID: 24352, Blinks: 0\n", "ID: 24475, Blinks: 1\n", "ID: 24601, Blinks: 0\n", "ID: 24622, Blinks: 0\n", "ID: 24757, Blinks: 6\n", "ID: 24860, Blinks: 0\n", "ID: 24889, Blinks: 0\n", "ID: 25260, Blinks: 0\n", "ID: 25352, Blinks: 0\n", "ID: 25439, Blinks: 2\n", "ID: 25439, Blinks: 2\n", "ID: 25533, Blinks: 2\n", "ID: 25934, Blinks: 8\n", "ID: 25957, Blinks: 0\n", "ID: 26145, Blinks: 0\n", "ID: 26692, Blinks: 0\n", "ID: 27123, Blinks: 1\n", "ID: 27425, Blinks: 2\n", "ID: 28321, Blinks: 1\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[13], line 77\u001b[0m\n\u001b[0;32m     75\u001b[0m counted_blinks \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[38;5;124mfiles.ubc.ca\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[38;5;124mteam\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[38;5;124mPPRC\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[38;5;124mCamera\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[38;5;124mBooth_Processed\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[38;5;124mdocs\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[38;5;124mbooth_blinkings_only.csv\u001b[39m\u001b[38;5;124m'\u001b[39m\n\u001b[0;32m     76\u001b[0m \u001b[38;5;66;03m# Example usage\u001b[39;00m\n\u001b[1;32m---> 77\u001b[0m \u001b[43mprocess_blinks\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcounted_blinks\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;130;43;01m\\\\\u001b[39;49;00m\u001b[38;5;130;43;01m\\\\\u001b[39;49;00m\u001b[38;5;124;43mfiles.ubc.ca\u001b[39;49m\u001b[38;5;130;43;01m\\\\\u001b[39;49;00m\u001b[38;5;124;43mteam\u001b[39;49m\u001b[38;5;130;43;01m\\\\\u001b[39;49;00m\u001b[38;5;124;43mPPRC\u001b[39;49m\u001b[38;5;130;43;01m\\\\\u001b[39;49;00m\u001b[38;5;124;43mCamera\u001b[39;49m\u001b[38;5;130;43;01m\\\\\u001b[39;49;00m\u001b[38;5;124;43mBooth_Processed\u001b[39;49m\u001b[38;5;130;43;01m\\\\\u001b[39;49;00m\u001b[38;5;124;43mfinger_tapping\u001b[39;49m\u001b[38;5;130;43;01m\\\\\u001b[39;49;00m\u001b[38;5;124;43mleft\u001b[39;49m\u001b[38;5;130;43;01m\\\\\u001b[39;49;00m\u001b[38;5;124;43mlandmarks\u001b[39;49m\u001b[38;5;130;43;01m\\\\\u001b[39;49;00m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43msave_doc\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m     79\u001b[0m \u001b[38;5;66;03m# join saved csv file with blink results and save again\u001b[39;00m\n\u001b[0;32m     80\u001b[0m df1 \u001b[38;5;241m=\u001b[39m pd\u001b[38;5;241m.\u001b[39mread_csv(counted_blinks)\n", "Cell \u001b[1;32mIn[13], line 64\u001b[0m, in \u001b[0;36mprocess_blinks\u001b[1;34m(id_file, landmarks_dir, save_doc)\u001b[0m\n\u001b[0;32m     62\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mstr\u001b[39m(\u001b[38;5;28mid\u001b[39m) \u001b[38;5;129;01min\u001b[39;00m files:\n\u001b[0;32m     63\u001b[0m     landmarks_file \u001b[38;5;241m=\u001b[39m os\u001b[38;5;241m.\u001b[39mpath\u001b[38;5;241m.\u001b[39mjoin(landmarks_dir, files)\n\u001b[1;32m---> 64\u001b[0m     landmarks_df \u001b[38;5;241m=\u001b[39m \u001b[43mload_landmarks\u001b[49m\u001b[43m(\u001b[49m\u001b[43mlandmarks_file\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m     65\u001b[0m     total_blinks \u001b[38;5;241m=\u001b[39m detect_blinks(landmarks_df)\n\u001b[0;32m     66\u001b[0m     results\u001b[38;5;241m.\u001b[39mappend({\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mID\u001b[39m\u001b[38;5;124m'\u001b[39m: \u001b[38;5;28mid\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mBlinks\u001b[39m\u001b[38;5;124m'\u001b[39m: total_blinks})\n", "Cell \u001b[1;32mIn[13], line 20\u001b[0m, in \u001b[0;36mload_landmarks\u001b[1;34m(csv_file)\u001b[0m\n\u001b[0;32m     19\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mload_landmarks\u001b[39m(csv_file):\n\u001b[1;32m---> 20\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[43mpd\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mread_csv\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcsv_file\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mc:\\Users\\<USER>\\.conda\\envs\\myenv\\Lib\\site-packages\\pandas\\io\\parsers\\readers.py:1026\u001b[0m, in \u001b[0;36mread_csv\u001b[1;34m(filepath_or_buffer, sep, delimiter, header, names, index_col, usecols, dtype, engine, converters, true_values, false_values, skipinitialspace, skiprows, skipfooter, nrows, na_values, keep_default_na, na_filter, verbose, skip_blank_lines, parse_dates, infer_datetime_format, keep_date_col, date_parser, date_format, dayfirst, cache_dates, iterator, chunksize, compression, thousands, decimal, lineterminator, quotechar, quoting, doublequote, escapechar, comment, encoding, encoding_errors, dialect, on_bad_lines, delim_whitespace, low_memory, memory_map, float_precision, storage_options, dtype_backend)\u001b[0m\n\u001b[0;32m   1013\u001b[0m kwds_defaults \u001b[38;5;241m=\u001b[39m _refine_defaults_read(\n\u001b[0;32m   1014\u001b[0m     dialect,\n\u001b[0;32m   1015\u001b[0m     delimiter,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m   1022\u001b[0m     dtype_backend\u001b[38;5;241m=\u001b[39mdtype_backend,\n\u001b[0;32m   1023\u001b[0m )\n\u001b[0;32m   1024\u001b[0m kwds\u001b[38;5;241m.\u001b[39mupdate(kwds_defaults)\n\u001b[1;32m-> 1026\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_read\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfilepath_or_buffer\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mkwds\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mc:\\Users\\<USER>\\.conda\\envs\\myenv\\Lib\\site-packages\\pandas\\io\\parsers\\readers.py:620\u001b[0m, in \u001b[0;36m_read\u001b[1;34m(filepath_or_buffer, kwds)\u001b[0m\n\u001b[0;32m    617\u001b[0m _validate_names(kwds\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mnames\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m))\n\u001b[0;32m    619\u001b[0m \u001b[38;5;66;03m# Create the parser.\u001b[39;00m\n\u001b[1;32m--> 620\u001b[0m parser \u001b[38;5;241m=\u001b[39m \u001b[43mTextFileReader\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfilepath_or_buffer\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwds\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    622\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m chunksize \u001b[38;5;129;01mor\u001b[39;00m iterator:\n\u001b[0;32m    623\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m parser\n", "File \u001b[1;32mc:\\Users\\<USER>\\.conda\\envs\\myenv\\Lib\\site-packages\\pandas\\io\\parsers\\readers.py:1620\u001b[0m, in \u001b[0;36mTextFileReader.__init__\u001b[1;34m(self, f, engine, **kwds)\u001b[0m\n\u001b[0;32m   1617\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39moptions[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mhas_index_names\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m=\u001b[39m kwds[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mhas_index_names\u001b[39m\u001b[38;5;124m\"\u001b[39m]\n\u001b[0;32m   1619\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mhandles: IOHandles \u001b[38;5;241m|\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[1;32m-> 1620\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_engine \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_make_engine\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mengine\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mc:\\Users\\<USER>\\.conda\\envs\\myenv\\Lib\\site-packages\\pandas\\io\\parsers\\readers.py:1880\u001b[0m, in \u001b[0;36mTextFileReader._make_engine\u001b[1;34m(self, f, engine)\u001b[0m\n\u001b[0;32m   1878\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mb\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m mode:\n\u001b[0;32m   1879\u001b[0m         mode \u001b[38;5;241m+\u001b[39m\u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mb\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m-> 1880\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mhandles \u001b[38;5;241m=\u001b[39m \u001b[43mget_handle\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m   1881\u001b[0m \u001b[43m    \u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1882\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmode\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1883\u001b[0m \u001b[43m    \u001b[49m\u001b[43mencoding\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mencoding\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1884\u001b[0m \u001b[43m    \u001b[49m\u001b[43mcompression\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mcompression\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1885\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmemory_map\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mmemory_map\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1886\u001b[0m \u001b[43m    \u001b[49m\u001b[43mis_text\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mis_text\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1887\u001b[0m \u001b[43m    \u001b[49m\u001b[43merrors\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mencoding_errors\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mstrict\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1888\u001b[0m \u001b[43m    \u001b[49m\u001b[43mstorage_options\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mstorage_options\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1889\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   1890\u001b[0m \u001b[38;5;28;01massert\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mhandles \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[0;32m   1891\u001b[0m f \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mhandles\u001b[38;5;241m.\u001b[39mhandle\n", "File \u001b[1;32mc:\\Users\\<USER>\\.conda\\envs\\myenv\\Lib\\site-packages\\pandas\\io\\common.py:873\u001b[0m, in \u001b[0;36mget_handle\u001b[1;34m(path_or_buf, mode, encoding, compression, memory_map, is_text, errors, storage_options)\u001b[0m\n\u001b[0;32m    868\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(handle, \u001b[38;5;28mstr\u001b[39m):\n\u001b[0;32m    869\u001b[0m     \u001b[38;5;66;03m# Check whether the filename is to be opened in binary mode.\u001b[39;00m\n\u001b[0;32m    870\u001b[0m     \u001b[38;5;66;03m# Binary mode does not support 'encoding' and 'newline'.\u001b[39;00m\n\u001b[0;32m    871\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m ioargs\u001b[38;5;241m.\u001b[39mencoding \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mb\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m ioargs\u001b[38;5;241m.\u001b[39mmode:\n\u001b[0;32m    872\u001b[0m         \u001b[38;5;66;03m# Encoding\u001b[39;00m\n\u001b[1;32m--> 873\u001b[0m         handle \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mopen\u001b[39;49m\u001b[43m(\u001b[49m\n\u001b[0;32m    874\u001b[0m \u001b[43m            \u001b[49m\u001b[43mhandle\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    875\u001b[0m \u001b[43m            \u001b[49m\u001b[43mioargs\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmode\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    876\u001b[0m \u001b[43m            \u001b[49m\u001b[43mencoding\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mioargs\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mencoding\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    877\u001b[0m \u001b[43m            \u001b[49m\u001b[43merrors\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43merrors\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    878\u001b[0m \u001b[43m            \u001b[49m\u001b[43mnewline\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[0;32m    879\u001b[0m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    880\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m    881\u001b[0m         \u001b[38;5;66;03m# Binary mode\u001b[39;00m\n\u001b[0;32m    882\u001b[0m         handle \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mopen\u001b[39m(handle, ioargs\u001b[38;5;241m.\u001b[39mmode)\n", "File \u001b[1;32m<frozen codecs>:309\u001b[0m, in \u001b[0;36m__init__\u001b[1;34m(self, errors)\u001b[0m\n", "\u001b[1;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["import pandas as pd\n", "from scipy.spatial import distance\n", "import numpy as np\n", "import os\n", "\n", "# Constants\n", "EYE_AR_THRESH = 0.9\n", "EYE_AR_CONSEC_FRAMES = 4\n", "MOVING_AVERAGE_WINDOW = 6\n", "\n", "# Function to compute Eye Aspect Ratio (EAR)\n", "def eye_aspect_ratio(eye):\n", "    A = distance.euclidean(eye[1], eye[5])\n", "    B = distance.euclidean(eye[2], eye[4])\n", "    C = distance.euclidean(eye[0], eye[3])\n", "    return (A + B) / (2.0 * C)\n", "\n", "# Function to load landmarks data from CSV\n", "def load_landmarks(csv_file):\n", "    return pd.read_csv(csv_file)\n", "\n", "# Function to detect blinks in landmarks data\n", "def detect_blinks(landmarks_df):\n", "    blink_counter = 0\n", "    frame_counter = 0\n", "    ear_values = []\n", "\n", "    for _, row in landmarks_df.iterrows():\n", "        left_eye = [(row[f'Landmark_{i}_x'], row[f'Landmark_{i}_y']) for i in range(36, 42)]\n", "        right_eye = [(row[f'Landmark_{i}_x'], row[f'Landmark_{i}_y']) for i in range(42, 48)]\n", "        left_ear = eye_aspect_ratio(left_eye)\n", "        right_ear = eye_aspect_ratio(right_eye)\n", "        ear = (left_ear + right_ear) / 2.0\n", "        ear_values.append(ear)\n", "        if len(ear_values) > MOVING_AVERAGE_WINDOW:\n", "            ear_values.pop(0)\n", "        smoothed_ear = np.mean(ear_values)\n", "        dynamic_threshold = smoothed_ear * EYE_AR_THRESH\n", "        if ear < dynamic_threshold:\n", "            frame_counter += 1\n", "        else:\n", "            if frame_counter >= EYE_AR_CONSEC_FRAMES:\n", "                blink_counter += 1\n", "            frame_counter = 0\n", "\n", "    return blink_counter\n", "\n", "# Main function to process each ID\n", "def process_blinks(id_file, landmarks_dir,save_doc):\n", "    id_df = pd.read_csv(id_file)\n", "    results = []\n", "\n", "    for _, row in id_df.iterrows():\n", "        id = row['ID']\n", "\n", "        for files in os.listdir(landmarks_dir):\n", "            if str(id) in files:\n", "                landmarks_file = os.path.join(landmarks_dir, files)\n", "                landmarks_df = load_landmarks(landmarks_file)\n", "                total_blinks = detect_blinks(landmarks_df)\n", "                results.append({'ID': id, 'Blinks': total_blinks})\n", "                print(f'ID: {id}, Blinks: {total_blinks}')\n", "\n", "    # Save results to a new CSV file\n", "    results_df = pd.DataFrame(results)\n", "    results_df.to_csv(save_doc, index=False)\n", "    print(f'Results have been saved to {save_doc}')\n", "\n", "save_doc = 'blink_results_right.csv'\n", "counted_blinks = '\\\\\\\\files.ubc.ca\\\\team\\\\PPRC\\\\Camera\\\\Booth_Processed\\\\docs\\\\booth_blinkings_only.csv'\n", "# Example usage\n", "process_blinks(counted_blinks, '\\\\\\\\files.ubc.ca\\\\team\\\\PPRC\\\\Camera\\\\Booth_Processed\\\\finger_tapping\\\\right\\\\landmarks\\\\',save_doc)\n", "\n", "# join saved csv file with blink results and save again\n", "df1 = pd.read_csv(counted_blinks)\n", "df2 = pd.read_csv(save_doc)\n", "df = pd.merge(df1, df2, on='ID')\n", "df['diff'] =  df['Right blinks'] -df['Blinks']\n", "print (sum(abs(df[df['Right blinks']>=0]['diff'])))\n", "df.to_csv(save_doc, index=False)\n", "\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ID: 7182, Blinks: 4\n", "ID: 7225, Blinks: 2\n", "ID: 7288, Blinks: 6\n", "ID: 7415, Blinks: 3\n", "ID: 7417, Blinks: 1\n", "ID: 7452, Blinks: 0\n", "ID: 7488, Blinks: 4\n", "ID: 7518, Blinks: 2\n", "ID: 7571, Blinks: 26\n", "ID: 7578, Blinks: 2\n", "ID: 7580, Blinks: 0\n", "ID: 7597, Blinks: 4\n", "ID: 7609, Blinks: 1\n", "ID: 7610, Blinks: 16\n", "ID: 7628, Blinks: 0\n", "ID: 7629, Blinks: 0\n", "ID: 7634, Blinks: 6\n", "ID: 7640, Blinks: 8\n", "ID: 7652, Blinks: 7\n", "ID: 7735, Blinks: 0\n", "ID: 7755, Blinks: 1\n", "ID: 7795, Blinks: 3\n", "ID: 7814, Blinks: 4\n", "ID: 7822, Blinks: 1\n", "ID: 7852, Blinks: 4\n", "ID: 7888, Blinks: 1\n", "ID: 7985, Blinks: 8\n", "ID: 7987, Blinks: 11\n", "ID: 14555, Blinks: 1\n", "ID: 15377, Blinks: 0\n", "ID: 15813, Blinks: 0\n", "ID: 16219, Blinks: 3\n", "ID: 16827, Blinks: 7\n", "ID: 16883, Blinks: 0\n", "ID: 17000, Blinks: 0\n", "ID: 17200, Blinks: 6\n", "ID: 17202, Blinks: 6\n", "ID: 17434, Blinks: 4\n", "ID: 17599, Blinks: 4\n", "ID: 17980, Blinks: 0\n", "Results have been saved to blink_results3.csv\n", "90.0\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "import os\n", "from scipy.signal import find_peaks\n", "\n", "# Constants\n", "BLINK_THRESHOLD = 0.6  # Threshold for blink detection using AU45\n", "EYE_AR_CONSEC_FRAMES = 4  # Number of consecutive frames considered a blink\n", "\n", "# Function to load AU data from CSV\n", "def load_action_units(csv_file):\n", "    return pd.read_csv(csv_file)\n", "\n", "# Function to detect blinks using action unit data\n", "def detect_blinks(au_df):\n", "    blink_counter = 0\n", "    # Obtain AU45 data which indicates eye blink\n", "    data = au_df[' AU45_c'].astype(float).rolling(window=1, center=True).mean()\n", "    data.dropna(inplace=True)\n", "    peaks, _ = find_peaks(data, height=BLINK_THRESHOLD)\n", "    blink_counter = len(peaks)\n", "    return blink_counter\n", "\n", "# Main function to process each ID\n", "def process_blinks(id_file, action_units_dir, save_doc):\n", "    id_df = pd.read_csv(id_file)\n", "    results = []\n", "\n", "    for _, row in id_df.iterrows():\n", "        id = row['ID']\n", "        if row['Right blinks'] >= 0:\n", "            for files in os.listdir(action_units_dir):\n", "                if str(id) in files and files.endswith('.csv'):\n", "                    au_file = os.path.join(action_units_dir, files)\n", "                    au_df = load_action_units(au_file)\n", "                    total_blinks = detect_blinks(au_df)\n", "                    results.append({'ID': id, 'Blinks': total_blinks})\n", "                    print(f'ID: {id}, Blinks: {total_blinks}')\n", "                    break\n", "\n", "    # Save results to a new CSV file\n", "    results_df = pd.DataFrame(results)\n", "    results_df.to_csv(save_doc, index=False)\n", "    print(f'Results have been saved to {save_doc}')\n", "\n", "save_doc = 'blink_results3.csv'\n", "action_units_path = '\\\\\\\\files.ubc.ca\\\\team\\\\PPRC\\\\Camera\\\\Booth_Processed\\\\finger_tapping\\\\left\\\\action_units\\\\'\n", "\n", "# Example usage\n", "id_file_path = '\\\\\\\\files.ubc.ca\\\\team\\\\PPRC\\\\Camera\\\\Booth_Processed\\\\docs\\\\booth_blinkings_only.csv'\n", "process_blinks(id_file_path, action_units_path, save_doc)\n", "\n", "# Join saved csv file with blink results and save again\n", "df1 = pd.read_csv(id_file_path)\n", "df2 = pd.read_csv(save_doc)\n", "df = pd.merge(df1, df2, on='ID')\n", "df['diff'] = df['Right blinks'] - df['Blinks']\n", "print(sum(abs(df[df['Right blinks'] >= 0]['diff'])))\n", "df.to_csv(save_doc, index=False)\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Plot saved for 07155\n", "Plot saved for 07182\n", "Plot saved for 07225\n", "Plot saved for 07284\n", "Plot saved for 07288\n", "Plot saved for 07334\n", "Plot saved for 07415\n", "Plot saved for 07417\n", "Plot saved for 07452\n", "Plot saved for 07452\n", "Plot saved for 07488\n", "Plot saved for 07518\n", "Plot saved for 07536\n", "Plot saved for 07566\n", "Plot saved for 07571\n", "Plot saved for 07578\n", "Plot saved for 07580\n", "Plot saved for 07587\n", "Plot saved for 07597\n", "Plot saved for 07609\n", "Plot saved for 07610\n", "Plot saved for 07628\n", "Plot saved for 07629\n", "Plot saved for 07634\n", "Plot saved for 07640\n", "Plot saved for 07652\n", "Plot saved for 07677\n", "Plot saved for 07681\n", "Plot saved for 07727\n", "Plot saved for 07735\n", "Plot saved for 07755\n", "Plot saved for 07771\n", "Plot saved for 07795\n", "Plot saved for 07814\n", "Plot saved for 07819\n", "Plot saved for 07822\n", "Plot saved for 07852\n", "Plot saved for 07855\n", "Plot saved for 07888\n", "Plot saved for 07985\n", "Plot saved for 07987\n", "Plot saved for 14555\n", "Plot saved for 15377\n", "Plot saved for 15813\n", "Plot saved for 16219\n", "Plot saved for 16827\n", "Plot saved for 16883\n", "Plot saved for 17000\n", "Plot saved for 17200\n", "Plot saved for 17202\n", "Plot saved for 17434\n", "Plot saved for 17599\n", "Plot saved for 17980\n", "Plot saved for 18198\n", "Plot saved for 18317\n", "Plot saved for 18372\n", "Plot saved for 19015\n", "Plot saved for 19091\n", "Plot saved for 19124\n", "Plot saved for 20959\n", "Plot saved for 21401\n", "Plot saved for 21696\n", "Plot saved for 22178\n", "Plot saved for 23160\n", "Plot saved for 23191\n", "Plot saved for 23284\n", "Plot saved for 23284\n", "Plot saved for 24192\n", "Plot saved for 24318\n", "Plot saved for 24352\n", "Plot saved for 24475\n", "Plot saved for 24601\n", "Plot saved for 24622\n", "Plot saved for 24757\n", "Plot saved for 24860\n", "Plot saved for 24889\n", "Plot saved for 25260\n", "Plot saved for 25352\n", "Plot saved for 25439\n", "Plot saved for 25533\n", "Plot saved for 25793\n", "Plot saved for 25934\n", "Plot saved for 25957\n", "Plot saved for 26145\n", "Plot saved for 26407\n", "Plot saved for 26692\n", "Plot saved for 27123\n", "Plot saved for 27423\n", "Plot saved for 27425\n", "Plot saved for 28321\n", "Plot saved for 28350\n", "Plot saved for 28411\n", "Plot saved for 28550\n", "Plot saved for 28615\n", "Plot saved for 28630\n", "Plot saved for 28637\n", "Plot saved for 28641\n", "Plot saved for 28726\n", "Plot saved for 28731\n", "Plot saved for 28813\n", "Plot saved for 29079\n", "Plot saved for 29157\n", "Plot saved for 29377\n", "Plot saved for 29570\n", "Plot saved for 29880\n", "Plot saved for 29880\n", "Plot saved for 29880\n", "Plot saved for 30009\n", "Plot saved for 30104\n", "Plot saved for 30148\n", "Plot saved for 30279\n", "Plot saved for 30414\n", "Plot saved for 30593\n", "Plot saved for 30893\n", "Plot saved for 30961\n", "Plot saved for 30982\n", "Plot saved for 31092\n", "Plot saved for 31100\n", "Plot saved for 31182\n", "Plot saved for 31231\n", "Plot saved for 31240\n", "Plot saved for 31318\n", "Plot saved for 31319\n", "Plot saved for 31769\n", "Plot saved for 31848\n", "Plot saved for 31961\n", "Plot saved for 32160\n", "Plot saved for 32200\n", "Plot saved for 32282\n", "Plot saved for 32519\n", "Plot saved for 32541\n", "Plot saved for 32594\n", "Plot saved for 32853\n", "Plot saved for 33023\n", "Plot saved for 33151\n", "Plot saved for 33164\n", "Plot saved for 33164\n", "Plot saved for 33430\n", "Plot saved for 33527\n", "Plot saved for 33749\n", "Plot saved for 33927\n", "Plot saved for 34142\n", "Plot saved for 34417\n", "Plot saved for 34492\n", "Plot saved for 34509\n", "Plot saved for 34914\n", "Plot saved for 34965\n", "Plot saved for 35246\n", "Plot saved for 35623\n", "Plot saved for 35747\n", "Plot saved for 35816\n", "Plot saved for 36220\n", "Plot saved for 36297\n", "Plot saved for 36407\n", "Plot saved for 36436\n", "Plot saved for 36532\n", "Plot saved for 36564\n", "Plot saved for 36581\n", "Plot saved for 36660\n", "Plot saved for 38046\n", "Plot saved for 38050\n", "Plot saved for 38073\n", "Plot saved for 38100\n", "Plot saved for 38210\n", "Plot saved for 38215\n", "Plot saved for 38255\n", "Plot saved for 38256\n", "Plot saved for 38519\n", "Plot saved for 38902\n", "Plot saved for 39200\n", "Plot saved for 39274\n", "Plot saved for 39528\n", "Plot saved for 39685\n", "Plot saved for 40613\n"]}], "source": ["import pandas as pd\n", "from scipy.spatial import distance\n", "import numpy as np\n", "import os\n", "import matplotlib.pyplot as plt\n", "\n", "# Constants\n", "EYE_AR_THRESH = 0.9\n", "EYE_AR_CONSEC_FRAMES = 4\n", "MOVING_AVERAGE_WINDOW = 6  # This window size can be adjusted for smoother results\n", "\n", "# Function to compute Eye Aspect Ratio (EAR)\n", "def eye_aspect_ratio(eye):\n", "    A = distance.euclidean(eye[1], eye[5])\n", "    B = distance.euclidean(eye[2], eye[4])\n", "    C = distance.euclidean(eye[0], eye[3])\n", "    return (A + B) / (2.0 * C)\n", "\n", "# Function to load landmarks data from CSV\n", "def load_landmarks(csv_file):\n", "    return pd.read_csv(csv_file)\n", "\n", "# Main function to process each video\n", "def process_videos(landmarks_dir, plot_dir):\n", "    for files in os.listdir(landmarks_dir):\n", "        landmarks_file = os.path.join(landmarks_dir, files)\n", "        landmarks_df = load_landmarks(landmarks_file)\n", "        id = files.split('_')[0]\n", "\n", "        # Extract EAR values for all frames\n", "        ear_series = []\n", "        for _, row in landmarks_df.iterrows():\n", "            left_eye = [(row[f'Landmark_{i}_x'], row[f'Landmark_{i}_y']) for i in range(36, 42)]\n", "            right_eye = [(row[f'Landmark_{i}_x'], row[f'Landmark_{i}_y']) for i in range(42, 48)]\n", "            left_ear = eye_aspect_ratio(left_eye)\n", "            right_ear = eye_aspect_ratio(right_eye)\n", "            ear = (left_ear + right_ear) / 2.0\n", "            ear_series.append(ear)\n", "\n", "        # Convert EAR series to DataFrame and apply rolling average\n", "        ear_df = pd.DataFrame(ear_series, columns=['EAR'])\n", "        ear_df['Smoothed_EAR'] = ear_df['EAR'].rolling(window=MOVING_AVERAGE_WINDOW, min_periods=1, center=True).mean()\n", "\n", "        # Plotting EAR over frames\n", "        plt.figure(figsize=(10, 5))\n", "        plt.plot(ear_df['EAR'], label='Original EAR', alpha=0.5)\n", "        plt.plot(ear_df['Smoothed_EAR'], label='Smoothed EAR', color='red')\n", "        plt.xlabel('Frame')\n", "        plt.ylabel('EAR')\n", "        plt.title(f'EAR Changes Over Time - {\"Healthy\" if id.startswith(\"07\") else \"PD\"}')\n", "        plt.legend()\n", "\n", "        # Saving the plot\n", "        plt.savefig(os.path.join(plot_dir, f'{id}_EAR_plot.png'))\n", "        plt.close()\n", "        print(f'Plot saved for {id}')\n", "\n", "# Directory paths\n", "save_plot_dir = '\\\\\\\\files.ubc.ca\\\\team\\\\PPRC\\\\Camera\\\\Booth_Processed\\\\finger_tapping\\\\left\\\\figures\\\\'\n", "landmarks_dir = '\\\\\\\\files.ubc.ca\\\\team\\\\PPRC\\\\Camera\\\\Booth_Processed\\\\finger_tapping\\\\left\\\\landmarks\\\\'\n", "\n", "# Example usage\n", "process_videos(landmarks_dir, save_plot_dir)\n"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Classification accuracy: 0.73\n"]}], "source": ["import pandas as pd\n", "from scipy.spatial import distance\n", "import numpy as np\n", "import os\n", "import matplotlib.pyplot as plt\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import accuracy_score\n", "from pycatch22 import catch22_all\n", "\n", "# Constants\n", "EYE_AR_THRESH = 0.9\n", "EYE_AR_CONSEC_FRAMES = 4\n", "MOVING_AVERAGE_WINDOW = 6  # This window size can be adjusted for smoother results\n", "\n", "# Function to compute Eye Aspect Ratio (EAR)\n", "def eye_aspect_ratio(eye):\n", "    A = distance.euclidean(eye[1], eye[5])\n", "    B = distance.euclidean(eye[2], eye[4])\n", "    C = distance.euclidean(eye[0], eye[3])\n", "    return (A + B) / (2.0 * C)\n", "\n", "# Function to load landmarks data from CSV\n", "def load_landmarks(csv_file):\n", "    return pd.read_csv(csv_file)\n", "\n", "# Function to extract features from EAR series\n", "def extract_features(ear_series):\n", "    features = {}\n", "    # Basic statistical features\n", "    features['mean'] = np.mean(ear_series)\n", "    features['std'] = np.std(ear_series)\n", "    \n", "    # Catch22 features\n", "    catch22_results = catch22_all(ear_series)\n", "    for name, value in zip(catch22_results['names'], catch22_results['values']):\n", "        features[name] = value\n", "    \n", "    return features\n", "\n", "# Main function to process each video\n", "def process_videos(landmarks_dir):\n", "    feature_set = []\n", "    for files in os.listdir(landmarks_dir):\n", "        landmarks_file = os.path.join(landmarks_dir, files)\n", "        landmarks_df = load_landmarks(landmarks_file)\n", "        id = files.split('_')[0]\n", "\n", "        # Extract EAR values for all frames\n", "        ear_series = [(eye_aspect_ratio([(row[f'Landmark_{i}_x'], row[f'Landmark_{i}_y']) for i in range(36, 42)]) +\n", "                      eye_aspect_ratio([(row[f'Landmark_{i}_x'], row[f'Landmark_{i}_y']) for i in range(42, 48)])) / 2.0\n", "                      for _, row in landmarks_df.iterrows()]\n", "\n", "        # Extract features\n", "        features = extract_features(ear_series)\n", "        features['label'] = 0 if id.startswith(\"07\") else 1  # Healthy: 0, PD: 1\n", "        features['ID'] = id\n", "        feature_set.append(features)\n", "\n", "    return feature_set\n", "\n", "# Directory paths\n", "landmarks_dir_left = '\\\\\\\\files.ubc.ca\\\\team\\\\PPRC\\\\Camera\\\\Booth_Processed\\\\finger_tapping\\\\left\\\\landmarks\\\\'\n", "landmarks_dir_right = '\\\\\\\\files.ubc.ca\\\\team\\\\PPRC\\\\Camera\\\\Booth_Processed\\\\finger_tapping\\\\right\\\\landmarks\\\\'\n", "\n", "# Process videos and extract features\n", "features_left = process_videos(landmarks_dir_left) \n", "features_right = process_videos(landmarks_dir_right)\n", "# Convert to DataFrame\n", "df_left = pd.DataFrame(features_left)\n", "df_right = pd.DataFrame(features_right)\n", "df_left.set_index('ID', inplace=True)\n", "df_right.set_index('ID', inplace=True)\n", "df_left.to_csv('features_blinking_left.csv')\n", "df_right.to_csv('features_blinking_right.csv')\n", "df = df_left.join(df_right, lsuffix='_left', rsuffix='_right')\n", "\n", "# Prepare data for classification\n", "X = df.drop(['label_left','label_right'], axis=1)\n", "y = df['label_left']\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)\n", "\n", "# Train Random Forest classifier\n", "clf = RandomForestClassifier(n_estimators=100, random_state=42)\n", "clf.fit(X_train, y_train)\n", "y_pred = clf.predict(X_test)\n", "\n", "# Calculate accuracy\n", "accuracy = accuracy_score(y_test, y_pred)\n", "print(f'Classification accuracy: {accuracy:.2f}')\n"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"image/png": "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*****************************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*******************************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", "text/plain": ["<Figure size 864x720 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from scipy.spatial import distance\n", "import numpy as np\n", "\n", "# Function to compute Eye Aspect Ratio (EAR)\n", "def eye_aspect_ratio(eye):\n", "    A = distance.euclidean(eye[1], eye[5])\n", "    B = distance.euclidean(eye[2], eye[4])\n", "    C = distance.euclidean(eye[0], eye[3])\n", "    return (A + B) / (2.0 * C)\n", "\n", "# Function to convert row data to eye landmarks\n", "def extract_eye_landmarks(row, eye_indices):\n", "    return [(row[f'Landmark_{i}_x'], row[f'Landmark_{i}_y']) for i in eye_indices]\n", "\n", "# Function to process a CSV file and display EAR timeseries\n", "def display_ear_timeseries(landmarks_csv):\n", "    landmarks_df = pd.read_csv(landmarks_csv)\n", "\n", "    left_eye_indices = range(36, 42)\n", "    right_eye_indices = range(42, 48)\n", "\n", "    ear_left = []\n", "    ear_right = []\n", "\n", "    for _, row in landmarks_df.iterrows():\n", "        left_eye = extract_eye_landmarks(row, left_eye_indices)\n", "        right_eye = extract_eye_landmarks(row, right_eye_indices)\n", "\n", "        ear_left.append(eye_aspect_ratio(left_eye))\n", "        ear_right.append(eye_aspect_ratio(right_eye))\n", "\n", "    # Plot the EAR timeseries\n", "    plt.figure(figsize=(12, 10))\n", "    plt.plot(ear_left, label='Left EAR')\n", "    plt.plot(ear_right, label='Right EAR')\n", "    plt.xlabel('Frame')\n", "    plt.ylabel('EAR')\n", "    plt.title('Eye Aspect Ratio (EAR) Over Time')\n", "    plt.legend()\n", "    plt.show()\n", "\n", "# Example usage\n", "landmarks_csv = r'\\\\files.ubc.ca\\team\\PPRC\\Camera\\Booth_Processed\\finger_tapping\\right\\landmarks\\07610_20231205_right_finger_tapping_landmarks.csv'\n", "display_ear_timeseries(landmarks_csv)\n"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["save_doc = '\\\\\\\\files.ubc.ca\\\\team\\\\PPRC\\\\Camera\\\\Booth_results\\\\eye_blinking\\\\Experiment_1\\\\finger_tapping_right_blink_results.csv'\n", "counted_blinks = '\\\\\\\\files.ubc.ca\\\\team\\\\PPRC\\\\Camera\\\\Booth_Processed\\\\docs\\\\booth_right_blinking.csv'\n", "booth_updrs = '\\\\\\\\files.ubc.ca\\\\team\\\\PPRC\\\\Camera\\\\Booth_Processed\\\\docs\\\\booth_updrs.csv'\n", "\n", "#join saved csv file with blink results and save again\n", "df1 = pd.read_csv(counted_blinks)\n", "df2 = pd.read_csv(save_doc)\n", "df3 = pd.read_csv(booth_updrs)\n", "\n", "#set id integer\n", "df3['ID'] = df3['ID'].astype(int)\n", "df3.set_index('ID', inplace=True)\n", "df = pd.merge(df1, df2, on='ID')\n", "df = pd.merge(df, df3, on='ID')\n", "df['diff'] =  df['number of blinks'] -df['Blinks']\n", "df_new = df[['ID','Eye Bli0ki0g','Blinks','number of blinks','diff','Facial_Expression_UPDRS (conventional)','Duration','glasses','obstacle']]\n", "#df.to_csv(save_doc, index=False)"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["loc = r'\\\\files.ubc.ca\\team\\PPRC\\Camera\\Booth_results\\eye_blinking\\Experiment_1\\finger_tapping_right_blink_compare_results_addon.csv'\n", "df_new.to_csv(loc, index=False)"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Correlation between 'glasses' and 'diff': 0.10286218033312723\n"]}], "source": ["correlation = df_new['glasses'].corr(np.abs(df_new['diff']))\n", "print(\"Correlation between 'glasses' and 'diff':\", correlation)\n"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 864x288 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["from sklearn.metrics import confusion_matrix\n", "import matplotlib.pyplot as plt\n", "from seaborn import heatmap\n", "\n", "def plot_correlation_matrix(corr_matrix):\n", "    plt.figure(figsize=(12, 4))\n", "\n", "    heatmap(corr_matrix, annot=True, cmap='coolwarm', vmin=-1, vmax=1)\n", "    plt.title('Experte Eye Blinking Decreesed Detected vs. Number of Eye Blinking in the video')\n", "    plt.ylabel('Eye Blinking Decreesed')\n", "    plt.xlabel('Number of Eye Blinkings in the video')\n", "    plt.show()\n", "    \n", "df = df_new\n", "#remove where Eye Bli0ki0g is nan\n", "df_new = df_new.dropna(subset=['Eye Bli0ki0g'])\n", "df_new = df_new[df_new['Duration'] >5]\n", "# Extract the relevant columns from the dataframe\n", "eye_blinking = df_new['Eye Bli0ki0g']\n", "number_of_blinks = df_new['number of blinks']\n", "#replace nan with Blinks value\n", "number_of_blinks = number_of_blinks.fillna(df_new['Blinks'])\n", "\n", "\n", "# Create the confusion matrix\n", "cm = confusion_matrix(eye_blinking, number_of_blinks)\n", "\n", "#only save two rows and all columns\n", "cm = cm[:2,:]\n", "\n", "# Plot the confusion matrix\n", "plot_correlation_matrix(cm)\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 2}