from pathlib import Path
import pandas as pd
from hand_processor.finger_tapping_features import FeatureExtractor
from report_generation.base_report_generation.base_report_generator import BaseReportGenerator
from report_generation.base_report_generation.updrs_model import UPDRSModel
import matplotlib.pyplot as plt
import numpy as np
import os
import cv2
from scipy.stats import norm
import matplotlib.colors as mcolors
from visualization.plotter import plot_time_series_start_end
from report_generation.base_report_generation.DataReader import TappingDataReader
from scipy import stats
import ast
import mediapipe as mp

class FeatureProcessor:
    def __init__(self, model: UPDRSModel):
        self.model = model

    def extract_features(self, features_data, labels_data):
        try:
            # Check if labels_data is empty or has insufficient data
            if len(labels_data) == 0:
                raise ValueError("Labels data is empty or insufficient.")
            
            # Calculate mean and standard deviation, handling any issues with empty data
            mu = np.mean(labels_data)
            std = np.std(labels_data)
            if std == 0:
                raise ValueError("Standard deviation is zero, cannot calculate z-score.")
            
            # Initialize features dictionary
            features = {}
            
            # Safely access keys from features_data with default values or handle missing keys
            try:
                features['taps'] = features_data.get('tap_count', [0])[0]
                features['hesitation/halt'] = features_data.get('Finger Normalized Distance_halts_and_hesitations', [0])[0]
                features['amplitude'] = np.round(features_data.get('Angular Distance_max_amplitude', [0])[0])
                features['speed'] = np.round(features_data.get('Finger Normalized Distance_mean_tap_speed', [0])[0])
                features['consistency'] = features_data.get('Finger Normalized Distance_consistency', [0])[0]
                features['time'] = features_data.get('task_duration', [0])[0]
                features['decrement amplitude'] = np.round(features_data.get('Angular Distance_amplitude_decrement', [0])[0])
            except (KeyError, IndexError) as e:
                raise KeyError(f"Missing or invalid key in features_data: {e}")
            
            # Handle potential prediction errors from the model
            try:
                features['updrs score'] = np.round(self.model.predict(features_data))
            except Exception as e:
                raise ValueError(f"Error in model prediction: {e}")

            # Calculate z-score and handle potential division errors
            try:
                z_score = (features['updrs score'] - mu) / std  # z-score of the value
                features['z-score'] = np.round(z_score, 2)
            except ZeroDivisionError:
                raise ZeroDivisionError("Standard deviation is zero, cannot compute z-score.")
            
            return features
        
        except Exception as e:
            print(f"Error in extract_features: {e}")
            return None


class ChartGenerator:
    def __init__(self, output_path, output_name):
        self.output_path = output_path
        self.output_name = output_name


    def generate_percentile_plot(self, data, value, side):
        # Drop missing data
        data.dropna(inplace=True)

        # Calculate percentile and z-score
        percentile_value = stats.percentileofscore(data, value)
        mu, std = np.mean(data), np.std(data)

        # Set up x range and compute PDF values for the normal distribution
        x_range = np.linspace(mu - 4 * std, mu + 4 * std, 1000)
        pdf_values = norm.pdf(x_range, mu, std)

        # Create color map
        cmap = mcolors.LinearSegmentedColormap.from_list("", ["blue", "white", "red"])
        norm_x = (x_range - np.min(x_range)) / (np.max(x_range) - np.min(x_range))
        colors = cmap(norm_x)

        # Create plot
        fig, ax = plt.subplots(figsize=(16, 6))
        for i in range(len(x_range) - 1):
            ax.fill_between(x_range[i:i + 2], pdf_values[i:i + 2], color=colors[i], alpha=0.7)

        # Plot the normal distribution
        ax.plot(x_range, pdf_values, color='gray', linewidth=2)

        # Add vertical line for the z-score of the value
        ax.axvline(value, color='gray', linestyle='dashed')

        # Add text for the percentile
        ax.text(value, max(pdf_values) * 0.2,
                f'Top {100-percentile_value:.1f}% of the population.', color='black', ha='center', va='center', fontsize=30,
                bbox=dict(facecolor='white', edgecolor='gray', boxstyle='round,pad=0.5'))

        # Set axis limits and labels
        ax.set_ylim(0, max(pdf_values) * 1.1)
        ax.set_yticks([])
        ax.set_xticks([])

        # Save the plot to the specified directory
        output_dir = os.path.join(self.output_path, 'images')
        os.makedirs(output_dir, exist_ok=True)
        output_file = os.path.join(output_dir, f'{self.output_name}_placement_chart_{side}.png')
        plt.savefig(output_file)
        plt.close(fig)

        return output_file

    def generate_distance_chart(self, hand_data, maximums, minimums, start, end, side):
        # Ensure maximums and minimums are numpy arrays for easy indexing
        minimums = np.array(minimums)
        maximums = np.array(maximums)

        # Find indices of minimum values within hand_data
        min_indices = np.array(minimums)

        # Ensure the window includes at least 10 minimums
        if len(min_indices) >= 10:
            best_mean = -np.inf  # Initialize with a very low mean
            best_start, best_end = start, end

            # Find the best window containing 10 minimums
            for i in range(len(min_indices) - 9):
                window_start = min_indices[i]
                window_end = min_indices[i + 9]  # Ensures window contains 10 minimums

                # Get maximums within the current window
                window_max_indices = [idx for idx in maximums if window_start <= idx <= window_end]
                window_max_mean = np.mean([hand_data[idx] for idx in window_max_indices])  # Mean of max values in window

                # Update best window if the current mean is higher
                if window_max_mean > best_mean:
                    best_mean = window_max_mean
                    best_start, best_end = window_start, window_end

            # Set start and end to the best window found
            start, end = best_start, best_end
        else:
            # If fewer than 10 minimums, use the entire range of hand_data
            start, end = 0, len(hand_data) - 1

        # Generate the plot with the calculated window
        output_dir = os.path.join(self.output_path, 'images')
        os.makedirs(output_dir, exist_ok=True)
        file_path = os.path.join(output_dir, f'{self.output_name}_distance_chart_{side}.png')
        plot_time_series_start_end(
            os.path.basename(file_path), np.array(hand_data), start, end, x=None,
            title='Distance Over Time', mins=minimums, maxes=maximums,
            output_dir=output_dir, xlabel='Frame', ylabel='Distance'
        )
        return file_path




class PhotoSaver:
    def __init__(self, output_path, output_name):
        self.output_path = output_path
        self.output_name = output_name

    def save_frames(self, video_path, minimum_idx, maximum_idx, side):
        """
        Extracts and saves 1 frame with the minimum distance and 1 frame with the maximum distance,
        using wrist coordinates and hand bounding box to crop the hand and save in a square frame.
        
        :param video_path: Path to the video file.
        :param minimum_idx: Index of the frame with minimum distance.
        :param maximum_idx: Index of the frame with maximum distance.
        :param side: 'right' or 'left', indicating which hand.
        :param bbox_data: Data containing wrist coordinates and bounding box for each frame.
        """
        # Load the video
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            raise ValueError(f"Unable to open video file: {video_path}")
        
        # Create directory for saving frames
        frames_dir = os.path.join(self.output_path, f'images')
        os.makedirs(frames_dir, exist_ok=True)

        # Save the frame for the minimum distance
        min_frame_path = self._save_cropped_hand_frame(cap, minimum_idx, side, 'min', frames_dir)

        # Save the frame for the maximum distance3
        max_frame_path = self._save_cropped_hand_frame(cap, maximum_idx, side, 'max', frames_dir)

        # Release the video capture object
        cap.release()
        return max_frame_path, min_frame_path


    def _save_cropped_hand_frame(self, cap, frame_num, side, label, frames_dir):
        """
        Helper function to detect and crop the hand using MediaPipe Hand detection, then save the cropped hand.
        
        :param cap: VideoCapture object.
        :param frame_num: Frame number to save.
        :param side: 'right' or 'left'.
        :param label: Label for the frame (e.g., 'min', 'max').
        :param frames_dir: Directory to save the frames.
        """    
        mp_hands = mp.solutions.hands
        hands = mp_hands.Hands(static_image_mode=True, max_num_hands=1, min_detection_confidence=0.5)
        cap.set(cv2.CAP_PROP_POS_FRAMES, frame_num)
        ret, frame = cap.read()
        if ret:
            # Convert the frame to RGB as required by MediaPipe
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

            # Process the frame with MediaPipe to detect hands
            results = hands.process(frame_rgb)
            while not results.multi_hand_landmarks and frame_num < cap.get(cv2.CAP_PROP_FRAME_COUNT):
                #get next frame
                frame_num += 1
                cap.set(cv2.CAP_PROP_POS_FRAMES, frame_num)
                ret, frame = cap.read()
                frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                results = hands.process(frame_rgb)
                
            if results.multi_hand_landmarks:
                for hand_landmarks in results.multi_hand_landmarks:
                    # Get the bounding box around the detected hand
                    x_min = min([landmark.x for landmark in hand_landmarks.landmark])
                    x_max = max([landmark.x for landmark in hand_landmarks.landmark])
                    y_min = min([landmark.y for landmark in hand_landmarks.landmark])
                    y_max = max([landmark.y for landmark in hand_landmarks.landmark])

                    # Convert normalized coordinates to image pixel coordinates
                    h, w, _ = frame.shape
                    x_min = int(x_min * w)
                    x_max = int(x_max * w)
                    y_min = int(y_min * h)
                    y_max = int(y_max * h)

                    # Ensure the bounding box is square by adjusting width and height
                    box_width = x_max - x_min
                    box_height = y_max - y_min
                    max_side = max(box_width, box_height)
                    x_max = x_min + max_side
                    y_max = y_min + max_side

                    # Crop the hand region, ensuring it's a square
                    cropped_hand = frame[y_min:y_max, x_min:x_max]  

                    # Resize the cropped hand to ensure consistent output size (optional)
                    square_frame = cv2.resize(cropped_hand, (256, 256))
                    

                    # Save the square frame
                    frame_file = os.path.join(frames_dir, f'{side}_frame_{label}_{frame_num}.png')
                    cv2.imwrite(frame_file, square_frame)
                    return frame_file
            else:
                print(f"No hand detected in frame {frame_num}.")
                return None
        else:
            print(f"Warning: Could not read frame {frame_num} from video.")
            return None

class FingerTappingReportGenerator(BaseReportGenerator):
    
    def __init__(self, name, model: UPDRSModel, output_path, right_distances_path=None, left_distances_path=None,
                 right_features_path=None, left_features_path=None, right_video_path=None, left_video_path=None):
        super().__init__(output_path, name)
        self.data_folder = Path(self.data_folder) / 'finger_tapping'
        self.model = model
        self.features = {}
        self.images = {}

        # Paths
        self.right_distances_path = right_distances_path or self._default_path('right', 'distances')
        self.left_distances_path = left_distances_path or self._default_path('left', 'distances')
        self.right_features_path = right_features_path or self._default_path('right', 'features')
        self.left_features_path = left_features_path or self._default_path('left', 'features')

        # Data Readers
        self.right_hand_reader = TappingDataReader(self.right_distances_path, self.right_features_path)
        self.left_hand_reader = TappingDataReader(self.left_distances_path, self.left_features_path)
        self.right_distances_data, self.right_features_data = self.right_hand_reader.read_data_distance_feature()
        self.left_distances_data, self.left_features_data = self.left_hand_reader.read_data_distance_feature()

        # Processors
        self.feature_processor = FeatureProcessor(self.model)
        self.finger_tapping_chart_generator = ChartGenerator(self.output_path, self.output_name)
        
        self.labels_data = self.right_hand_reader.read_labels_data() 
        
        # Initialize PhotoSaver
        self.photo_saver = PhotoSaver(self.output_path, self.output_name)
        
        self.right_video_path = right_video_path
        self.left_video_path = left_video_path

    def generate_report(self):
        # Generate the report
        self.feature_generator()
        self.image_generator()
        return self.write_to_yaml()
    
    def get_interpretation(self, updrs_score):
        dict = {
            0: 'Normal, No problems.',
            1: 'Slight symptoms',
            2: 'Mild symptoms',
            3: 'Moderate symptoms',
            4: 'Severe symptoms',
        }
        return dict[int(updrs_score)]
    
    def feature_generator(self):
        right_features = self.feature_processor.extract_features(self.right_features_data,self.labels_data)
        right_features['interpretation'] = self.get_interpretation(right_features['updrs score'])
        self.features['right'] = right_features
        left_features = self.feature_processor.extract_features(self.left_features_data,self.labels_data)
        left_features['interpretation'] = self.get_interpretation(left_features['updrs score'])
        self.features['left'] = left_features

    def image_generator(self):
        right_percentile_chart = self.finger_tapping_chart_generator.generate_percentile_plot(self.labels_data, self.features['right']['updrs score'], 'right')
        left_percentile_chart = self.finger_tapping_chart_generator.generate_percentile_plot(self.labels_data, self.features['left']['updrs score'], 'left')

        finger_feature_extractor = FeatureExtractor()
        # Right Hand Chart
        data, maximums, minimums, start, end = finger_feature_extractor.find_peaks_finger_tapping(
            self.right_distances_data)
        right_hand_chart = self.finger_tapping_chart_generator.generate_distance_chart(data, maximums, minimums, start, end, 'right')
        
        # Save right-hand frames using PhotoSaver
        right_max_path, right_min_path = self.photo_saver.save_frames(self.right_video_path, minimums[-2], maximums[-2], 'right')


        # Left Hand Chart
        data, maximums, minimums, start, end = finger_feature_extractor.find_peaks_finger_tapping(
            self.left_distances_data)
        left_hand_chart =  self.finger_tapping_chart_generator.generate_distance_chart(data, maximums, minimums, start, end, 'left')
        
        # Save left-hand frames using PhotoSaver
        left_max_path , left_min_path = self.photo_saver.save_frames(self.left_video_path, minimums[-2], maximums[-2], 'left')
        
        self.images['right_distances'] = right_hand_chart
        self.images['left_distances'] = left_hand_chart
        self.images['right_percentile'] = right_percentile_chart
        self.images['left_percentile'] = left_percentile_chart
        self.images['left_max'] = left_max_path
        self.images['left_min'] = left_min_path
        self.images['right_max'] = right_max_path
        self.images['right_min'] = right_min_path
        
        
        

