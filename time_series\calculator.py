import numpy as np
from scipy.fftpack import fft
from pycatch22 import catch22_all
import os
import pandas as pd
import glob
from scipy import signal, stats
from scipy.signal import find_peaks
def calculate_statistics(data):
    signal_data = np.array(data)
    features = {        
        'amplitude': np.max(signal_data) - np.min(signal_data),
        'mean': np.mean(signal_data),
        'variance': np.var(signal_data),
        'standard_deviation': np.std(signal_data),
        'rms': np.sqrt(np.mean(signal_data ** 2)),
        'skewness': stats.skew(signal_data),
        'kurtosis': stats.kurtosis(signal_data),
        'median': np.median(signal_data),
        'max': np.max(signal_data),
        'min': np.min(signal_data),
    }
    return features

def calculate_catch22_features(data):
    
    features={}
    # Calculate catch22 features
    catch22_results = catch22_all(data)
    catch22_features = catch22_results['values']
    # Add catch22 features to the dictionary
    for idx, feature_name in enumerate(catch22_results['names']):
        features[f'catch22_{feature_name}'] = catch22_features[idx]

    return features

def calculate_time_series_statistics(name, data):
    
    stats = calculate_statistics(data)
    catch22_features = calculate_catch22_features(data)
    combine_dict = {'name': name, **stats, **catch22_features}
    return combine_dict

# Helper function to count peaks in time series
def count_peaks(au_series, height, distance):
    peaks, _ = find_peaks(au_series, height=height, distance=distance)
    return len(peaks)

# Helper function to count transitions from 0 to 1 in AU time series
def count_transaction(au_series):
    transitions = np.diff(au_series) == 1
    return np.sum(transitions)

        

def analyze_signal_shape(signal):
    # Convert the signal to a numpy array if it isn't one already
    signal = np.array(signal)
    
    # 1. Standard Deviation
    std_dev = np.std(signal)
    
    # 2. Kurtosis
    kurtosis = stats.kurtosis(signal)
    
    # 3. Skewness
    skewness = stats.skew(signal)
    
    # 4. Entropy (using a simple histogram-based approximation)
    histogram, bin_edges = np.histogram(signal, bins=30, density=True)
    histogram = histogram[np.nonzero(histogram)]
    entropy = -np.sum(histogram * np.log(histogram))
    
    # 5. Frequency Analysis using FFT
    frequencies = np.abs(fft(signal))
    
    # 6. Autocorrelation
    autocorr = np.correlate(signal - np.mean(signal), signal - np.mean(signal), mode='full')
    autocorr = autocorr[len(autocorr)//2:]
    
    # 7. Peak Analysis
    #smooth the signal
    signal_smooth =  signal
    signal_smooth = pd.Series(signal)
    signal_smooth =  signal_smooth.rolling(window=100, center=True).mean()
    peaks, _ = find_peaks(signal_smooth, height=20,prominence=1, width=100)
    peak_heights = signal_smooth[peaks]
    peak_count = len(peaks)
    if peak_count > 1:
        peak_distances = np.diff(peaks)
        avg_peak_distance = np.mean(peak_distances)
    else:
        avg_peak_distance = None

    signal_smooth =  signal
    
    #signal_smooth =  signal.rolling(window=20, center=True).mean()
    peaks_min, _ = find_peaks(-signal_smooth, height=-40,prominence=1, width=20)
    peak_heights_min = signal_smooth[peaks_min]
    peak_count_min = len(peaks_min)
    if peak_count_min > 1:
        peak_distances_min = np.diff(peaks_min)
        avg_peak_distance_min = np.mean(peak_distances_min)
    else:
        avg_peak_distance_min = None
        


    return {
        'signal_Standard Deviation': std_dev,
        'signal_number of samples above 60': len(signal[signal>60]),
        'signal_number of samples above 80': len(signal[signal>80]),
        'signal_mean area under curve': np.trapz(signal)/len(signal),
        'signal_Kurtosis': kurtosis,
        'signal_Skewness': skewness,
        'signal_Entropy': entropy,
        'signal_Frequencies': np.mean(frequencies),
        'signal_Autocorrelation': np.mean(autocorr),
        'signal_Peak Count': peak_count,
        'signal_Peak Count Min': peak_count_min,
        'signal_Peak Distance Min': avg_peak_distance_min,
        'signal_Peak Height Min': np.mean(peak_heights_min),
        'signal_Average Peak Distance': avg_peak_distance,
        'signal_Peak Heights': np.mean(peak_heights),
        'signal_Peak Std Dev': np.std(peaks),
        'signal_Peak Median': np.median(peaks)
    }


                                           