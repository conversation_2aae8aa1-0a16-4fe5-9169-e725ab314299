import cv2
import mediapipe as mp
import numpy as np
import math

# Initialize MediaPipe hands solution
mp_drawing = mp.solutions.drawing_utils
mp_hands = mp.solutions.hands

# Function to calculate angle between two vectors
def calculate_angle(v1, v2):
    dot_product = np.dot(v1, v2)
    magnitude_v1 = np.linalg.norm(v1)
    magnitude_v2 = np.linalg.norm(v2)
    cos_theta = dot_product / (magnitude_v1 * magnitude_v2)
    angle = np.arccos(np.clip(cos_theta, -1.0, 1.0))
    return np.degrees(angle)

# Function to calculate angle between thumb and index finger
def calculate_thumb_index_angle(landmarks):
    thumb_tip = np.array([landmarks[mp_hands.HandLandmark.THUMB_TIP].x, landmarks[mp_hands.HandLandmark.THUMB_TIP].y])
    index_tip = np.array([landmarks[mp_hands.HandLandmark.INDEX_FINGER_TIP].x, landmarks[mp_hands.HandLandmark.INDEX_FINGER_TIP].y])
    wrist = np.array([landmarks[mp_hands.HandLandmark.WRIST].x, landmarks[mp_hands.HandLandmark.WRIST].y])

    vector1 = thumb_tip - wrist
    vector2 = index_tip - wrist

    angle = calculate_angle(vector1, vector2)
    return angle

# Open video file
input_video_path = r'C:\Users\<USER>\Videos\finger.mp4'
output_video_path = r'C:\Users\<USER>\Videos\output_video_with_hands.mp4'
cap = cv2.VideoCapture(input_video_path)

# Get video properties
width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
fps = int(cap.get(cv2.CAP_PROP_FPS))

# Define video writer
fourcc = cv2.VideoWriter_fourcc(*'mp4v')
out = cv2.VideoWriter(output_video_path, fourcc, fps, (width, height))

with mp_hands.Hands(static_image_mode=False, max_num_hands=2, min_detection_confidence=0.5) as hands:
    while cap.isOpened():
        ret, frame = cap.read()
        if not ret:
            break

        # Convert the frame to RGB
        rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

        # Process the frame with MediaPipe Hands
        results = hands.process(rgb_frame)

        # Draw hand annotations on the frame
        if results.multi_hand_landmarks:
            for hand_landmarks in results.multi_hand_landmarks:
                # Draw the hand skeleton
                mp_drawing.draw_landmarks(frame, hand_landmarks, mp_hands.HAND_CONNECTIONS)

                # Calculate angle between thumb and index finger
                angle = calculate_thumb_index_angle(hand_landmarks.landmark)

                # Write the angle on the frame
                thumb_tip_coords = hand_landmarks.landmark[mp_hands.HandLandmark.THUMB_TIP]
                x = int(thumb_tip_coords.x * width)
                y = int(thumb_tip_coords.y * height)
                cv2.putText(frame, f'Angle: {int(angle)} deg', (x, y - 20), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (244, 111, 66), 2, cv2.LINE_AA)

        # Write the frame to the output video
        out.write(frame)

        # Show the frame (optional)
        cv2.imshow('Hand Tracking', frame)
        if cv2.waitKey(1) & 0xFF == ord('q'):
            break

# Release resources
cap.release()
out.release()
cv2.destroyAllWindows()
