#!/usr/bin/env python3
"""
Robust JSON extractor from CSV response column with multiple parsing strategies.
"""

import pandas as pd
import json
import re
import ast
from typing import List, Dict, Any


class JSONExtractor:
    """Class to extract JSON from text using multiple strategies."""
    
    def __init__(self):
        self.extraction_stats = {
            'total_processed': 0,
            'json_found': 0,
            'json_parsed': 0,
            'parsing_method': {}
        }
    
    def extract_json_objects(self, text: str) -> List[Dict]:
        """Extract JSON objects using multiple strategies."""
        if not text or pd.isna(text):
            return []
        
        self.extraction_stats['total_processed'] += 1
        text = str(text)
        
        json_objects = []
        
        # Strategy 1: Find balanced curly braces
        objects = self._find_balanced_braces(text)
        for obj_text in objects:
            parsed = self._try_parse_json(obj_text)
            if parsed:
                json_objects.extend(parsed)
        
        # Strategy 2: Regex patterns for common JSON structures
        if not json_objects:
            objects = self._find_regex_patterns(text)
            for obj_text in objects:
                parsed = self._try_parse_json(obj_text)
                if parsed:
                    json_objects.extend(parsed)
        
        # Strategy 3: Look for key-value patterns
        if not json_objects:
            objects = self._extract_key_value_patterns(text)
            if objects:
                json_objects.extend(objects)
        
        if json_objects:
            self.extraction_stats['json_found'] += 1
            self.extraction_stats['json_parsed'] += len(json_objects)
        
        return json_objects
    
    def _find_balanced_braces(self, text: str) -> List[str]:
        """Find text with balanced curly braces."""
        objects = []
        brace_count = 0
        start_pos = -1
        
        for i, char in enumerate(text):
            if char == '{':
                if brace_count == 0:
                    start_pos = i
                brace_count += 1
            elif char == '}':
                brace_count -= 1
                if brace_count == 0 and start_pos != -1:
                    objects.append(text[start_pos:i+1])
                    start_pos = -1
        
        return objects
    
    def _find_regex_patterns(self, text: str) -> List[str]:
        """Find JSON using regex patterns."""
        patterns = [
            r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}',  # Nested braces
            r'\{[^}]+\}',  # Simple braces
        ]
        
        objects = []
        for pattern in patterns:
            matches = re.findall(pattern, text, re.DOTALL)
            objects.extend(matches)
        
        return objects
    
    def _try_parse_json(self, text: str) -> List[Dict]:
        """Try multiple methods to parse JSON text."""
        methods = [
            ('direct', lambda x: [json.loads(x)]),
            ('cleaned', lambda x: [json.loads(self._clean_json_text(x))]),
            ('ast_literal', lambda x: [ast.literal_eval(x)] if isinstance(ast.literal_eval(x), dict) else []),
            ('manual_fix', lambda x: [self._manual_json_fix(x)]),
        ]
        
        for method_name, method_func in methods:
            try:
                result = method_func(text)
                if result and isinstance(result[0], dict):
                    self.extraction_stats['parsing_method'][method_name] = \
                        self.extraction_stats['parsing_method'].get(method_name, 0) + 1
                    return result
            except:
                continue
        
        return []
    
    def _clean_json_text(self, text: str) -> str:
        """Clean common JSON formatting issues."""
        # Remove trailing commas
        text = re.sub(r',(\s*[}\]])', r'\1', text)
        
        # Fix unquoted keys
        text = re.sub(r'(\w+)(\s*):', r'"\1"\2:', text)
        
        # Fix single quotes
        text = text.replace("'", '"')
        
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text)
        
        return text
    
    def _manual_json_fix(self, text: str) -> Dict:
        """Manually extract key-value pairs when JSON parsing fails."""
        # Extract key-value pairs using regex
        kv_pattern = r'["\']?(\w+)["\']?\s*:\s*["\']?([^,}]+)["\']?'
        matches = re.findall(kv_pattern, text)
        
        result = {}
        for key, value in matches:
            # Try to convert value to appropriate type
            value = value.strip().strip('"\'')
            try:
                # Try numeric conversion
                if '.' in value:
                    result[key] = float(value)
                else:
                    result[key] = int(value)
            except:
                result[key] = value
        
        return result if result else {}
    
    def _extract_key_value_patterns(self, text: str) -> List[Dict]:
        """Extract structured data even without proper JSON format."""
        # Look for patterns like "key: value" or "key = value"
        patterns = [
            r'(\w+)\s*[:=]\s*([^,\n]+)',
            r'["\'](\w+)["\']\s*[:=]\s*["\']?([^,\n"\']+)["\']?',
        ]
        
        result = {}
        for pattern in patterns:
            matches = re.findall(pattern, text)
            for key, value in matches:
                value = value.strip().strip('"\'')
                try:
                    if '.' in value:
                        result[key] = float(value)
                    else:
                        result[key] = int(value)
                except:
                    result[key] = value
        
        return [result] if result else []
    
    def get_stats(self) -> Dict:
        """Get extraction statistics."""
        return self.extraction_stats


def convert_responses_to_csv(
    input_csv: str,
    output_csv: str,
    response_column: str = 'response'
) -> None:
    """Convert JSON in CSV responses to structured CSV."""
    
    print(f"Reading CSV: {input_csv}")
    
    # Read input CSV
    try:
        df = pd.read_csv(input_csv, encoding='utf-8-sig')
        print(f"Loaded {len(df)} rows")
    except Exception as e:
        print(f"Error reading CSV: {e}")
        return
    
    # Check response column
    if response_column not in df.columns:
        print(f"Column '{response_column}' not found!")
        print(f"Available columns: {list(df.columns)}")
        
        # Try common alternatives
        alternatives = ['output', 'result', 'text', 'content']
        for alt in alternatives:
            if alt in df.columns:
                response_column = alt
                print(f"Using '{alt}' column instead")
                break
        else:
            return
    
    # Initialize extractor
    extractor = JSONExtractor()
    
    # Process each row
    all_records = []
    
    for idx, row in df.iterrows():
        response_text = row[response_column]
        file_name = row.get('file_name', f'row_{idx}')
        
        print(f"Processing {idx + 1}/{len(df)}: {file_name}")
        
        # Extract JSON objects
        json_objects = extractor.extract_json_objects(response_text)
        
        if json_objects:
            for i, json_obj in enumerate(json_objects):
                record = {
                    'source_row': idx,
                    'file_name': file_name,
                    'json_index': i,
                    'success': row.get('success', True),
                    'timestamp': row.get('timestamp', ''),
                }
                
                # Add JSON fields
                for key, value in json_obj.items():
                    # Handle nested structures
                    if isinstance(value, (dict, list)):
                        record[f'{key}_json'] = json.dumps(value)
                    else:
                        record[key] = value
                
                all_records.append(record)
        else:
            # No JSON found
            record = {
                'source_row': idx,
                'file_name': file_name,
                'json_index': 0,
                'success': row.get('success', False),
                'timestamp': row.get('timestamp', ''),
                'extraction_status': 'No JSON found'
            }
            all_records.append(record)
    
    # Create output DataFrame
    output_df = pd.DataFrame(all_records)
    
    # Save results
    try:
        output_df.to_csv(output_csv, index=False, encoding='utf-8-sig')
        print(f"\nSaved {len(output_df)} records to: {output_csv}")
    except Exception as e:
        print(f"Error saving CSV: {e}")
        return
    
    # Show statistics
    stats = extractor.get_stats()
    print(f"\nExtraction Statistics:")
    print(f"  Total rows processed: {stats['total_processed']}")
    print(f"  Rows with JSON found: {stats['json_found']}")
    print(f"  Total JSON objects parsed: {stats['json_parsed']}")
    print(f"  Parsing methods used: {stats['parsing_method']}")
    
    # Show sample results
    print(f"\nSample of extracted data:")
    print(output_df.head())
    
    print(f"\nOutput columns: {list(output_df.columns)}")


if __name__ == "__main__":
    # Configuration
    INPUT_CSV = r"G:\My Drive\Datasets\XAI\gpt-outputs.csv"
    OUTPUT_CSV = r"G:\My Drive\Datasets\XAI\structured_responses.csv"
    RESPONSE_COLUMN = "response"  # Change to "output" if needed
    
    # Convert responses to structured CSV
    convert_responses_to_csv(INPUT_CSV, OUTPUT_CSV, RESPONSE_COLUMN)
