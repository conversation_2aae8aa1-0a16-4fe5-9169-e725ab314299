import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy.spatial import distance

# Eye landmark indices
LEFT_EYE_EAR = [362, 380, 374, 263, 386, 385]
RIGHT_EYE_EAR = [33, 159, 158, 133, 153, 145]

def eye_aspect_ratio(eye):
    """Compute Eye Aspect Ratio (EAR) for an eye."""
    A = distance.euclidean(eye[1], eye[5])
    B = distance.euclidean(eye[2], eye[4])
    C = distance.euclidean(eye[0], eye[3])
    return (A + B) / (2.0 * C)

class EyeBlinkingsVideo:
    def __init__(self, landmarks_dir, save_doc, save_chart_dir, eye_ar_thresh=0.85, consec_frames=4, moving_avg_window=5, fps=60, static_threshold=0.5):
        """Initialize the EyeBlinkingsVideo class with parameters."""
        self.landmarks_dir = landmarks_dir
        self.save_doc = save_doc
        self.save_chart_dir = save_chart_dir
        self.eye_ar_thresh = eye_ar_thresh
        self.consec_frames = consec_frames
        self.moving_avg_window = moving_avg_window
        self.fps = fps  # Frames per second (default 60)
        self.static_threshold = static_threshold  # Static EAR threshold for blink detection
        
        # Ensure the directory for charts exists
        os.makedirs(self.save_chart_dir, exist_ok=True)

    def detect_blinks(self, landmarks_df, video_id):
        """Detect blinks in landmarks data and compute EAR statistics."""
        blink_counter = 0
        frame_counter = 0
        ear_values = []
        all_ear_values = []
        all_thresholds = []
        blink_frames = []
        min_ear = float('inf')
        max_ear = float('-inf')
        blink_durations = []
        
        for frame_idx, row in landmarks_df.iterrows():
            left_eye = [(row[f'Landmark_{i}_x'], row[f'Landmark_{i}_y']) for i in LEFT_EYE_EAR]
            right_eye = [(row[f'Landmark_{i}_x'], row[f'Landmark_{i}_y']) for i in RIGHT_EYE_EAR]
            left_ear = eye_aspect_ratio(left_eye)
            right_ear = eye_aspect_ratio(right_eye)
            ear = (left_ear + right_ear) / 2.0

            ear_values.append(ear)
            all_ear_values.append(ear)
            min_ear = min(min_ear, ear)
            max_ear = max(max_ear, ear)

            if len(ear_values) > self.moving_avg_window:
                ear_values.pop(0)

            smoothed_ear = np.mean(ear_values)
            dynamic_threshold = smoothed_ear * self.eye_ar_thresh
            all_thresholds.append(dynamic_threshold)

            if ear < dynamic_threshold and ear < self.static_threshold:
                frame_counter += 1
            else:
                if frame_counter >= self.consec_frames:
                    blink_counter += 1
                    blink_frames.append(frame_idx)
                    blink_duration = frame_counter / self.fps  # Convert frames to seconds
                    blink_durations.append(blink_duration)
                frame_counter = 0

        mean_blink_duration = np.mean(blink_durations) if blink_durations else 0
        
        # Save EAR chart
        #self.save_ear_chart(video_id, all_ear_values, all_thresholds, blink_counter, blink_frames)

        return blink_counter, min_ear, max_ear, mean_blink_duration
    
    def save_ear_chart(self, video_id, ear_values, thresholds, total_blinks, blink_frames):
        """Generate and save the EAR chart for a video with specific blink detection points."""
        plt.figure(figsize=(10, 5))
        plt.plot(ear_values, label='EAR', color='blue')
        plt.plot(thresholds, label='Dynamic Threshold', color='red', linestyle='--')
        plt.scatter(blink_frames, [ear_values[i] for i in blink_frames], color='green', marker='o', label='Blink Detected')
        plt.xlabel('Frame')
        plt.ylabel('EAR')
        plt.title(f'EAR Over Time - Video {video_id} (Total Blinks: {total_blinks})')
        plt.legend()
        plt.grid()
        
        save_path = os.path.join(self.save_chart_dir, f'{video_id}_ear_chart.png')
        plt.savefig(save_path)
        plt.close()
        print(f'EAR chart saved: {save_path}')

    def process_blinks(self):
        """Processes all landmarks files in the directory and detects blinks."""
        results = []

        for file_name in os.listdir(self.landmarks_dir):
            landmarks_file = os.path.join(self.landmarks_dir, file_name)
            if not landmarks_file.endswith('.csv'):
                continue

            landmarks_df = pd.read_csv(landmarks_file)
            video_id = file_name.split('_')[0]
            total_blinks, min_ear, max_ear, mean_blink_duration = self.detect_blinks(landmarks_df, video_id)

            results.append({
                'ID': video_id,
                'Blinks': total_blinks,
                'Min_EAR': min_ear,
                'Max_EAR': max_ear,
                'Mean_Blink_Duration': mean_blink_duration,
                'Duration (s)': len(landmarks_df) / self.fps
            })

        # Save results to a new CSV file
        results_df = pd.DataFrame(results)
        results_df.to_csv(self.save_doc, index=False)
        print(f"Results saved to {self.save_doc}")

if __name__ == '__main__':
    landmarks_dir = r'\\files.ubc.ca\team\PPRC\CAMERA\Booth_Processed\finger_tapping\right\mediapipe_face_landmarks'
    save_doc = r'\\files.ubc.ca\team\PPRC\CAMERA\Booth_Processed\finger_tapping\right\csvs\eye_blinkings.csv'
    save_chart_dir = r'\\files.ubc.ca\team\PPRC\CAMERA\Booth_Processed\finger_tapping\right\blinking-charts'
    
    # eye_blinkings = EyeBlinkingsVideo(landmarks_dir, save_doc, save_chart_dir, fps=60, eye_ar_thresh=0.85, consec_frames=4, moving_avg_window=5, static_threshold=0.5)
    # eye_blinkings.process_blinks()
    
    # #Evaluate_model
    # df_eb_neutral = pd.read_csv(r'\\files.ubc.ca\team\PPRC\CAMERA\Booth_Processed\finger_tapping\right\csvs\eye_blinkings.csv')
    # df_eb_neutral.drop_duplicates(subset=['ID'],inplace=True,keep='first')
    # df_eb_neutral.set_index('ID', inplace=True)
    # df_eb_neutral['blink_rate'] = (df_eb_neutral['Blinks'])/df_eb_neutral['Duration (s)']

    # df_true_blinking = pd.read_csv(r'\\files.ubc.ca\team\PPRC\CAMERA\Booth_Processed\finger_tapping\right\csvs\booth_right_blinking.csv')
    # df_true_blinking.set_index('ID', inplace=True)
    # df_combinned_true = pd.concat([df_true_blinking, df_eb_neutral], axis=1, join='inner')
    # df_combinned_true['diff'] = df_combinned_true['number of blinks'] - df_combinned_true['Blinks']
    # sum = np.sum(np.abs(df_combinned_true['diff']))
    # print(sum)
    import os
    import pandas as pd
    import numpy as np
    from itertools import product

    # Load true blinking data
    true_blinking_path = r'\\files.ubc.ca\team\PPRC\CAMERA\Booth_Processed\finger_tapping\right\csvs\booth_right_blinking.csv'
    detected_blinking_path = r'\\files.ubc.ca\team\PPRC\CAMERA\Booth_Processed\finger_tapping\right\csvs\eye_blinkings.csv'

    df_true_blinking = pd.read_csv(true_blinking_path)
    df_true_blinking.set_index('ID', inplace=True)

    # Define parameter search space
    eye_ar_thresh_values = np.linspace(0.8, 0.9, 5)  # Search around 0.85
    consec_frames_values = range(3, 6)  # Search around 4
    moving_avg_window_values = range(4, 7)  # Search around 5
    static_threshold_values = np.linspace(0.35, 0.55, 5)  # Search around 0.5

    # Store results
    best_params = None
    best_score = float('inf')

    # Perform grid search
    for eye_ar_thresh, consec_frames, moving_avg_window, static_threshold in product(
        eye_ar_thresh_values, consec_frames_values, moving_avg_window_values, static_threshold_values):
        eye_blinkings = EyeBlinkingsVideo(landmarks_dir, save_doc, save_chart_dir, fps=60, eye_ar_thresh=eye_ar_thresh, consec_frames=consec_frames, moving_avg_window=moving_avg_window, static_threshold=static_threshold)
        eye_blinkings.process_blinks()

        # Load detected blinking data
        df_eb_neutral = pd.read_csv(detected_blinking_path)
        df_eb_neutral.drop_duplicates(subset=['ID'], inplace=True, keep='first')
        df_eb_neutral.set_index('ID', inplace=True)

        # Merge with true data
        df_combined = pd.concat([df_true_blinking, df_eb_neutral], axis=1, join='inner')
        df_combined['diff'] = df_combined['number of blinks'] - df_combined['Blinks']

        # Compute score (absolute sum of differences)
        score = np.sum(np.abs(df_combined['diff']))

        # Update best parameters
        if score < best_score:
            best_score = score
            best_params = {
                'eye_ar_thresh': eye_ar_thresh,
                'consec_frames': consec_frames,
                'moving_avg_window': moving_avg_window,
                'static_threshold': static_threshold
            }
            print(best_params, best_score)

    print(best_params, best_score)


