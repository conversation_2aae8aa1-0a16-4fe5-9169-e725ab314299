import sys
sys.path.insert(0, 'd:\\Atefeh\\Codes\\Python\\ParkinsonAssessment')
import os
import cv2
from action_unit import VideoAUsAnalyzer
from emotion import ImageFrameEmotionExtractor
#from face import Face
import pandas as pd
from multiprocessing import Pool

from face_processor.open_face import OpenFace

def crop_and_align_face(video_path, video_name, output_directory):

    video_name = os.path.splitext(os.path.basename(video_path))[0]
    output_folder = os.path.join(output_directory, video_name)
        

    if not os.path.exists(output_folder):
        os.mkdir(output_folder)
    else:
        return

    cap = cv2.VideoCapture(video_path)

    frame_count = 0
    while True:
        ret, frame = cap.read()
        if not ret:
            break

        try:
                # Process the frame
            face = Face(frame)
            aligned_face = face.align_face()

            # Save the aligned face
            output_image_path = os.path.join(output_folder, f'frame_{frame_count}.jpg')

            # Assume aligned_face is in the format compatible with cv2
            cv2.imwrite(output_image_path, aligned_face)

        except Exception as e:
            print(f"Error processing frame {frame_count} in video {video_name}: {e}")

        frame_count += 1

    cap.release()

    cv2.destroyAllWindows()



def process_frame(frame_data):
    frame_count, frame = frame_data
    try:
        face = OpenFace(frame)
        landmarks = face.landmarks
        return [frame_count] + [coord for point in landmarks for coord in point]
    except Exception as e:
        print(f"Error processing frame {frame_count}: {e}")
        return None

def extract_landmarks(video_path, video_name, output_csv_folder):
    video_name = os.path.splitext(os.path.basename(video_path))[0]
    landmarks_file = os.path.join(output_csv_folder, f'{video_name}_landmarks.csv')
    
    if os.path.exists(landmarks_file):
        return landmarks_file
    
    cap = cv2.VideoCapture(video_path)

    frame_count = 0
    frames = []
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        frames.append((frame_count, frame))
        frame_count += 1

    cap.release()
    cv2.destroyAllWindows()

    with Pool(15) as pool:
        landmarks_data = pool.map(process_frame, frames)

    # Filter out None values
    landmarks_data = [landmark for landmark in landmarks_data if landmark is not None]

    # Save landmarks to CSV
    landmarks_df = pd.DataFrame(landmarks_data, columns=['Frame'] + [f'Landmark_{i}_{axis}' for i in range(68) for axis in ['x', 'y']])
    landmarks_df.to_csv(landmarks_file, index=False)

    return landmarks_file

def extract_action_units(video_path,video_name, output_csv_folder):
    video_name =  video_name.split('.')[0]
    output_path = os.path.join(output_csv_folder, f'{video_name}_aus.csv')
    if os.path.exists(output_path):
        return output_path
    AUAnalyzer = VideoAUsAnalyzer(video_path, output_path)
    AUAnalyzer.extract_action_units()
    return output_path
    

def extract_emotions(images_folder, video_name, output_csv):
    if not os.path.exists(images_folder):
        return ''
    video_name =  video_name.split('.')[0]
    output_path = os.path.join(output_csv, f'{video_name}_emotions.csv')
    frames_path = os.path.join(images_folder, video_name)
    if os.path.exists(output_path):
        return output_path
    emotions = ImageFrameEmotionExtractor(frames_path, output_path)
    emotions.extract_emotions()
    return output_path
    
def make_sure_exist(folder):
    if not os.path.exists(folder):
        os.mkdir(folder)
    return folder

       
# Define constants for emotions, tasks, and folders
EMOTIONS = ['disgust', 'angry', 'happy', 'sad']
FACIAL_TASKS = ['imitation', 'text']
NEUTRAL = ['neutral']
FINGER_TASKS = ['left', 'right']
ORIGIN_FOLDER = '//files.ubc.ca/team/PPRC/Camera/Booth_Processed/'
FACIAL_ORIGIN_FOLDER = f'{ORIGIN_FOLDER}/facial_expression/'
FINGER_ORIGIN_FOLDER = f'{ORIGIN_FOLDER}/finger_tapping/'
HAND_TASKS = [ 'left_up_down', 'right_up_down','left_open_close', 'right_open_close',
              'both_still']
HAND_ORIGIN_FOLDER = f'{ORIGIN_FOLDER}/hand_movement/'

def process_videos(origin_folder, tasks, process_function):
    for task in tasks:
        video_folder = os.path.join(origin_folder, task, 'videos')
        output_folders = {
            'frames': make_sure_exist(os.path.join(origin_folder, task, 'frames')),
            'action_units': make_sure_exist(os.path.join(origin_folder, task, 'action_units')),
            'emotions': make_sure_exist(os.path.join(origin_folder, task, 'emotions')),
            'landmarks': make_sure_exist(os.path.join(origin_folder, task, 'landmarks'))
        }

        for video_file in os.listdir(video_folder):
            if not video_file.endswith(('.mp4', '.avi')):
                continue
            video_path = os.path.join(video_folder, video_file)
            process_function(video_path, video_file, output_folders)
            print(f'Video processed: {video_path}')

def process_facial_expression(video_path, video_file, output_folders):
    extract_action_units(video_path, video_file, output_folders['action_units'])
    #crop_and_align_face(video_path, video_file, output_folders['frames'])
    images_folder = os.path.join(output_folders['frames'], os.path.splitext(video_file)[0])
    #extract_emotions(output_folders['frames'], video_file, output_folders['emotions'])
    extract_landmarks(video_path, video_file, output_folders['landmarks'])

def process_finger_tapping(video_path, video_file, output_folders):
    #extract_action_units(video_path, video_file, output_folders['action_units'])
    extract_landmarks(video_path, video_file, output_folders['landmarks'])
    
def process_hand_movement(video_path, video_file, output_folders):
    extract_landmarks(video_path, video_file, output_folders['landmarks'])

if __name__ == '__main__':\
    #TODO: Add blinking rate calculation
    #Process facial expression videos
    # for emotion in EMOTIONS:
    #     process_videos(os.path.join(FACIAL_ORIGIN_FOLDER, emotion), FACIAL_TASKS, process_facial_expression)

    # #Process neutral videos   
    # process_videos(FACIAL_ORIGIN_FOLDER, NEUTRAL, process_facial_expression)

    # #Process finger tapping videos
    #process_videos(FINGER_ORIGIN_FOLDER, FINGER_TASKS, process_finger_tapping)   
    
    #process hand movement videos
    #process_videos(HAND_ORIGIN_FOLDER, HAND_TASKS, process_finger_tapping)    
        