from openai import OpenAI

api_key = '********************************************************************************************************************************************************************'
client = OpenAI(api_key=api_key)

# Function to create a file with the Files API
def create_file(file_path):
  with open(file_path, "rb") as file_content:
    result = client.files.create(
        file=file_content,
        purpose="vision",
    )
    return result.id

# Getting the file ID
file_id = create_file(r"N:\Booth_Results\reports\users\19091\finger_tapping\images\19091_20240923_distance_chart_left.png")
template = r"""Analyze this finger-tapping distance plot (normalized thumb–index finger distance over time, 60 fps) for a Parkinson's patient.Investigate the presence or absence of the following motor symptoms based on the plot: Amplitude decrement,Low speed,Bradykinesia (based on MDS-UPDRS criteria: slowness + decrement or hesitation),Low amplitude,Halts or hesitations,Whether the task is completed (at least 10 taps),Tremor (if visible in the plot),Rhythm irregularity For each symptom, clearly state whether it is present, mild, or absent, and give a short explanation with frame-based or time-based references.Conclude with a summary table showing each symptom and its status, and provide an overall clinical conclusion note and an MDS-UPDRS finger tapping score (0–4) based on the observations.FYI, an ML model predicts a UPDRS score of 3 and amplitude of 0, decreases amplitude by 1 and slowness by  1 (not mention to model in your response)provide a json including id, hand, symptoms and their severity at end. Follow this template for result:
Finger Tapping Analysis Summary
Patient ID:
Date:
Side Assessed:
Estimated UPDRS:
Motor Symptom Assessment
Symptom
Status
Explanation
Clinical Note"""


response = client.responses.create(
    model="o3-2025-04-16",
    input=[{
        "role": "user",
        "content": [
            {"type": "input_text", "text": template},
            {
                "type": "input_image",
                "file_id": file_id,
            },
        ],
    }],
)

print(response.output_text)


