import os
from pathlib import Path
from typing import List, Dict, Optional

class DatasetLoader:
    """
    A class to load and manage raw data for assessment.
    """
    
    def __init__(self, base_path: str):
        """
        Initializes the DataLoader with the base directory path.
        
        :param base_path: The root directory where raw data is stored.
        """
        self.base_path = Path(base_path)
        if not self.base_path.exists():
            raise FileNotFoundError(f"The base path {self.base_path} does not exist.")
    
    def get_dates(self, user_id: str) -> List[str]:
        """
        Retrieves all available recording dates for a given user.
        
        :param user_id: The UserBoothID.
        :return: A list of dates as strings.
        """
        user_path = self.base_path / user_id
        if not user_path.exists():
            raise FileNotFoundError(f"User ID {user_id} does not exist in the data directory.")
        
        dates = [date.name for date in user_path.iterdir() if date.is_dir()]
        return sorted(dates)
    
    def get_tasks(self, user_id: str, date = None) -> List[str]:
        """
        Retrieves all available tasks for a specific user and date. if date is null retrieves last tasks for the user.
        
        :param user_id: The UserBoothID.
        :param date: The Date-of-recording.
        :return: A list of task names.
        """
        if date is None:
            date = sorted(self.get_dates(user_id))[-1]
            
        date_path = self.base_path / user_id / date
        
        if not date_path.exists():
            raise FileNotFoundError(f"Date {date} does not exist for user {user_id}.")
        
        tasks = [task.name for task in date_path.iterdir() if task.is_dir()]
        return sorted(tasks)
    
    def get_videos(self, user_id: str, date: str, task: str, subtask: Optional[str] = None) -> List[Path]:
        """
        Retrieves video file paths for a specific task and optionally a subtask.
        
        :param user_id: The UserBoothID.
        :param date: The Date-of-recording.
        :param task: The task name (e.g., 'facial-expression').
        :param subtask: The subtask name (optional, e.g., 'neutral').
        :return: A list of Path objects pointing to video files.
        """
        task_path = self.base_path / user_id / date / task
        if not task_path.exists():
            raise FileNotFoundError(f"Task {task} does not exist for user {user_id} on date {date}.")
        
        videos = []
        if subtask:
            # Assuming subtask corresponds to the video filename without extension
            expected_filename = f"{subtask}.mp4"
            video_file = task_path / expected_filename
            if video_file.exists():
                videos.append(video_file)
            else:
                raise FileNotFoundError(f"Subtask {subtask} does not exist as {expected_filename} in task {task}.")
        else:
            # Retrieve all video files in the task directory
            videos = [file for file in task_path.glob("*.mp4") if file.is_file()]
        
        return sorted(videos)
    
    def list_users(self) -> List[str]:
        """
        Lists all UserBoothIDs available in the data directory.
        
        :return: A list of UserBoothIDs.
        """
        users = [user.name for user in self.base_path.iterdir() if user.is_dir()]
        return sorted(users)
    
    def list_subtasks(self, user_id: str, date: str, task: str) -> List[str]:
        """
        Lists all subtasks available for a specific task.
        
        :param user_id: The UserBoothID.
        :param date: The Date-of-recording.
        :param task: The task name.
        :return: A list of subtask names derived from video filenames.
        """
        task_path = self.base_path / user_id / date / task
        if not task_path.exists():
            raise FileNotFoundError(f"Task {task} does not exist for user {user_id} on date {date}.")
        
        subtasks = [file.stem for file in task_path.glob("*.mp4") if file.is_file()]
        return sorted(subtasks)

    def get_subtask(self, user_id: str, date: str, task: str, subtask: str) -> str:
        """
        Retrieves the video file path for a specific subtask.
        
        :param user_id: The UserBoothID.
        :param date: The Date-of-recording.
        :param task: The task name.
        :param subtask: The subtask name.
        :return: A Path object pointing to the video file.
        """
        task_path = self.base_path / user_id / date / task
        if not task_path.exists():
            raise FileNotFoundError(f"Task {task} does not exist for user {user_id} on date {date}.")
        
        video_file = task_path / f"{subtask}.mp4"
        if video_file.exists():
            return video_file
        else:
            return None
        
# Example Usage
if __name__ == "__main__":
    # Initialize the DataLoader with the base directory
    base_directory = "CAMERA Booth Data/Booth"
    data_loader = DatasetLoader(base_directory)
    
    # Example UserBoothID
    user_id = "User12345"
    
    try:
        # Get all available dates for the user
        dates = data_loader.get_dates(user_id)
        print(f"Available dates for {user_id}: {dates}")
        
        # Choose a specific date (e.g., the first available date)
        if dates:
            selected_date = dates[0]
            print(f"Selected date: {selected_date}")
            
            # Get all tasks available on the selected date
            tasks = data_loader.get_tasks(user_id, selected_date)
            print(f"Available tasks on {selected_date}: {tasks}")
            
            # Choose a specific task (e.g., 'facial-expression')
            if tasks:
                selected_task = tasks[0]
                print(f"Selected task: {selected_task}")
                
                # List all subtasks for the selected task
                subtasks = data_loader.list_subtasks(user_id, selected_date, selected_task)
                print(f"Available subtasks in {selected_task}: {subtasks}")
                
                # Choose a specific subtask (e.g., 'neutral')
                if subtasks:
                    selected_subtask = subtasks[0]
                    print(f"Selected subtask: {selected_subtask}")
                    
                    # Get the video file for the specific subtask
                    videos = data_loader.get_videos(user_id, selected_date, selected_task, selected_subtask)
                    print(f"Video files for {selected_subtask}: {[str(video) for video in videos]}")
    except FileNotFoundError as e:
        print(e)
