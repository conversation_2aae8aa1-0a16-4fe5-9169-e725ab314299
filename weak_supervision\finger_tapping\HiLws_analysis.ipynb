{"cells": [{"cell_type": "code", "execution_count": 110, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_63796\\3489792395.py:16: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  df_right['hand'] = 'right'\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_63796\\3489792395.py:17: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  df_left['hand'] = 'left'\n"]}], "source": ["import pandas as pd\n", "import os\n", "# read csv file for finger tapping labels\n", "file_location = r'\\\\files.ubc.ca\\team\\PPRC\\CAMERA\\Booth_Processed\\finger_tapping\\docs\\combined_v2.csv'\n", "df_labels = pd.read_csv(file_location)\n", "df_labels['Date'] = pd.to_datetime(df_labels['Date'], format='%d-%b-%y').dt.strftime('%Y%m%d')\n", "df_labels.set_index(['ID','Date'], inplace=True)\n", "#remove Collection_UPDRS_ from the column names\n", "df_labels.columns = df_labels.columns.str.replace('Collection_UPDRS_', '')\n", "\n", "\n", "df_right = df_labels[df_labels.columns[df_labels.columns.str.contains('Right', case=False)]]\n", "df_left = df_labels[df_labels.columns[df_labels.columns.str.contains('Left', case=False)]]\n", "df_right.columns = df_right.columns.str.replace('Right_', '')\n", "df_left.columns = df_left.columns.str.replace('Left_', '')\n", "df_right['hand'] = 'right'\n", "df_left['hand'] = 'left'\n", "\n", "\n", "df_union = pd.concat([df_right, df_left], axis=0)\n", "#add hand to index \n", "df_union = df_union.reset_index()\n", "df_union.set_index(df_union['ID'].astype(str) + '_' + df_union['Date'].astype(str) + '_' + df_union['hand'], inplace=True)\n", "df_union.drop(columns=['ID','Date','hand'], inplace=True)\n", "\n", "#numerical columns\n", "df_union = df_union.apply(pd.to_numeric)\n", "df_union['single'] = df_union['MG']\n", "#apply majority vote to the columns\n", "df_union['majority'] = df_union[['KW', 'MG', 'TM', 'SA','WM']].mode(axis=1)[0]\n", "\n", "#drop all missing rows\n", "df_union.dropna(axis=0, how='all', inplace=True)\n", "def modify_string(s):\n", "    parts = s.split('_')\n", "    if len(parts) > 1 and parts[0].isdigit() and len(parts[0]) == 4:\n", "        parts[0] = '0' + parts[0]\n", "    return '_'.join(parts)\n", "\n", "#add 0 to y index if it is split by _, firt part is 4 digit\n", "df_union.index =df_union.index.map(modify_string)\n"]}, {"cell_type": "code", "execution_count": 117, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:root:Computing O...\n", "INFO:root:Estimating \\mu...\n", "  0%|          | 0/500 [00:00<?, ?epoch/s]INFO:root:[0 epochs]: TRAIN:[loss=0.689]\n", " 18%|█▊        | 92/500 [00:00<00:00, 919.28epoch/s]INFO:root:[100 epochs]: TRAIN:[loss=0.018]\n", " 38%|███▊      | 190/500 [00:00<00:00, 947.95epoch/s]INFO:root:[200 epochs]: TRAIN:[loss=0.013]\n", " 57%|█████▋    | 285/500 [00:00<00:00, 940.36epoch/s]INFO:root:[300 epochs]: TRAIN:[loss=0.011]\n", " 78%|███████▊  | 392/500 [00:00<00:00, 990.81epoch/s]INFO:root:[400 epochs]: TRAIN:[loss=0.010]\n", "100%|██████████| 500/500 [00:00<00:00, 1001.04epoch/s]\n", "INFO:root:Finished Training\n"]}], "source": ["#snorkel weak supervision\n", "import pandas as pd\n", "from snorkel.labeling.model.label_model import LabelModel \n", "\n", "\n", "# Constants\n", "ABSTAIN = -1  # Use this if some of your LFs abstain\n", "\n", "# Step 1: Create L matrix from the 5 LF columns\n", "# Assume df_union columns are exactly the weak labels\n", "L_train = df_union.fillna(ABSTAIN).iloc[:, :5].astype(float).values\n", "\n", "\n", "# Step 2: Fit the Label Model\n", "label_model = LabelModel(cardinality=5, verbose=True)  # change to 3 if multiclass\n", "label_model.fit(L_train=L_train, n_epochs=500, log_freq=100, seed=123)\n", "\n", "# Step 3: Predict labels (hard and probabilistic)\n", "df_union[\"snorkel_label\"] = label_model.predict(L=L_train)\n", "df_union[\"snorkel_prob_0\"] = label_model.predict_proba(L=L_train)[:, 0]  # probability of class 0\n", "df_union[\"snorkel_prob_1\"] = label_model.predict_proba(L=L_train)[:, 1]  # probability of class 1\n", "df_union[\"snorkel_prob_2\"] = label_model.predict_proba(L=L_train)[:, 2]  # probability of class 2\n", "df_union[\"snorkel_prob_3\"] = label_model.predict_proba(L=L_train)[:, 3]  # probability of class 3\n", "df_union[\"snorkel_prob_4\"] = label_model.predict_proba(L=L_train)[:, 4]  # probability of class 4\n", "\n", "# df_union.reset_index(inplace=True)\n", "# df_union.rename(columns={'index': 'ID'}, inplace=True)\n", "#df_union.to_csv(r'\\\\files.ubc.ca\\team\\PPRC\\CAMERA\\Booth_Processed\\finger_tapping\\docs\\weak_supervision.csv', index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'df_updrs' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[37], line 8\u001b[0m\n\u001b[0;32m      6\u001b[0m df_severity \u001b[38;5;241m=\u001b[39m pd\u001b[38;5;241m.\u001b[39mread_csv(\u001b[38;5;124mr\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[38;5;124mfiles.ubc.ca\u001b[39m\u001b[38;5;124m\\\u001b[39m\u001b[38;5;124mteam\u001b[39m\u001b[38;5;124m\\\u001b[39m\u001b[38;5;124mPPRC\u001b[39m\u001b[38;5;124m\\\u001b[39m\u001b[38;5;124mCamera\u001b[39m\u001b[38;5;124m\\\u001b[39m\u001b[38;5;124mBooth_Results\u001b[39m\u001b[38;5;124m\\\u001b[39m\u001b[38;5;124mfinger_tapping_ws\u001b[39m\u001b[38;5;124m\\\u001b[39m\u001b[38;5;124mExperiment2\u001b[39m\u001b[38;5;124m\\\u001b[39m\u001b[38;5;124mcsvs\u001b[39m\u001b[38;5;124m\\\u001b[39m\u001b[38;5;124mpredicted_labels_severity.csv\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[0;32m      7\u001b[0m df_classifier \u001b[38;5;241m=\u001b[39m pd\u001b[38;5;241m.\u001b[39mread_csv(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mcomplete_predicted_labels_loocv.csv\u001b[39m\u001b[38;5;124m'\u001b[39m)\u001b[38;5;241m.\u001b[39mset_index(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mFile_Name\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[1;32m----> 8\u001b[0m df_L \u001b[38;5;241m=\u001b[39m df_updrs\u001b[38;5;241m.\u001b[39mcopy()\n\u001b[0;32m      9\u001b[0m \u001b[38;5;66;03m#drop duplicate file_name where other values are not available\u001b[39;00m\n\u001b[0;32m     10\u001b[0m df_L\u001b[38;5;241m.\u001b[39mdrop_duplicates(subset\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mfile_name\u001b[39m\u001b[38;5;124m'\u001b[39m, inplace\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m)\n", "\u001b[1;31mNameError\u001b[0m: name 'df_updrs' is not defined"]}], "source": ["#combine L_file wth classifier and severiy score\n", "import pandas as pd\n", "import numpy as np\n", "\n", "\n", "df_severity = pd.read_csv(r'\\\\files.ubc.ca\\team\\PPRC\\Camera\\Booth_Results\\finger_tapping_ws\\Experiment2\\csvs\\predicted_labels_severity.csv')\n", "df_classifier = pd.read_csv('complete_predicted_labels_loocv.csv').set_index('File_Name')\n", "df_L = df_union.copy()\n", "#drop duplicate file_name where other values are not available\n", "df_L.drop_duplicates(subset='file_name', inplace=True)\n", "df_L = df_L.set_index('file_name')\n", "\n", "df_L['classifier'] = df_classifier['Predicted_Label']\n", "df_severity['file_name'] = df_severity['Patient_ID']+'_finger_tapping_distances.csv'\n", "df_severity = df_severity.drop(columns=['Patient_ID']).set_index('file_name')\n", "\n", "df_L = df_L.join(df_severity)\n", "df_L.rename(columns={'Predicted_Label':'severity model'}, inplace=True)\n", "df_L['severity model'] = df_L['severity model'].round(0)\n", "df_L.to_csv(L_file, index=True)"]}, {"cell_type": "code", "execution_count": 112, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\assessment\\Lib\\site-packages\\sklearn\\feature_selection\\_univariate_selection.py:112: UserWarning: Features [19] are constant.\n", "  warnings.warn(\"Features %s are constant.\" % constant_features_idx, UserWarning)\n", "c:\\Users\\<USER>\\.conda\\envs\\assessment\\Lib\\site-packages\\sklearn\\feature_selection\\_univariate_selection.py:113: RuntimeWarning: invalid value encountered in divide\n", "  f = msb / msw\n", "433it [08:29,  1.18s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Confusion Matrix:\n", "[[48 21 11  2  0]\n", " [18 38 26 10  4]\n", " [ 7 22 39 28  3]\n", " [ 3 11 25 68 13]\n", " [ 1  3  2 16 14]]\n", "\n", "Classification Report:\n", "              precision    recall  f1-score   support\n", "\n", "         0.0       0.62      0.59      0.60        82\n", "         1.0       0.40      0.40      0.40        96\n", "         2.0       0.38      0.39      0.39        99\n", "         3.0       0.55      0.57      0.56       120\n", "         4.0       0.41      0.39      0.40        36\n", "\n", "    accuracy                           0.48       433\n", "   macro avg       0.47      0.47      0.47       433\n", "weighted avg       0.48      0.48      0.48       433\n", "\n", "\n", "Accuracy Score:\n", "0.4780600461893764\n", "\n", "Mean Absolute Error (MAE): 0.6859122401847575\n", "Mean Squared Error (MSE): 1.0831408775981524\n", "Model, feature selector, and all predicted labels saved successfully.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["#train random forest classifier\n", "import numpy as np\n", "import pandas as pd\n", "from sklearn.model_selection import LeaveOneOut\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.metrics import classification_report, confusion_matrix, accuracy_score, mean_absolute_error, mean_squared_error\n", "from sklearn.feature_selection import SelectKBest, f_classif\n", "from imblearn.over_sampling import SMOTE\n", "import joblib\n", "from tqdm import tqdm\n", "\n", "# Load the data\n", "file_l = r'\\\\files.ubc.ca\\team\\PPRC\\Camera\\Booth_Results\\finger_tapping_ws\\Experiment2\\csvs\\left_features_extracted_finger_distance.csv'\n", "file_r = r'\\\\files.ubc.ca\\team\\PPRC\\Camera\\Booth_Results\\finger_tapping_ws\\Experiment2\\csvs\\right_features_extracted_finger_distance.csv'\n", "df_l_r = pd.concat([pd.read_csv(file_l), pd.read_csv(file_r)])\n", "file1 = r'\\\\files.ubc.ca\\team\\PPRC\\CAMERA\\Booth_Processed\\finger_tapping\\docs\\weak_supervision.csv'\n", "df_updrs = pd.read_csv(file1)[['ID','snorkel_label']]\n", "df_updrs.rename(columns={'snorkel_label':'UPDRS'}, inplace=True)\n", "df_l_r['ID'] = df_l_r['file_name'].str.split('_finger').str[0]\n", "df_l_r.drop(columns=['file_name'], inplace=True)\n", "\n", "df = pd.merge(df_l_r, df_updrs, on='ID', how='left')\n", "df = df.dropna(subset=['UPDRS'])\n", "\n", "# Define the features and target\n", "X = df.drop(columns=['ID', 'UPDRS'])\n", "y = df['UPDRS']\n", "<PERSON><PERSON>fillna(0, inplace=True)\n", "file_names = df['ID']\n", "\n", "# Feature selection\n", "selector = SelectKBest(score_func=f_classif, k=20)\n", "selector.fit(X, y)\n", "X_selected = selector.transform(X)\n", "\n", "# Initialize the leave-one-out cross-validator\n", "loo = LeaveOneOut()\n", "smote = SMOTE( random_state=42)\n", "\n", "# Initialize lists to store results\n", "all_preds = []\n", "all_labels = []\n", "all_file_names = []\n", "\n", "# Loop over each split\n", "for train_index, test_index in tqdm(loo.split(X_selected)):\n", "    X_train, X_test = X_selected[train_index], X_selected[test_index]\n", "    y_train, y_test = y.iloc[train_index], y.iloc[test_index]\n", "    file_name_test = file_names.iloc[test_index].values[0]\n", "    \n", "    # Apply SMOTE to the training data\n", "    X_train_resampled, y_train_resampled = smote.fit_resample(X_train, y_train)\n", "\n", "    # Initialize and train the classifier\n", "    clf = RandomForestClassifier(random_state=42, n_estimators=500, max_depth=12)\n", "    clf.fit(X_train_resampled, y_train_resampled)\n", "\n", "    # Make prediction for the test instance\n", "    y_pred = clf.predict(X_test)\n", "\n", "    # Store the results\n", "    all_preds.append(y_pred[0])\n", "    all_labels.append(y_test.values[0])\n", "    all_file_names.append(file_name_test)\n", "\n", "# Evaluate the classifier\n", "print(\"Confusion Matrix:\")\n", "print(confusion_matrix(all_labels, all_preds))\n", "print(\"\\nClassification Report:\")\n", "print(classification_report(all_labels, all_preds))\n", "print(\"\\nAccuracy Score:\")\n", "print(accuracy_score(all_labels, all_preds))\n", "\n", "# Calculate and print MAE and MSE\n", "mae = mean_absolute_error(all_labels, all_preds)\n", "mse = mean_squared_error(all_labels, all_preds)\n", "print(f\"\\nMean Absolute Error (MAE): {mae}\")\n", "print(f\"Mean Squared Error (MSE): {mse}\")\n", "\n", "# Save the model, feature selector, and predictions\n", "#joblib.dump(clf, 'rf_model.pkl')\n", "#joblib.dump(selector, 'feature_selector.pkl')\n", "\n", "results_df = pd.DataFrame({\n", "    'File_Name': all_file_names,\n", "    'True_Label': all_labels,\n", "    'Predicted_Label': all_preds\n", "})\n", "\n", "# Identify rows in df_l_r not in results_df and predict their labels\n", "unprocessed_files = df_l_r[~df_l_r['ID'].isin(results_df['File_Name'])]\n", "\n", "if not unprocessed_files.empty:\n", "    unprocessed_X = unprocessed_files.drop(columns=['ID'])\n", "    unprocessed_X = unprocessed_X[X.columns]\n", "    unprocessed_X.fillna(0, inplace=True)\n", "\n", "    # Apply the same scaling and transformation\n", "    unprocessed_X_selected = selector.transform(unprocessed_X)\n", "    unprocessed_preds = clf.predict(unprocessed_X_selected)\n", "\n", "    unprocessed_results_df = pd.DataFrame({\n", "        'File_Name': unprocessed_files['ID'],\n", "        'True_Label': np.nan,  # True labels are not available for these\n", "        'Predicted_Label': unprocessed_preds\n", "    })\n", "\n", "    # Add these predictions to the original results_df\n", "    results_df = pd.concat([results_df, unprocessed_results_df], ignore_index=True)\n", "\n", "# Save all results to a CSV file\n", "#results_df.to_csv('complete_predicted_labels_loocv.csv', index=False)\n", "\n", "print(\"Model, feature selector, and all predicted labels saved successfully.\")\n", "#merge df results df to the union df\n", "df_union = df_union.join(results_df.set_index('File_Name')['Predicted_Label'])\n", "df_union.rename(columns={'Predicted_Label':'RF'}, inplace=True) "]}, {"cell_type": "code", "execution_count": 111, "metadata": {}, "outputs": [], "source": ["\n", "#merge with lightgbm model\n", "df_severity = pd.read_csv(r'\\\\files.ubc.ca\\team\\PPRC\\CAMERA\\Booth_Processed\\finger_tapping\\docs\\labels_updrs_weak_severity_model.csv')\n", "df_severity['filename'] = df_severity['filename'].str.replace('_finger_tapping.mp4', '')\n", "df_severity.set_index('filename', inplace=True)\n", "\n", "#join the severity model to the df_union\n", "df_union = df_union.join(df_severity['Predicted_Label'],)\n", "df_union.rename(columns={'Predicted_Label':'LightGBM'}, inplace=True)\n", "df_union['LightGBM'] = df_union['LightGBM'].round(0)"]}, {"cell_type": "code", "execution_count": 114, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:root:Computing O...\n", "INFO:root:Estimating \\mu...\n", "  0%|          | 0/500 [00:00<?, ?epoch/s]INFO:root:[0 epochs]: TRAIN:[loss=1.510]\n", " 13%|█▎        | 64/500 [00:00<00:00, 636.87epoch/s]INFO:root:[100 epochs]: TRAIN:[loss=0.032]\n", " 38%|███▊      | 190/500 [00:00<00:00, 572.85epoch/s]INFO:root:[200 epochs]: TRAIN:[loss=0.026]\n", " 51%|█████     | 255/500 [00:00<00:00, 600.36epoch/s]INFO:root:[300 epochs]: TRAIN:[loss=0.024]\n", " 80%|███████▉  | 399/500 [00:00<00:00, 669.57epoch/s]INFO:root:[400 epochs]: TRAIN:[loss=0.023]\n", "100%|██████████| 500/500 [00:00<00:00, 677.66epoch/s]\n", "INFO:root:Finished Training\n"]}], "source": ["#snorkel model for combine all\n", "#snorkel weak supervision\n", "import pandas as pd\n", "from snorkel.labeling.model.label_model import LabelModel \n", "\n", "\n", "# Constants\n", "ABSTAIN = -1  # Use this if some of your LFs abstain\n", "\n", "# Step 1: Create L matrix from the 5 LF columns\n", "# Assume df_union columns are exactly the weak labels\n", "L_train = df_union.fillna(ABSTAIN)[['KW','MG','SA','TM','WM','LightGBM','RF']].astype(float).values\n", "\n", "\n", "# Step 2: Fit the Label Model\n", "label_model = LabelModel(cardinality=5, verbose=True)  # change to 3 if multiclass\n", "label_model.fit(L_train=L_train, n_epochs=500, log_freq=100, seed=123)\n", "\n", "# Step 3: Predict labels (hard and probabilistic)\n", "df_union[\"snorkel_label_final\"] = label_model.predict(L=L_train)\n", "df_union[\"snorkel_prob_0_final\"] = label_model.predict_proba(L=L_train)[:, 0]  # probability of class 0\n", "df_union[\"snorkel_prob_1_final\"] = label_model.predict_proba(L=L_train)[:, 1]  # probability of class 1\n", "df_union[\"snorkel_prob_2_final\"] = label_model.predict_proba(L=L_train)[:, 2]  # probability of class 2\n", "df_union[\"snorkel_prob_3_final\"] = label_model.predict_proba(L=L_train)[:, 3]  # probability of class 3\n", "df_union[\"snorkel_prob_4_final\"] = label_model.predict_proba(L=L_train)[:, 4]  # probability of class 4\n", "\n", "df_union.to_csv(r'\\\\files.ubc.ca\\team\\PPRC\\CAMERA\\Booth_Processed\\finger_tapping\\docs\\weak_supervision_final.csv', index=True)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\assessment\\Lib\\site-packages\\sklearn\\feature_selection\\_univariate_selection.py:112: UserWarning: Features [19] are constant.\n", "  warnings.warn(\"Features %s are constant.\" % constant_features_idx, UserWarning)\n", "c:\\Users\\<USER>\\.conda\\envs\\assessment\\Lib\\site-packages\\sklearn\\feature_selection\\_univariate_selection.py:113: RuntimeWarning: invalid value encountered in divide\n", "  f = msb / msw\n", "433it [09:34,  1.33s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Confusion Matrix:\n", "[[48 21 11  2  0]\n", " [18 38 26 10  4]\n", " [ 7 22 39 28  3]\n", " [ 3 11 25 68 13]\n", " [ 1  3  2 16 14]]\n", "\n", "Classification Report:\n", "              precision    recall  f1-score   support\n", "\n", "         0.0       0.62      0.59      0.60        82\n", "         1.0       0.40      0.40      0.40        96\n", "         2.0       0.38      0.39      0.39        99\n", "         3.0       0.55      0.57      0.56       120\n", "         4.0       0.41      0.39      0.40        36\n", "\n", "    accuracy                           0.48       433\n", "   macro avg       0.47      0.47      0.47       433\n", "weighted avg       0.48      0.48      0.48       433\n", "\n", "\n", "Accuracy Score:\n", "0.4780600461893764\n", "\n", "Mean Absolute Error (MAE): 0.6859122401847575\n", "Mean Squared Error (MSE): 1.0831408775981524\n", "Model, feature selector, and all predicted labels saved successfully.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["#train random forest classifier\n", "import numpy as np\n", "import pandas as pd\n", "from sklearn.model_selection import LeaveOneOut\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.metrics import classification_report, confusion_matrix, accuracy_score, mean_absolute_error, mean_squared_error\n", "from sklearn.feature_selection import SelectKBest, f_classif\n", "from imblearn.over_sampling import SMOTE\n", "import joblib\n", "from tqdm import tqdm\n", "\n", "# Load the data\n", "file_l = r'\\\\files.ubc.ca\\team\\PPRC\\Camera\\Booth_Results\\finger_tapping_ws\\Experiment2\\csvs\\left_features_extracted_finger_distance.csv'\n", "file_r = r'\\\\files.ubc.ca\\team\\PPRC\\Camera\\Booth_Results\\finger_tapping_ws\\Experiment2\\csvs\\right_features_extracted_finger_distance.csv'\n", "df_l_r = pd.concat([pd.read_csv(file_l), pd.read_csv(file_r)])\n", "file1 = r'\\\\files.ubc.ca\\team\\PPRC\\CAMERA\\Booth_Processed\\finger_tapping\\docs\\weak_supervision.csv'\n", "df_updrs = pd.read_csv(file1)[['ID','snorkel_label']]\n", "df_updrs.rename(columns={'snorkel_label':'UPDRS'}, inplace=True)\n", "df_l_r['ID'] = df_l_r['file_name'].str.split('_finger').str[0]\n", "df_l_r.drop(columns=['file_name'], inplace=True)\n", "\n", "df = pd.merge(df_l_r, df_updrs, on='ID', how='left')\n", "df = df.dropna(subset=['UPDRS'])\n", "\n", "# Define the features and target\n", "X = df.drop(columns=['ID', 'UPDRS'])\n", "y = df['UPDRS']\n", "<PERSON><PERSON>fillna(0, inplace=True)\n", "file_names = df['ID']\n", "\n", "# Feature selection\n", "selector = SelectKBest(score_func=f_classif, k=20)\n", "selector.fit(X, y)\n", "X_selected = selector.transform(X)\n", "\n", "# Initialize the leave-one-out cross-validator\n", "loo = LeaveOneOut()\n", "smote = SMOTE( random_state=42)\n", "\n", "# Initialize lists to store results\n", "all_preds = []\n", "all_labels = []\n", "all_file_names = []\n", "\n", "# Loop over each split\n", "for train_index, test_index in tqdm(loo.split(X_selected)):\n", "    X_train, X_test = X_selected[train_index], X_selected[test_index]\n", "    y_train, y_test = y.iloc[train_index], y.iloc[test_index]\n", "    file_name_test = file_names.iloc[test_index].values[0]\n", "    \n", "    # Apply SMOTE to the training data\n", "    X_train_resampled, y_train_resampled = smote.fit_resample(X_train, y_train)\n", "\n", "    # Initialize and train the classifier\n", "    clf = RandomForestClassifier(random_state=42, n_estimators=500, max_depth=12)\n", "    clf.fit(X_train_resampled, y_train_resampled)\n", "\n", "    # Make prediction for the test instance\n", "    y_pred = clf.predict(X_test)\n", "\n", "    # Store the results\n", "    all_preds.append(y_pred[0])\n", "    all_labels.append(y_test.values[0])\n", "    all_file_names.append(file_name_test)\n", "\n", "# Evaluate the classifier\n", "print(\"Confusion Matrix:\")\n", "print(confusion_matrix(all_labels, all_preds))\n", "print(\"\\nClassification Report:\")\n", "print(classification_report(all_labels, all_preds))\n", "print(\"\\nAccuracy Score:\")\n", "print(accuracy_score(all_labels, all_preds))\n", "\n", "# Calculate and print MAE and MSE\n", "mae = mean_absolute_error(all_labels, all_preds)\n", "mse = mean_squared_error(all_labels, all_preds)\n", "print(f\"\\nMean Absolute Error (MAE): {mae}\")\n", "print(f\"Mean Squared Error (MSE): {mse}\")\n", "\n", "# Save the model, feature selector, and predictions\n", "#joblib.dump(clf, 'rf_model.pkl')\n", "#joblib.dump(selector, 'feature_selector.pkl')\n", "\n", "results_df = pd.DataFrame({\n", "    'File_Name': all_file_names,\n", "    'True_Label': all_labels,\n", "    'Predicted_Label': all_preds\n", "})\n", "\n", "# Identify rows in df_l_r not in results_df and predict their labels\n", "unprocessed_files = df_l_r[~df_l_r['ID'].isin(results_df['File_Name'])]\n", "\n", "if not unprocessed_files.empty:\n", "    unprocessed_X = unprocessed_files.drop(columns=['ID'])\n", "    unprocessed_X = unprocessed_X[X.columns]\n", "    unprocessed_X.fillna(0, inplace=True)\n", "\n", "    # Apply the same scaling and transformation\n", "    unprocessed_X_selected = selector.transform(unprocessed_X)\n", "    unprocessed_preds = clf.predict(unprocessed_X_selected)\n", "\n", "    unprocessed_results_df = pd.DataFrame({\n", "        'File_Name': unprocessed_files['ID'],\n", "        'True_Label': np.nan,  # True labels are not available for these\n", "        'Predicted_Label': unprocessed_preds\n", "    })\n", "\n", "    # Add these predictions to the original results_df\n", "    results_df = pd.concat([results_df, unprocessed_results_df], ignore_index=True)\n", "\n", "# Save all results to a CSV file\n", "#results_df.to_csv('complete_predicted_labels_loocv.csv', index=False)\n", "\n", "print(\"Model, feature selector, and all predicted labels saved successfully.\")"]}, {"cell_type": "code", "execution_count": 120, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "--- Inter-Rater Standard Deviation per UPDRS Class ---\n", "Class 0: 0.5384\n", "Class 1: 1.0451\n", "Class 2: 0.6253\n", "Class 3: 0.6681\n", "Class 4: 0.5224\n", "\n", "--- HiLWS Entropy (Uncertainty) per UPDRS Class ---\n", "Class 0: 0.2842\n", "Class 1: 0.5697\n", "Class 2: 0.5082\n", "Class 3: 0.3767\n", "Class 4: 0.1587\n", "\n", "--- QWK Between Raters and HiLWS ---\n", "KW: 0.7885\n", "MG: 0.8122\n", "TM: 0.6470\n", "SA: 0.7371\n", "WM: 0.6491\n", "\n", "--- QWK Between Rater Pairs ---\n", "KW                     float64\n", "MG                     float64\n", "TM                     float64\n", "SA                     float64\n", "WM                     float64\n", "snorkel_label_final      int32\n", "dtype: object\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#Compute paper metrics\n", "import pandas as pd\n", "import numpy as np\n", "from sklearn.metrics import confusion_matrix\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from scipy.stats import entropy\n", "from sklearn.metrics import cohen_kappa_score\n", "\n", "import pandas as pd\n", "import numpy as np\n", "from sklearn.metrics import confusion_matrix, cohen_kappa_score, f1_score, mean_absolute_error\n", "from scipy.stats import entropy\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.model_selection import train_test_split\n", "\n", "# Define raters and probability columns\n", "raters = ['KW', 'MG', 'TM', 'SA', 'WM']\n", "prob_cols = [f\"snorkel_prob_{i}\" for i in range(5)]\n", "\n", "# 1. Inter-Rater Std. Dev and Entropy per Class\n", "inter_rater_std = {}\n", "entropy_scores = {}\n", "\n", "for c in range(5):\n", "    class_group = df_union[df_union['snorkel_label'] == c]\n", "    if not class_group.empty:\n", "        inter_rater_std[c] = class_group[raters].std(axis=1).mean()\n", "        probs = class_group[prob_cols].values\n", "        entropy_scores[c] = np.mean([entropy(p) for p in probs])\n", "    else:\n", "        inter_rater_std[c] = np.nan\n", "        entropy_scores[c] = np.nan\n", "\n", "print(\"\\n--- Inter-Rater Standard Deviation per UPDRS Class ---\")\n", "for c in range(5):\n", "    print(f\"Class {c}: {inter_rater_std[c]:.4f}\")\n", "\n", "print(\"\\n--- HiLWS Entropy (Uncertainty) per UPDRS Class ---\")\n", "for c in range(5):\n", "    print(f\"Class {c}: {entropy_scores[c]:.4f}\")\n", "\n", "\n", "# 3. QWK Between Each Rater and HiLWS\n", "\n", "print(\"\\n--- QWK Between Raters and HiLWS ---\")\n", "for r in raters:\n", "    mask = df_union[[r, 'snorkel_label_final']].dropna().index\n", "    qwk = cohen_kappa_score(df_union.loc[mask, r], df_union.loc[mask, 'snorkel_label_final'], weights='quadratic')\n", "    print(f\"{r}: {qwk:.4f}\")\n", "\n", "# 4. QWK Between All Rater Pairs\n", "print(\"\\n--- QWK Between Rater Pairs ---\")\n", "raters = ['KW', 'MG', 'TM', 'SA', 'WM','snorkel_label_final']\n", "print(df_union[raters].dtypes)\n", "qwk_matrix = np.zeros((len(raters), len(raters)))\n", "\n", "for i, r1 in enumerate(raters):\n", "    for j, r2 in enumerate(raters):\n", "        mask = df_union[[r1, r2]].dropna().index\n", "        matching_rows = df_union.loc[mask,:]\n", "        if i == j:\n", "            qwk_matrix[i][j] = 1.0\n", "        else:\n", "            qwk_matrix[i][j] = cohen_kappa_score(matching_rows[r1], matching_rows[r2], weights='quadratic')\n", "\n", "# Plot the QWK matrix as a heatmap\n", "plt.figure(figsize=(8, 6))\n", "sns.heatmap(qwk_matrix, annot=True, xticklabels=['Exp. 1','Exp. 2','Exp. 3','Exp. 4','Exp. 5','HiLWS'], yticklabels=['Exp. 1','Exp. 2','Exp. 3','Exp. 4','Exp. 5','HiLWS'], cmap='Blues', fmt=\".2f\" )\n", "# set font size\n", "plt.xticks(fontsize=10)\n", "plt.yticks(fontsize=10)\n", "#plt.title(\"Quadratic Weighted Kappa Between Raters\")\n", "plt.xlabel(\"Label\")\n", "plt.ylabel(\"Label\")\n", "plt.tight_layout()\n", "plt.show()\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "# Store the results\n", "all_preds.append(y_pred[0])\n", "all_labels.append(y_test.values[0])\n", "all_file_names.append(file_name_test)\n", "\n", "# Evaluate the classifier\n", "print(\"Confusion Matrix:\")\n", "print(confusion_matrix(all_labels, all_preds))\n", "print(\"\\nClassification Report:\")\n", "print(classification_report(all_labels, all_preds))\n", "print(\"\\nAccuracy Score:\")\n", "print(accuracy_score(all_labels, all_preds))\n", "\n", "# Calculate and print MAE and MSE\n", "mae = mean_absolute_error(all_labels, all_preds)\n", "mse = mean_squared_error(all_labels, all_preds)\n", "print(f\"\\nMean Absolute Error (MAE): {mae}\")\n", "print(f\"Mean Squared Error (MSE): {mse}\")\n", "\n", "# Save the model, feature selector, and predictions\n", "#joblib.dump(clf, 'rf_model.pkl')\n", "#joblib.dump(selector, 'feature_selector.pkl')\n", "\n", "results_df = pd.DataFrame({\n", "    'File_Name': all_file_names,\n", "    'True_Label': all_labels,\n", "    'Predicted_Label': all_preds\n", "})\n", "\n", "# Identify rows in df_l_r not in results_df and predict their labels\n", "unprocessed_files = df_l_r[~df_l_r['ID'].isin(results_df['File_Name'])]\n", "\n", "if not unprocessed_files.empty:\n", "    unprocessed_X = unprocessed_files.drop(columns=['ID'])\n", "    unprocessed_X = unprocessed_X[X.columns]\n", "    unprocessed_X.fillna(0, inplace=True)\n", "\n", "    # Apply the same scaling and transformation\n", "    unprocessed_X_selected = selector.transform(unprocessed_X)\n", "    unprocessed_preds = clf.predict(unprocessed_X_selected)\n", "\n", "    unprocessed_results_df = pd.DataFrame({\n", "        'File_Name': unprocessed_files['ID'],\n", "        'True_Label': np.nan,  # True labels are not available for these\n", "        'Predicted_Label': unprocessed_preds\n", "    })\n", "\n", "    # Add these predictions to the original results_df\n", "    results_df = pd.concat([results_df, unprocessed_results_df], ignore_index=True)\n", "\n", "# Save all results to a CSV file\n", "#results_df.to_csv('complete_predicted_labels_loocv.csv', index=False)\n", "\n", "print(\"Model, feature selector, and all predicted labels saved successfully.\")"]}], "metadata": {"kernelspec": {"display_name": "assessment", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.5"}}, "nbformat": 4, "nbformat_minor": 2}