import cv2
import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
import os
from scipy.signal import find_peaks

def analyze_video_shake_and_zoom(video_path, output_chart_folder, store_chart=False):
    cap = cv2.VideoCapture(video_path)
    
    if not cap.isOpened():
        print(f"Error: Could not open video {video_path}")
        return None
    
    # ORB feature detector
    orb = cv2.ORB_create()
    bf = cv2.BFMatcher(cv2.NORM_HAMMING, crossCheck=True)

    # Store shake and zoom values
    shake_magnitudes = []
    zoom_factors = []
    frame_indices = []

    # Read first frame
    ret, prev_frame = cap.read()
    prev_frame = prev_frame[:900, :400]
    if not ret:
        print(f"Error: Could not read first frame of {video_path}")
        return None
    
    prev_gray = cv2.cvtColor(prev_frame, cv2.COLOR_BGR2GRAY)
    
    prev_keypoints, prev_descriptors = orb.detectAndCompute(prev_gray, None)

    frame_count = 0

    while True:
        ret, frame = cap.read()
        frame = frame
        if not ret:
            break
        
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

        keypoints, descriptors = orb.detectAndCompute(gray, None)

        if prev_descriptors is None or descriptors is None:
            continue
        
        matches = bf.match(prev_descriptors, descriptors)
        matches = sorted(matches, key=lambda x: x.distance)

        if len(matches) > 10:
            src_pts = np.float32([prev_keypoints[m.queryIdx].pt for m in matches]).reshape(-1, 1, 2)
            dst_pts = np.float32([keypoints[m.trainIdx].pt for m in matches]).reshape(-1, 1, 2)

            matrix, _ = cv2.estimateAffinePartial2D(src_pts, dst_pts)

            if matrix is not None:
                dx, dy = matrix[0, 2], matrix[1, 2]  # Translation
                scale_x = np.linalg.norm(matrix[0, :2])
                scale_y = np.linalg.norm(matrix[1, :2])
                scale = (scale_x + scale_y) / 2

                shake_magnitude = np.sqrt(dx**2 + dy**2)  # Euclidean displacement
                zoom_factors.append(scale)
                shake_magnitudes.append(shake_magnitude)
                frame_indices.append(frame_count)
        
        prev_gray = gray
        prev_keypoints, prev_descriptors = keypoints, descriptors
        frame_count += 1
    
    cap.release()

    # Peak Detection for Shake and Zoom
    shake_peaks, _ = find_peaks(shake_magnitudes, height=np.mean(shake_magnitudes) + np.std(shake_magnitudes))
    zoom_peaks, _ = find_peaks(zoom_factors, height=np.mean(zoom_factors) + np.std(zoom_factors))

    # Find the longest duration without zoom peaks
    longest_no_zoom_start = None
    longest_no_zoom_end = None
    current_start = None

    for i in range(len(frame_indices)):
        if i in zoom_peaks:
            # Zoom peak found, check previous segment length
            if current_start is not None:
                duration = frame_indices[i - 1] - frame_indices[current_start]
                if longest_no_zoom_start is None or duration > (longest_no_zoom_end - longest_no_zoom_start):
                    longest_no_zoom_start = frame_indices[current_start]
                    longest_no_zoom_end = frame_indices[i - 1]
            current_start = None  # Reset since we found a peak
        else:
            if current_start is None:
                current_start = i  # Start a new segment

    # Check last segment if it was the longest
    if current_start is not None:
        duration = frame_indices[-1] - frame_indices[current_start]
        if longest_no_zoom_start is None or duration > (longest_no_zoom_end - longest_no_zoom_start):
            longest_no_zoom_start = frame_indices[current_start]
            longest_no_zoom_end = frame_indices[-1]

    longest_duration_no_zoom = longest_no_zoom_end - longest_no_zoom_start if longest_no_zoom_start is not None else 0

    # Save Chart for Each Video
    if store_chart:
        os.makedirs(output_chart_folder, exist_ok=True)
        video_name = os.path.basename(video_path).split('.')[0]
        chart_path = os.path.join(output_chart_folder, f"{video_name}_chart.png")

        plt.figure(figsize=(12, 5))

        plt.subplot(1, 2, 1)
        plt.plot(frame_indices, shake_magnitudes, label="Shake Magnitude")
        plt.scatter(np.array(frame_indices)[shake_peaks], np.array(shake_magnitudes)[shake_peaks], color='red', label="Shake Peaks", marker='o')
        plt.xlabel("Frame")
        plt.ylabel("Shake (pixels)")
        plt.title(f"Shake Detection Over Time ({video_name})")
        plt.legend()

        plt.subplot(1, 2, 2)
        plt.plot(frame_indices, zoom_factors, label="Zoom Factor", color="red")
        plt.scatter(np.array(frame_indices)[zoom_peaks], np.array(zoom_factors)[zoom_peaks], color='blue', label="Zoom Peaks", marker='o')
        plt.xlabel("Frame")
        plt.ylabel("Zoom Level")
        plt.title(f"Zoom Detection Over Time ({video_name})")
        plt.legend()

        plt.tight_layout()
        plt.savefig(chart_path)
        plt.close()
    
    # Return results as a dictionary
    return {
        "Video Name": os.path.basename(video_path),
        "Shake Peaks": len(shake_peaks),
        "No Zoom Start Frame": longest_no_zoom_start,
        "No Zoom End Frame": longest_no_zoom_end,
        "No Zoom Duration": longest_duration_no_zoom
    }

def process_all_videos_in_folder(folder_path, store_csv , chart_folder="charts", store_chart= False):
    results = []

    # Create output folder for charts
    os.makedirs(chart_folder, exist_ok=True)
    
    for filename in os.listdir(folder_path):
        if filename.endswith((".mp4", ".avi", ".mov", ".mkv")):  # Add more extensions if needed
            video_path = os.path.join(folder_path, filename)
            print(f"Processing: {filename}")
            result = analyze_video_shake_and_zoom(video_path, chart_folder)
            if result:
                results.append(result)

    # Save results to CSV
    if results:
        df = pd.DataFrame(results)
        df.to_csv(store_csv, index=False)
        print(f"Results saved to {store_csv}")
    else:
        print("No valid videos processed.")

tasks = [#r'facial_expression\basic_facial\text',
             r'hand_movement\left_open_close', 
             r'hand_movement\right_open_close',
             r'hand_movement\left_up_down',
             r'hand_movement\right_up_down',
             r'finger_tapping\right', 
             r'finger_tapping\left']

for task in tasks:
    folder_path = r"\\files.ubc.ca\team\PPRC\CAMERA\Booth_Processed\{}\videos".format(task)  # Change to your video folder path
    chart_folder = r"\\files.ubc.ca\team\PPRC\CAMERA\Booth_Processed\{}\shaking_charts".format(task)  # Change to your preferred chart output folder
    store_csv = r"\\files.ubc.ca\team\PPRC\CAMERA\Booth_Processed\{}\csvs\shaking_analysis.csv".format(task)
    process_all_videos_in_folder(folder_path, store_csv , chart_folder=chart_folder)
    print(f"Completed processing for {task}")

 