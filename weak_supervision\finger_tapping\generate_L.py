import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split
from sklearn.svm import SVC
from sklearn.utils.class_weight import compute_class_weight
from imblearn.over_sampling import SMOTE
from imblearn.under_sampling import RandomUnderSampler
from imblearn.pipeline import Pipeline
from sklearn.metrics import accuracy_score

NEGATIVE = 0
POSITIVE = 1

def get_ground_truth_label(x):
    # If the file name starts with '07', the label is NEGATIVE, otherwise POSITIVE
    return NEGATIVE if x.startswith('07') else POSITIVE

def calculate_threshold(series, lower_percentile, upper_percentile):
    """
    Calculate thresholds such that the majority of data (from lower_percentile to upper_percentile)
    falls within these thresholds.
    """
    lower_threshold = np.percentile(series, lower_percentile)
    upper_threshold = np.percentile(series, upper_percentile)
    return lower_threshold, upper_threshold


def generate_labels(features_df, output_file, clfs):
    def get_percentile_thresholds(column, lower_pct=2, upper_pct=95):
        lower_threshold = np.percentile(column, lower_pct)
        upper_threshold = np.percentile(column, upper_pct)
        return lower_threshold, upper_threshold

    def create_labeling_function(column_name, lower_threshold, upper_threshold):
        def labeling_function(row):
            value = row[column_name]
            if value > upper_threshold or value < lower_threshold:
                return 1
            else:
                return -1
        return labeling_function
    
    # Calculate thresholds for each feature
    thresholds = {}
    for column in features_df.columns:
        if column != 'file_name':
            lower_threshold, upper_threshold = get_percentile_thresholds(features_df[column])
            thresholds[column] = (lower_threshold, upper_threshold)

    # Create labeling functions dynamically based on calculated thresholds
    labeling_functions = {}
    for column, (lower_threshold, upper_threshold) in thresholds.items():
        labeling_functions[f'LF_{column}'] = create_labeling_function(column, lower_threshold, upper_threshold)
    
    # Apply labeling functions to the features
    labels = {'file_name': features_df['file_name']}
    for name, lf in labeling_functions.items():
        labels[name] = features_df.apply(lf, axis=1)

    # Apply classifier predictions
    for i, clf in enumerate(clfs):
        clf_name = clf.__class__.__name__
        if clf_name == 'RandomForestClassifier':
            labels[f'LF_{clf_name}_{i}'] = clf.predict(features_df.drop('file_name', axis=1))
        else:
            labels[f'LF_{clf_name}_{i}'] = clf.predict(features_df.drop('file_name', axis=1))

    # Save labels to CSV
    labels_df = pd.DataFrame(labels)
    labels_df.to_csv(output_file, index=False)


def train_svm(features_df, labels):
    """
    Train a support vector machine classifier using the features and labels.
    """
    X = features_df.drop('file_name', axis=1).iloc[:, :]  # Remove the 'file_name' column from features
    y = labels  # Use the labels for training


    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=12)
    
    # Initialize and train the support vector machine classifier with class weights
    clf = SVC(kernel='linear', C=10, random_state=32)
    clf.fit(X_train, y_train)
    
    y_pred = clf.predict(X_test)
    accuracy = accuracy_score(y_test, y_pred)
    print(f"SVM Accuracy: {accuracy}")

    return clf
def train_random_forest_2(features_df, labels):
    """
    Train a random forest classifier using the features and labels.
    """
    X = features_df.drop('file_name', axis=1).iloc[:,:]   # Remove the 'file_name' column from features
    y = labels  # Use the labels for training

    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=12)
    
    # Initialize and train the random forest classifier
    clf = RandomForestClassifier(random_state=32, n_estimators=50)
    clf.fit(X_train, y_train)
    y_pred = clf.predict(X_test)
    accuracy = accuracy_score(y_test, y_pred)
    print(f"RF Accuracy: {accuracy}")
    return clf
def balance_dataset(features_df, labels):
    """
    Balance the dataset by downsampling the majority class.
    """
    # Separate the data into two classes
    features_df['label'] = labels
    negative_samples = features_df[features_df['label'] == NEGATIVE]
    positive_samples = features_df[features_df['label'] == POSITIVE]

    # Downsample the positive samples to match the number of negative samples
    positive_samples_downsampled = positive_samples.sample(len(negative_samples), random_state=14)

    # Combine the negative samples with the downsampled positive samples
    balanced_df = pd.concat([negative_samples, positive_samples_downsampled])

    # Separate the features and labels again
    balanced_features = balanced_df.drop(columns=['label'])
    balanced_labels = balanced_df['label']

    return balanced_features, balanced_labels

if __name__ == "__main__":
    for task in ['left','right']:
        features_file = rf'\\files.ubc.ca\\team\\PPRC\\Camera\\Booth_Results\\finger_tapping_ws\\features\\{task}_features_extracted_2.csv'
        labels_file = rf'\\files.ubc.ca\\team\\PPRC\\Camera\\Booth_Results\\finger_tapping_ws\\LFs\\{task}_labels.csv'
        features_df = pd.read_csv(features_file)
        labels = features_df['file_name'].apply(get_ground_truth_label)
        features_df['label'] = labels
        balanced_features, balanced_labels = balance_dataset(features_df, labels)
        # Train classifiers with class weights to handle class imbalance
        #rf = train_random_forest(balanced_features, balanced_labels)
        svm = train_svm(balanced_features, balanced_labels)
        rf = train_random_forest_2(balanced_features, balanced_labels)
        clfs = [svm,rf]
        features_df.drop(columns=['label'], inplace=True)
        generate_labels(features_df, labels_file, clfs)
