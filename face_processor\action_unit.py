import subprocess
import pandas as pd
import os
import sys
sys.path.insert(0, 'd:\\Atefeh\\Codes\\Python\\ParkinsonAssessment')

class VideoAUsAnalyzer:
    def __init__(self, video_path, output_path, openface_path='./libs/OpenFace_2.2.0'):
        self.video_path = video_path
        self.openface_path = openface_path
        self.action_units_df = None
        self.output_csv = output_path

    def extract_action_units(self):
        """Extract action units from the video using OpenFace."""
        print(self.video_path)
        cmd = [
            os.path.join(self.openface_path, 'FeatureExtraction'),
            '-f', self.video_path,
            '-out_dir', os.path.dirname(self.output_csv),
            '-aus',
            '-of', os.path.basename(self.output_csv)
        ]
        try:
            print(cmd)
            subprocess.run(cmd, check=True)
            self.action_units_df = pd.read_csv(self.output_csv)
        except subprocess.CalledProcessError as e:
            raise RuntimeError("Failed to extract features using OpenFace.") from e
        except FileNotFoundError:
            raise FileNotFoundError("The output CSV file was not generated by OpenFace.")
        except Exception as e:
            raise Exception("An error occurred while processing the video.") from e



