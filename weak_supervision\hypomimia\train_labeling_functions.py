
import sys
sys.path.insert(0, 'd:\\Atefeh\\Codes\\Python\\ParkinsonAssessment')
from weak_supervision.hypomimia.lf10_blink_rate import LF10Class
from weak_supervision.hypomimia.lf3_AU_intensity import LF3Class
from weak_supervision.hypomimia.lf4_emotion_intensity import LF4Class
from weak_supervision.hypomimia.lf7_neutral_dominance import LF7Class
from weak_supervision.hypomimia.lf9_reduced_eyebrow import LF9Class
from weak_supervision.hypomimia.lf5_emotion_delay import LF5Class

parameters_dict = {
    0: {},  # No parameters
    1: {},
    2: {},
    3: {},
    4: {'task': 'imitation', 'emotion': 'happy'},
    5: {'task': 'imitation', 'emotion': 'sad'},
    6: {'task': 'text', 'emotion': 'disgust'},
    7: {'task': 'imitation', 'emotion': 'angry'},
    8: {'task': 'imitation', 'emotion': 'disgust'},
    9: {'task': 'text', 'emotion': 'happy'},
    10: {'task': 'text', 'emotion': 'sad'},
    11: {'task': 'text', 'emotion': 'angry'},
    12: {},
}

version = 5
lfs = [
    LF3Class, LF4Class, LF9Class, LF10Class, LF5Class, LF5Class,
    LF5Class, LF5Class, LF5Class, LF5Class, LF5Class, LF5Class, LF7Class
]

for i, lf in enumerate(lfs):
    if i in parameters_dict:
        params = parameters_dict[i]
        task = params.get('task', '')
        emotion = params.get('emotion', '')
        model_path = f'.\\models\\best_{lf.__name__}_{task}_{emotion}_v{version}.json'
        
        lf_instance = lf(model_path=model_path, **params)
        classifier, selected_features = lf_instance.train_classifier()
        lf_instance.save_model(classifier, selected_features)
        lf_instance.evaluate_classifier(classifier, selected_features)
