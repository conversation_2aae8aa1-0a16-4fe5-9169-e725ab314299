import sys


sys.path.insert(0, 'd:\\Atefeh\\Codes\\Python\\ParkinsonAssessment')
from datasets.load_data import get_train_ensemble_set, get_train_set, get_validations, get_train_classifiers_set
import pandas as pd
from snorkel.labeling import labeling_function, PandasLFApplier, LFAnalysis
from snorkel.labeling.model.label_model import LabelModel 

from weak_supervision.LFBase import LFBaseClass


class SnorkelBase:
    def __init__(self, labeling_function_classes,predict,parameters_dict):
        self.labeling_function_classes = labeling_function_classes
        self.labeling_functions = []
        self.predict = predict
        self.df = pd.DataFrame()
        self.parmaeters_dict = parameters_dict

    def run(self):
        pass
    
    def define_labeling_functions(self):
        for index, lf_class in enumerate(self.labeling_function_classes):
            params = self.parmaeters_dict.get(index, {})
            self.labeling_functions.append(self.create_labeling_function(lf_class, index, **params))

    def create_labeling_function(self, lfBase: LFBaseClass, index, **kwargs):
        # Append the index to the function name to ensure uniqueness
        @labeling_function(name=f"lf_{index}")
        def lf(x):
            # Instantiate the lfBase class with specific parameters
            return  lfBase(**kwargs).labeling_function(x)
        
        return lf

    def train_snorkel(self):
        # Apply the labeling functions to the data
        if not self.labeling_functions:
            self.define_labeling_functions()
        
        if not self.df.empty: 
            self.create_df()   
            
        df = get_train_set(self.df)
        
        applier = PandasLFApplier(lfs=self.labeling_functions)
        L_train = applier.apply(df=df)

        # Analyze the coverage, overlaps, and conflicts between LFs
        lf_analysis = LFAnalysis(L=L_train)
        print(lf_analysis.lf_summary())

        # Train a label model to combine the outputs
        self.label_model = LabelModel(cardinality=5)
        self.label_model.fit(L_train=L_train, n_epochs=200, log_freq=100, seed=123)

        return L_train
    
    def evaluate(self,test_df=None):
        if test_df is None:
            test_df = get_validations(self.df)
        # Create the probabilistic training labels
        applier = PandasLFApplier(lfs=self.labeling_functions)
        L_test = applier.apply(df=test_df)
        probabilistic_labels = self.label_model.predict_proba(L=L_test)
        return probabilistic_labels, L_test

    def create_df(self):
        # Initialize an empty list to collect DataFrames
        dataframes = []

        # Call prepare_dataset() from each labeling function class and collect DataFrames
        for lf_class in self.labeling_function_classes:
            df = lf_class().prepare_dataset()
            # Ensure the index is explicitly considered if 'ID' is not set as an index
            if 'ID' not in df.columns and df.index.name != 'ID':
                df = df.set_index('ID')
            # Drop duplicates in the index to avoid 'InvalidIndexError'
            df = df[~df.index.duplicated(keep='first')]
            dataframes.append(df)

        # Concatenate all DataFrames along the index (ID), handling duplicates
        merged_df = pd.concat(dataframes, axis=1, join='outer')
        # Drop duplicate columns by keeping the first instance (assumes all DFs have same-named columns with the same meaning)
        merged_df = merged_df.loc[:, ~merged_df.columns.duplicated()]

        self.df = merged_df
        self.df = self.df.dropna(subset=[self.predict])
        return self.df


# import sys

# from sklearn.metrics import accuracy_score
# from sklearn.metrics import classification_report, confusion_matrix


# sys.path.insert(0, 'd:\\Atefeh\\Codes\\Python\\ParkinsonAssessment')
# import os
# from datasets.load_data import get_validations, load_emotion_task_ts_data, load_labels_with_ID, merge_df_with_labels
# from weak_supervision.SnorkelBase import SnorkelBase
# from weak_supervision.hypomimia.lf10_blink_rate import LF10Class
# from weak_supervision.hypomimia.lf3_AU_intensity import LF3Class
# from weak_supervision.hypomimia.lf4_emotion_intensity import LF4Class
# from weak_supervision.hypomimia.lf7_neutral_dominance import LF7Class
# from weak_supervision.hypomimia.lf9_reduced_eyebrow import LF9Class
# from weak_supervision.hypomimia.lf5_emotion_delay import LF5Class


# parameters_dict = {
#     0: {},  # No parameters
#     1: {},
#     2: {},
#     3: {},
#     4: {'task': 'imitation', 'emotion': 'happy'},
#     5: {'task': 'imitation', 'emotion': 'sad'},
#     # 7: {'task': 'imitation', 'emotion': 'angry'},
#     # 8: {'task': 'imitation', 'emotion': 'disgust'},
#     # 9: {'task': 'text', 'emotion': 'happy'},
#     # 10: {'task': 'text', 'emotion': 'sad'},
#     # 11: {'task': 'text', 'emotion': 'angry'},
#     # 12: {'task': 'text', 'emotion': 'disgust'}
# }

# hs = SnorkelBase(labeling_function_classes=[LF3Class, LF4Class, LF9Class, LF7Class , LF5Class,LF5Class],predict='Facial_Expression_UPDRS (custom)',parameters_dict=parameters_dict)
# hs.define_labeling_functions()
# hs.create_df()
# L_train = hs.train_snorkel()
# prob, L_test = hs.evaluate()
# import pandas as pd

        
# val_df =get_validations(hs.df)
# # Evaluate overall performance
# y_pred = prob.argmax(axis=1)
# y_true = val_df[hs.predict]
# df_y = pd.DataFrame(y_true)
# df_y['y_pred'] = y_pred

# accuracy = accuracy_score(y_true, y_pred)
# report = classification_report(y_true, y_pred)
# cm = confusion_matrix(y_true, y_pred)
# print(f'Accuracy: {accuracy}')
# print('Classification Report:')
# print(report)
# print('Confusion Matrix:')
# print(cm)
