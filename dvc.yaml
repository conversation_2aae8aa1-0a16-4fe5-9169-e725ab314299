stages:
  preprocess_facial_expression:
    cmd: python datasets/copy_scripts/copy_facial_expression.py
    deps:
      - datasets/copy_scripts/copy_facial_expression.py
      - datasets/copy_scripts/copy_videos.py
      - config.py
      - \\files.ubc.ca\team\PPRC\Camera\CAMERA Booth Data\Booth
    outs:
      - \\files.ubc.ca\team\PPRC\Camera\Booth_Processed\facial_expression

  preprocess_finger_tapping:
    cmd: python datasets/copy_scripts/copy_finger_tapping.py
    deps:
      - datasets/copy_scripts/copy_finger_tapping.py
      - datasets/copy_scripts/copy_videos.py
      - config.py
      - \\files.ubc.ca\team\PPRC\Camera\CAMERA Booth Data\Booth
    outs:
      - \\files.ubc.ca\team\PPRC\Camera\Booth_Processed\finger_tapping

  preprocess_hand_movement:
    cmd: python datasets/copy_scripts/copy_hand_movement.py
    deps:
      - datasets/copy_scripts/copy_hand_movement.py
      - datasets/copy_scripts/copy_videos.py
      - config.py
      - \\files.ubc.ca\team\PPRC\Camera\CAMERA Booth Data\Booth
    outs:
      - \\files.ubc.ca\team\PPRC\Camera\Booth_Processed\hand_movement

  preprocess_eye_movement:
    cmd: python datasets/copy_scripts/copy_eye_movement.py
    deps:
      - datasets/copy_scripts/copy_eye_movement.py
      - datasets/copy_scripts/copy_videos.py
      - config.py
      - \\files.ubc.ca\team\PPRC\Camera\CAMERA Booth Data\Booth
    outs:
      - \\files.ubc.ca\team\PPRC\Camera\Booth_Processed\eye_movement
