#read labels
import pandas as pd
import os
from scipy.signal import savgol_filter  
import numpy as np
from tqdm import tqdm  # Import tqdm for progress display
from tensorflow.keras.preprocessing.sequence import pad_sequences
#create y
labels_path = r'\\files.ubc.ca\team\PPRC\CAMERA\Booth_Processed\finger_tapping\docs\weak_supervision_final.csv'
df_labels = pd.read_csv(labels_path)
df_labels.set_index('ID',inplace=True)
y = df_labels['snorkel_label_final']
#kip first duplicate
y = y[~y.index.duplicated(keep='first')]
y.dropna(inplace=True)
y_binary = y>0


#read labels
import pandas as pd
import os
import numpy as np
from tqdm import tqdm  # Import tqdm for progress display
from tensorflow.keras.preprocessing.sequence import pad_sequences



#create x
def create_df_combined(metric='Finger Normalized Distance'):
    tasks=['right', 'left']

    df_combined = pd.DataFrame(columns=['values', 'ID'])
    df_combined['ID'] = y.index
    df_combined.set_index('ID', inplace=True)

    for task in tasks:
        file =  rf'\\files.ubc.ca\team\PPRC\Camera\Booth_Processed\finger_tapping\{task}\distances'
        segments_file = rf'\\files.ubc.ca\team\PPRC\Camera\Booth_Processed\finger_tapping\{task}\csvs\{task}_best_segments_and_indexes.csv'
        segments_file = pd.read_csv(segments_file).set_index('file_name')
        
        for root, dirs, filenames in os.walk(file):
            # Initialize progress bar
            filenames = [f for f in filenames if f.endswith('.csv')]
            with tqdm(total=len(filenames), desc=f"Processing {file}", unit="file") as pbar:
                for filename in filenames:
                    file_path = os.path.join(root, filename)
                    distances = pd.read_csv(file_path)[['Frame', 'Finger Distance', 'Finger Normalized Distance', 'Angular Distance', 'Wrist Coordinate']]
                    if len(distances) < 5:
                        print(f"File {file} has less than 5 frames. Skipping...")
                        pbar.update(1)
                        continue
                    
                    #filter based on segments
                    # if filename in segments_file.index:
                    #     start,end,_ = segments_file.loc[filename].values
                    #     distances = distances[(distances['Frame'] >= start) & (distances['Frame'] <= end)]
                        
                        
                    normalized_distance = distances[metric]
                    file_id = filename.split('_finger')[0]
                    df_combined.loc[file_id, 'values'] = normalized_distance.to_numpy()
                    pbar.update(1)
    return df_combined
                
df_combined = create_df_combined(metric='Finger Normalized Distance')
df_combined_angular = create_df_combined(metric='Angular Distance')
df_combined_wrist = create_df_combined(metric='Wrist Coordinate')      

from tensorflow.keras.preprocessing.sequence import pad_sequences


def get_X_y(df_combined, y_binary, y,type='float32'):
    # Ensure the index of df_combined matches the index of y_binary
    df_combined = df_combined[df_combined.index.isin(y_binary.index)]
    
    # Shuffle the DataFrame
    df_combined_shuffled = df_combined.sample(frac=1, random_state=42)
    
    # Select valid IDs
    valid_ids = df_combined_shuffled['values'].dropna().index.intersection(y_binary.index)
    
    # Extract sequences
    sequences = df_combined_shuffled.loc[valid_ids, 'values'].tolist()
    
    if type ==  'float32':
        # Pad or truncate sequences to equal length
        X_padded = pad_sequences(sequences, maxlen=450, dtype=type, padding='post', truncating='post')
        
        # Expand dims to match required shape: (samples, series_length, 1)
        X = X_padded
    else:
        # For string type, we will convert sequences to a list of strings
        X = [np.array(seq, dtype=object) for seq in sequences]
        X = pad_sequences(X, maxlen=450, padding='post', truncating='post', dtype=object)
    
    y_binary = y_binary.loc[valid_ids]
    y = y.loc[valid_ids]
    
    return X, y_binary, y,valid_ids

X, y_binary, y, valid_ids = get_X_y(df_combined, y_binary, y)
X_angular, _, _, _ = get_X_y(df_combined_angular, y_binary, y)
X_wrist, _, _, _ = get_X_y(df_combined_wrist, y_binary, y,type='string')


# Check shapes
print(X.shape, y_binary.shape)


#Extract data and cycles
from scipy.signal import find_peaks, argrelextrema

def find_peaks_finger_tapping(distances):
    """
    Find minima (tap indices) and maxima (max distances between taps) in the FINGER_NORMALIZED_DISTANCE data.
    Returns maxima indices, minima indices, start index, end index.
    """

    # Find taps in the data (minima)
    tap_count, taps_indices = count_taps(distances)

    # Find maxima between taps
    maxima_indices = []
    for i in range(len(taps_indices) - 1):
        start_tap = taps_indices[i]
        end_tap = taps_indices[i + 1]
        # Find maximum between two taps
        if end_tap > start_tap + 1:
            local_max_index = np.argmax(distances[start_tap:end_tap + 1]) + start_tap
            maxima_indices.append(local_max_index)  # Adjust back to original indices

    # Adjust minima indices (taps_indices) back to original indices
    minima_indices = taps_indices

    return distances ,maxima_indices, minima_indices

def count_taps(distances, min_prominence=0.15, distance = 10, height_multiplyer = 0.3):
    distances = np.array(distances, dtype=float)
    taps_indices = argrelextrema(distances, np.less)[0]
    peaks, _ = find_peaks(-distances, prominence=min_prominence, distance=distance, height=-height_multiplyer*(max(distances)))
    taps_indices = np.intersect1d(taps_indices, peaks)
    tap_count = len(taps_indices)
    return tap_count, taps_indices
    
cycles = []
max_indices = []
for i in tqdm(range(len(X)), desc="Extracting features"):
    distances = X[i]
    if len(distances) < 5:
        continue  # Skip if not enough data
    
    # Find peaks and taps
    distances, open_indices, tap_indices = find_peaks_finger_tapping(distances)

    
    # Append the features
    cycles.append(tap_indices)
    max_indices.append(open_indices)

import numpy as np
from scipy.signal import find_peaks, welch
import pandas as pd
from tqdm import tqdm

POSITIVE = 1
NEGATIVE = 0
ABSTAIN = -1

# def find_peaks_finger_tapping(distances, min_distance=10, prominence=0.1):
#     peaks, _ = find_peaks(distances, distance=min_distance, prominence=prominence)
#     open_indices = peaks.tolist()
#     tap_cycles = [[peaks[i], peaks[i+1]] for i in range(len(peaks)-1)]
#     return open_indices, tap_cycles

def count_taps(distances, min_prominence=0.15, distance = 10, height_multiplyer = 0.3):
    distances = np.array(distances, dtype=float)
    taps_indices = argrelextrema(distances, np.less)[0]
    peaks, _ = find_peaks(-distances, prominence=min_prominence, distance=distance, height=-height_multiplyer*(max(distances)))
    taps_indices = np.intersect1d(taps_indices, peaks)
    tap_count = len(taps_indices)
    return tap_count, taps_indices

def find_best_segment_indices(distances, taps_indices, cutoff=1.0, fs=10.0, order=5, min_prominence=0.15, distance=10, height_multiplier=0.3, min_taps=8):
    

    def split_segments(distances, taps_indices):
        max_gap = np.mean(np.diff(taps_indices)) * 3
        segments = []
        start_idx = taps_indices[0]
        for i in range(1, len(taps_indices)):
            if taps_indices[i] - taps_indices[i - 1] > max_gap:
                segments.append((start_idx, taps_indices[i - 1]))
                start_idx = taps_indices[i]
        segments.append((start_idx, taps_indices[-1]))
        return segments

    def select_best_segment(distances, segments):
        best_segment = None
        max_taps = 0
        for start, end in segments:
            segment_distances = distances[start:end + 1]
            tap_count, _ = count_taps(segment_distances, min_prominence, distance, height_multiplier)
            
            if tap_count > max_taps and tap_count > min_taps:
                max_taps = tap_count
                best_segment = (start, end)
        if best_segment is None:
            best_segment = (segments[0][0], segments[-1][1])
        return best_segment
    
    
    # Split the data into segments based on the gap between taps
    if len(taps_indices) < 5:
        return taps_indices[0], taps_indices[-1]  # Not enough taps to define a segment
    segments = split_segments(distances, taps_indices)

    # Select the best segment with the highest number of taps
    best_segment = select_best_segment(distances, segments)

    # Return the start and last index of the best segment
    return best_segment

def find_peaks_finger_tapping(distances):
    """
    Find minima (tap indices) and maxima (max distances between taps) in the FINGER_NORMALIZED_DISTANCE data.
    Returns maxima indices, minima indices, start index, end index.
    """

    # Find taps in the data (minima)
    tap_count, taps_indices =count_taps(distances)

    # Find the best segment based on taps
    if len(taps_indices) < 2:
        return distances, [], [],0,0
    start_index, end_index = find_best_segment_indices(distances, taps_indices)

    # Trim distances and taps indices to the best segment
    distances = distances[start_index:end_index + 1]
    tap_count, taps_indices =count_taps(distances)

    # Find maxima between taps
    maxima_indices = []
    for i in range(len(taps_indices) - 1):
        start_tap = taps_indices[i]
        end_tap = taps_indices[i + 1]
        # Find maximum between two taps
        if end_tap > start_tap + 1:
            local_max_index = np.argmax(distances[start_tap:end_tap + 1]) + start_tap
            maxima_indices.append(local_max_index )

    # Adjust minima indices (taps_indices) back to original indices
    minima_indices = taps_indices
    tap_cycles = [[minima_indices[i], minima_indices[i+1]] for i in range(len(minima_indices)-1)]

    return distances, maxima_indices, tap_cycles, start_index, end_index
    
def compute_velocity(distances):
    return np.diff(distances)

def compute_two_phase_speeds(distances, tap_cycles, open_indices, fps=60):
    speeds = []

    for start_idx, end_idx in tap_cycles:
        # Find open indices within the cycle
        open_in_cycle = [i for i in open_indices if start_idx <= i <= end_idx]

        if not open_in_cycle:
            # No open phase found in this cycle
            speeds.extend([None, None])
            continue

        open_idx = open_in_cycle[0]  # Use first open index in the cycle

        # --- Phase 1: Closed -> Open
        if open_idx > start_idx:
            d_start = distances[start_idx]
            d_open = distances[open_idx]
            t1 = (open_idx - start_idx) / fps
            v1 = np.abs(d_open - d_start) / t1 if t1 != 0 else None
        else:
            v1 = None

        # --- Phase 2: Open -> Closed
        if end_idx > open_idx:
            d_close = distances[end_idx]
            t2 = (end_idx - open_idx) / fps
            v2 = np.abs(d_close - d_open) / t2 if t2 != 0 else None
        else:
            v2 = None

        speeds.extend([v1,v2])

    return speeds

def compute_cycle_durations(tap_cycles):
    return [end - start for start, end in tap_cycles]

def compute_velocity_slope(velocity):
    if len(velocity) < 2:
        return 0
    return np.polyfit(np.arange(len(velocity)), velocity, 1)[0]

def compute_median_velocity(velocity):
    return np.median(np.abs(velocity)) if len(velocity) else 0

def compute_cycle_variability(durations):
    return np.std(durations) / np.mean(durations) if durations and np.mean(durations) != 0 else 0

def count_decreases(x):
    if not x or len(x) < 2:
        return 0

    count = 0
    for i in range(1, len(x)):
        if x[i] < x[i - 1]:
            count += 1

    return count/len(x) if len(x) > 0 else 0

def extract_all_features(X, X_angular, X_wrist):
    features = []
    num=0
    for distances in tqdm(X, desc="Extracting features"):
        angles = X_angular[num]
        wrist_movements = X_wrist[num]
        num+=1
        distances, open_indices, tap_cycles, start, end = find_peaks_finger_tapping(distances)
        angles = angles[start:end + 1] if len(angles) > end else angles
        wrist_movements = wrist_movements[start:end + 1] if len(wrist_movements) > end else wrist_movements
        if len(tap_cycles) < 2:
            features.append( {
            'distances': distances,
            'velocity': [],
            'tap_cycles': [],
            'speeds':[],
            'open_indices': [],
            'durations': [],
            'halts_and_hesitations': 0,  # count
            'halt_ratio': 0,                        # prevalence
            'max_halt_duration': 0,          # magnitude
            'amplitudes':[],
            'angles': [],
            'tap_angles': [],
            'velocity_slope': 0,
            'median_velocity': 0,
            'cycle_variability': 0,
            'median_speeds': 0,
            'std_speeds': 0,
            'mean_durations': 0,
            'speed_slope': 0,
            'num_taps': 0,
            'max_opening': 0,
            'amplitude_slope': 0,
            'mean_amplitude': 0,
            'std_amplitude': 0,
            'amplitude_range': 0,
            'amplitude_entropy': 0,
            'amplitude_min': 0,
            'tap_angle_slope': 0,   
            'mean_tap_angle': 0,
            'max_tap_angle': 0,
            'std_tap_angle': 0,
            'min_tap_angle': 0,
            'amplitude_decrease_count': 0,
            'angle_decrease_count': 0,
            'median_tap_angle': 0,
            
        })
            continue
        velocity = compute_velocity(distances[tap_cycles[0][0]:tap_cycles[-1][1]])  # Compute velocity only for the range of tap cycles
        durations = compute_cycle_durations(tap_cycles)
        # --- Halt/Hesitation features ---
        if durations:
            durations_arr = np.array(durations, dtype=float)
            halt_threshold = 2 * np.mean(durations_arr)

            # Count of long pauses (already requested)
            halts_and_hesitations = int(np.sum(durations_arr > halt_threshold))

            # NEW 1) Fraction of long pauses among all intervals
            halt_ratio = float(np.mean(durations_arr > halt_threshold))

            # NEW 2) Magnitude of the worst halt (in frames, consistent with durations)
            max_halt_duration = float(np.max(durations_arr))
        else:
            halts_and_hesitations = 0
            halt_ratio = 0.0
            max_halt_duration = 0.0
        speeds = compute_two_phase_speeds(distances, tap_cycles, open_indices)
        #amplitude is distane in open phase and start of cycle
        amplitudes = [distances[open_idx] - distances[tap_cycles[i][0]] for i, open_idx in enumerate(open_indices) if i < len(tap_cycles)]
        
        tap_angles = [angles[open_indices[i]] for i in range(len(open_indices))]

        features.append({
            'distances': distances,
            'velocity': velocity,
            'tap_cycles': tap_cycles,
            'speeds': speeds,
            'open_indices': open_indices,
            'durations': durations,
            'halts_and_hesitations': halts_and_hesitations,  # count
            'halt_ratio': halt_ratio,                        # prevalence
            'max_halt_duration': max_halt_duration,          # magnitude
            'amplitudes': amplitudes,
            'angles': angles,
            'tap_angles': tap_angles,
            'velocity_slope': compute_velocity_slope(velocity),
            'median_velocity': compute_median_velocity(velocity),
            'cycle_variability': compute_cycle_variability(durations),
            'median_speeds': np.median(speeds) if speeds else 0,
            'std_speeds': np.std(speeds) if speeds else 0,
            'mean_durations': np.mean(durations) if durations else 0,
            'speed_slope': np.polyfit(np.arange(len(speeds)), speeds, 1)[0] if len(speeds) > 1 else 0,
            'num_taps': len(tap_cycles),
            'max_opening': np.max([distances[i] for i in open_indices]) if open_indices else 0, 
            'amplitude_slope': np.polyfit(np.arange(len(amplitudes)), amplitudes, 1)[0] if len(amplitudes) > 1 else 0,
            'mean_amplitude': np.mean(amplitudes) if amplitudes else 0,
            'std_amplitude': np.std(amplitudes) if amplitudes else 0,
            'amplitude_range': np.max(amplitudes) - np.min(amplitudes) if amplitudes else 0,
            'amplitude_entropy': -np.sum((np.array(amplitudes) / np.sum(amplitudes)) * np.log(np.array(amplitudes) / np.sum(amplitudes))) if amplitudes else 0,
            'amplitude_min': np.min(amplitudes) if amplitudes else 0,
            'tap_angle_slope': np.polyfit(np.arange(len(tap_angles)), tap_angles, 1)[0] if len(tap_angles) > 1 else 0,
            'mean_tap_angle': np.mean(tap_angles) if tap_angles else 0,
            'max_tap_angle': np.max(tap_angles) if tap_angles else 0,
            'std_tap_angle': np.std(tap_angles) if tap_angles else 0,
            'min_tap_angle': np.min(tap_angles) if tap_angles else 0,
            'amplitude_decrease_count': count_decreases(amplitudes),
            'angle_decrease_count': count_decreases(tap_angles),
            'median_tap_angle': np.median(tap_angles) if tap_angles else 0,
        
        })
    return features



features = extract_all_features(X, X_angular, X_wrist)
df_features = pd.DataFrame(features)
df_features.set_index(valid_ids, inplace=True)

#join with michael labels
michael_labels_path = r'\\files.ubc.ca\team\PPRC\CAMERA\Booth_Processed\docs\michael-labeling.csv'
df_michael_labels = pd.read_csv(michael_labels_path)
#add 0 before id if it is 4 digit
df_michael_labels['ID'] = df_michael_labels['ID'].apply(lambda x: f"{x:05d}" if len(str(x)) == 4 else str(x))
#combine id , date and hand to generate filename
df_michael_labels['file_name'] = df_michael_labels['ID'].astype(str) + '_' + df_michael_labels['date'].astype(str) + '_' + df_michael_labels['hand'].astype(str)
df_michael_labels.set_index('file_name', inplace=True)
df_michael_labels.drop(columns=['ID', 'date', 'hand'], inplace=True, errors='ignore')
#join with labeling functions
#lf_df_combined = lf_df.join(df_michael_labels['Slowness'], how='left')
#combine_with_features
df_features_combined = df_michael_labels.join(df_features, how='left')


#find p-values for each feature between slowness and non-slowness cases
from scipy.stats import ttest_ind, mannwhitneyu
import numpy as np

#define LFS
POSITIVE = 1
NEGATIVE = 0
ABSTAIN = -1


def find_significant_features(df, y_column, feature_columns):
    # Compare groups
    group_0 = df[df[y_column] == 0]
    group_1 = df[df[y_column] == 1]
    print(f"Group 0 size: {len(group_0)}, Group 1 size: {len(group_1)}")

    # Store results
    results = []

    for col in feature_columns:
        values_0 = group_0[col].dropna()
        values_1 = group_1[col].dropna()
        
        # Use Mann-Whitney U test (non-parametric, safer for unknown distributions)
        stat, p_value = mannwhitneyu(values_0, values_1, alternative='two-sided')
        
        results.append({
            "feature": col,
            "mean_group_0": values_0.mean(),
            "mean_group_1": values_1.mean(),
            "p_value": p_value
        })

    # Convert to DataFrame
    import pandas as pd
    df_stats = pd.DataFrame(results)
    df_stats["significant"] = df_stats["p_value"] < 0.05

    print(df_stats)


def define_labeling_functions(df, feature_cols, label_col="Slowness",min_percentile=10, max_percentile=90):
    labeling_outputs = {}

    for col in feature_cols:
        group_0 = df[df[label_col] == 0][col].dropna()
        group_1 = df[df[label_col] == 1][col].dropna()

        # Get value ranges based on 90 and 10 percentiles
        min_0, max_0 = np.percentile(group_0, min_percentile), np.percentile(group_0, max_percentile)
        min_1, max_1 = np.percentile(group_1, min_percentile), np.percentile(group_1, max_percentile)

        # Overlap region
        lower_bound = max(min_0, min_1)
        upper_bound = min(max_0, max_1)

        def lf(row_val):
            if row_val < lower_bound:
                return NEGATIVE if min_0 < min_1 else POSITIVE
            elif row_val > upper_bound:
                return NEGATIVE if max_0 > max_1 else POSITIVE
            else:
                return ABSTAIN

        # Apply LF to column
        labeling_outputs[f"LF_{col}"] = df[col].apply(lf)

    return pd.DataFrame(labeling_outputs, index=df.index)

# *** Bradykinesia
# Define the features you want to test
# feature_columns = [
#     'velocity_slope',
#     'median_velocity',
#     'cycle_variability',
#     'median_speeds',
#     'std_speeds',
#     'mean_durations',
#     'speed_slope',
#     'num_taps'
# ]
feature_columns = [
    'velocity_slope',
    'median_velocity',
    'cycle_variability',
    'median_speeds',
    'std_speeds',
    'mean_durations',
    'speed_slope',
    "halts_and_hesitations",
    'halt_ratio',                        # prevalence
    'max_halt_duration',          # magnitude
    'amplitude_slope',
    'std_tap_angle',
    'angle_decrease_count',
]

# find_significant_features(df_features_combined, 'Slowness', feature_columns)
find_significant_features(df_features_combined, 'Halt/Hesitation', feature_columns)


# # *** Hypokinesia
# feature_columns_hypo = [
#     'speed_slope',
#     'max_opening',
#     'amplitude_slope',
#     'mean_amplitude',
#     'std_amplitude',
#     'amplitude_range',
#     'amplitude_entropy',
#     'amplitude_min',
#     'tap_angle_slope',
#     'mean_tap_angle',
#     'max_tap_angle',
#     'std_tap_angle',
#     'min_tap_angle',
#     'amplitude_decrease_count',
#     'angle_decrease_count',
#     'median_tap_angle'
# ]
# find_significant_features(df_features_combined, 'Decrease Amplitude', feature_columns_hypo)
# find_significant_features(df_features_combined, 'Amplitude', feature_columns_hypo)


# Define labeling functions based on the defined thresholds
# features=['amplitude_slope','std_amplitude','amplitude_range','amplitude_decrease_count']
# output_lfs = define_labeling_functions(lf_df_combined, features, label_col="Decrease Amplitude")
# # Combine with original DataFrame
# lf_df_combined = df_features_combined.join(output_lfs, how='right')

# Define labeling functions based on the defined thresholds
# features=['max_opening', 'mean_amplitude','mean_tap_angle','max_tap_angle','std_tap_angle','min_tap_angle','amplitude_entropy']
# output_lfs = define_labeling_functions(lf_df_combined, features, label_col="Amplitude")
# # Combine with original DataFrame
# lf_df_combined = df_features_combined.join(output_lfs, how='left')

# Define labeling functions based on the defined thresholds
features=['cycle_variability', 'halts_and_hesitations', 'halt_ratio', 'max_halt_duration']
#features=['velocity_slope', 'median_velocity', 'num_taps', 'median_speeds', 'mean_durations']
output_lfs = define_labeling_functions(df_features_combined, features, label_col="Halt/Hesitation")
# Combine with original DataFrame
lf_df_combined = df_features_combined.join(output_lfs, how='left')


#run weak supervision on output_lfs
from snorkel.labeling import LabelingFunction, PandasLFApplier, LFAnalysis
from snorkel.labeling.model.label_model import LabelModel 
from snorkel.labeling import LFAnalysis
from sklearn.metrics import accuracy_score, f1_score, classification_report

# Run snorkel over lf
label = 'Halt/Hesitation'
L_train = lf_df_combined[output_lfs.columns].values

# add slowness as column
L_train = np.hstack((L_train, lf_df_combined[label].values.reshape(-1, 1)))


LFAnalysis(L=L_train).lf_summary()
# train snorkel
label_model = LabelModel(cardinality=2, verbose=True,)  # change to 3 if multiclass
label_model.fit(L_train=L_train, n_epochs=500, log_freq=100, seed=123)
probs= label_model.predict_proba(L=L_train)
lf_df_combined["snorkel_label_prob_1"] = probs[:, 1]
lf_df_combined["snorkel_label"] = (probs[:, 1] > 0.7).astype(int) 
#sort by label
lf_df_combined.sort_values(by=label, ascending=False, inplace=True)

#compute accuracy

# Compute accuracy and F1 score
y_true = lf_df_combined[label].values
y_pred = lf_df_combined['snorkel_label'].values
#remove NaN values
y_pred = y_pred[~np.isnan(y_true)]
y_true = y_true[~np.isnan(y_true)]

accuracy = accuracy_score(y_true, y_pred)
class_report = classification_report(y_true, y_pred, output_dict=False)
# Print results
print(f"Accuracy: {accuracy:.4f}")
print("Classification Report:") 
print(class_report)

lf_df_combined.to_csv(r'\\files.ubc.ca\team\PPRC\CAMERA\Booth_Processed\finger_tapping\docs\snorkel_labels_halt_hesitation_85.csv')

#use feature columns to train a model
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
# Define feature columns
feature_columns = [
    'velocity_slope',
    'median_velocity',
    'cycle_variability',
    'median_speeds',
    'std_speeds',
    'mean_durations',
    'speed_slope',
    'num_taps',
    'max_opening',
    'amplitude_slope',
    'mean_amplitude',
    'std_amplitude',
    'amplitude_range',
    'amplitude_entropy',
    'amplitude_min',
    'tap_angle_slope',
    'mean_tap_angle',
    'max_tap_angle',
    'std_tap_angle',
    'min_tap_angle',
    'amplitude_decrease_count',
    'angle_decrease_count'
]

# Prepare features and labels
X = lf_df_combined[feature_columns].values
y = lf_df_combined[label].values

# Remove rows with NaN values in features or labels
valid_indices = ~np.isnan(X).any(axis=1) & ~np.isnan(y)
X = X[valid_indices]
y = y[valid_indices]

# Split the data into training and testing sets
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
# Train a Random Forest classifier
rf_classifier = RandomForestClassifier(n_estimators=100, random_state=42,class_weight="balanced")
rf_classifier.fit(X_train, y_train)
# Evaluate the model
y_pred = rf_classifier.predict(X_test)
from sklearn.metrics import accuracy_score, classification_report
# Compute accuracy and F1 score
accuracy = accuracy_score(y_test, y_pred)
class_report = classification_report(y_test, y_pred, output_dict=False)

#Threshould determinastion

healthy_mask = (y == 0)  
features = extract_all_features(X)

# Calculate cycle durations for healthy individuals
cycle_durations = [np.mean(f['durations']) for f in features if f and len(f['durations']) > 0]
healthy_durations = [cycle_durations[i] for i in range(len(cycle_durations)) if healthy_mask[i]]
threshold = np.percentile(healthy_durations, 95)  # 95th percentile as upper bound
print(f"Threshold for cycle duration: {threshold}")

#Caculate velocity slope for healthy individuals
velocity_slopes = [f['velocity_slope'] for f in features if f and len(f['velocity']) > 0]
healthy_velocity_slopes = [velocity_slopes[i] for i in range(len(cycle_durations)) if healthy_mask[i]]
healthy_velocity_slope_threshold = np.percentile(healthy_velocity_slopes, 10)  # 95th percentile as upper bound
print(f"Threshold for velocity slope: {healthy_velocity_slope_threshold}")

#Calculate median velocity for healthy individuals
median_velocities = [f['median_velocity'] for f in features if f and len(f['velocity']) > 0]
healthy_median_velocities = [median_velocities[i] for i in range(len(cycle_durations)) if healthy_mask[i]]
healthy_median_velocity_threshold = np.percentile(healthy_median_velocities, 10)  # 95th percentile as upper bound
print(f"Threshold for median velocity: {healthy_median_velocity_threshold}")

#Calculate cycle variability for healthy individuals
cycle_variabilities = [f['cycle_variability'] for f in features if f and len(f['durations']) > 0]
healthy_cycle_variabilities = [cycle_variabilities[i] for i in range(len(cycle_durations)) if healthy_mask[i]]
healthy_cycle_variability_threshold = np.percentile(healthy_cycle_variabilities, 95)  # 95th percentile as upper bound
print(f"Threshold for cycle variability: {healthy_cycle_variability_threshold}")


#calculate median speed for healthy individuals
median_speeds = [np.median(f['speeds']) for f in features if f and len(f['speeds']) > 0]
healthy_median_speeds = [median_speeds[i] for i in range(len(cycle_durations)) if healthy_mask[i]]
healthy_median_speed_threshold = np.percentile(healthy_median_speeds, 10)  # 95th percentile as upper bound
print(f"Threshold for median speed: {healthy_median_speed_threshold}")

#calculate std in speed for healthy individuals
std_speeds = [np.std(f['speeds']) for f in features if f and len(f['speeds']) > 0]
healthy_std_speeds = [std_speeds[i] for i in range(len(cycle_durations)) if healthy_mask[i]]
healthy_std_speed_threshold = np.percentile(healthy_std_speeds, 95)  # 95th percentile as upper bound
print(f"Threshold for std speed: {healthy_std_speed_threshold}")



#Bradykinesia labeling functions
def lf_bradykinesia_cycle_duration(durations, threshold=44.95):
    return POSITIVE if durations and np.mean(durations) > threshold else NEGATIVE

def lf_bradykinesia_velocity_slope(slope, threshold=-0.002):
    return POSITIVE if slope < threshold else NEGATIVE

def lf_bradykinesia_median_velocity(median_velocity, threshold=0.064):
    return POSITIVE if median_velocity < threshold else NEGATIVE

def lf_bradykinesia_cycle_variability(variability, threshold=0.447):
    return POSITIVE if variability > threshold else ABSTAIN

def lf_bradykinesia_low_movement_count(num_taps, min_taps=6):
    return POSITIVE if num_taps < min_taps else NEGATIVE

def lf_bradykinesia_low_median_speed(median_speed, threshold=7.13):
    return POSITIVE if median_speed < threshold else NEGATIVE


def apply_labeling_functions(features):
    all_lf_results = []

    for f in features:
        if f is None:
            all_lf_results.append([ABSTAIN]*5)
            continue

        lf_results = [
            lf_bradykinesia_cycle_duration(f['durations']),
            lf_bradykinesia_velocity_slope(f['velocity_slope']),
            lf_bradykinesia_median_velocity(f['median_velocity']),
            lf_bradykinesia_cycle_variability(f['cycle_variability']),
            lf_bradykinesia_low_movement_count(f['num_taps']),
            lf_bradykinesia_low_median_speed(np.median(f['speeds']) if f['speeds'] else 0),
        ]
        all_lf_results.append(lf_results)

    return pd.DataFrame(all_lf_results, columns=[
        'bradykinesia_cycle_duration',
        'bradykinesia_velocity_slope',
        'bradykinesia_median_velocity',
        'bradykinesia_cycle_variability',
        'bradykinesia_low_movement_count',
        'bradykinesia_low_median_speed',
        'bradykinesia_high_std_speed'
    ])
    


#for each case that df_micheal)labels['Slowness'] is 1 I want to see their speeds over time
#filter for slowness
slowness_cases = lf_df_combined[lf_df_combined['Slowness'] == 1].index
# Extract speeds for slowness cases
slowness_speeds = {case: features[i]['speeds'] for i, case in enumerate(lf_df_combined.index) if case in slowness_cases and 'speeds' in features[i]}
# plot speeds over time for slowness cases
import matplotlib.pyplot as plt
for case, speeds in slowness_speeds.items():
    plt.figure(figsize=(10, 5))
    plt.plot(speeds, label=f'Speeds for {case}')
    plt.title(f'Speeds Over Time for {case}')
    plt.xlabel('Frame Index')
    plt.ylabel('Speed')
    plt.legend()
    plt.show()  # Show the plot for each case

#plot speed for helthy mask
speeds =  [f['speeds'] for f in features]
healthy_speeds = [speeds[i] for i in range(len(speeds)) if healthy_mask[i]]
# plot speeds over time for healthy cases
for case, speeds in enumerate(healthy_speeds):
    plt.figure(figsize=(10, 5))
    plt.plot(speeds, label=f'Speeds for {case}')
    plt.title(f'Speeds Over Time for {case}')
    plt.xlabel('Frame Index')
    plt.ylabel('Speed')
    plt.legend()
    plt.show()  # Show the plot for each case

#for each features show them as box plot for compare between pepole with slowness and without slowness
import seaborn as sns
# Define labeling functions based on the defined thresholds
features=['max_opening', 'mean_amplitude', 'std_amplitude', 'amplitude_entropy','amplitude_min']
lf_df_combined = lf_df_combined.reset_index(drop=True)
def plot_feature_boxplots(lf_df_combined, feature_name):
    plt.figure(figsize=(12, 6))
    sns.boxplot(x='Amplitude', y=feature_name, data=lf_df_combined)
    plt.title(f'Boxplot of {feature_name} by Amplitude')
    plt.xlabel('Amplitude')
    plt.ylabel(feature_name)
    plt.show()
    
for feature in features:
    plot_feature_boxplots(lf_df_combined, feature)

#change list of dictionary features to df
expected_keys = [
    'distances', 'velocity', 'tap_cycles', 'open_indices',
    'durations', 'velocity_slope', 'median_velocity',
    'cycle_variability', 'num_taps'
]

df_features = pd.DataFrame([{
    key: (f.get(key) if f is not None else None) for key in expected_keys
} for f in features])

df_features.set_index(valid_ids, inplace=True)


# 2. Hypokinesia
def lf_hypokinesia_amplitude(data, tap_indices, threshold=10):
    """LF: Check if peak-to-peak amplitude around taps is low."""
    amplitudes = compute_amplitude_range(data['finger_distance'], tap_indices)
    if not amplitudes:
        return ABSTAIN
    avg_amplitude = np.mean(amplitudes)
    return POSITIVE if avg_amplitude < threshold else NEGATIVE

def lf_hypokinesia_rom(data, threshold=20):
    """LF: Check if range of motion (ROM) is restricted."""
    if len(data['joint_angle']) == 0:
        return ABSTAIN
    rom = np.ptp(data['joint_angle'])
    return POSITIVE if rom < threshold else NEGATIVE

def lf_hypokinesia_trajectory_deviation(data, tap_indices, threshold=5):
    """LF: Check if joint displacement around taps is small."""
    displacements = compute_amplitude_range(data['joint_angle'], tap_indices)
    if not displacements:
        return ABSTAIN
    return POSITIVE if np.mean(displacements) < threshold else NEGATIVE

def lf_hypokinesia_amplitude_consistency(data, tap_indices, threshold=0.3):
    """LF: Check variability in amplitude across taps."""
    amplitudes = compute_amplitude_range(data['finger_distance'], tap_indices)
    if not amplitudes or np.mean(amplitudes) == 0:
        return ABSTAIN
    cv = np.std(amplitudes) / np.mean(amplitudes)
    return POSITIVE if cv > threshold else ABSTAIN

def lf_hypokinesia_low_peak_distance(data, threshold=15):
    """LF: Check if peak-to-peak finger distance is low."""
    if len(data['finger_distance']) == 0:
        return ABSTAIN
    peak_distance = np.ptp(data['finger_distance'])
    return POSITIVE if peak_distance < threshold else NEGATIVE

# 3. Fatigue (Sequence Effect)
def lf_fatigue_amplitude_trend(data, tap_indices, threshold=-0.05):
    """LF: Check for negative trend in amplitude over taps."""
    amplitudes = compute_amplitude_range(data['finger_distance'], tap_indices)
    if len(amplitudes) < 2:
        return ABSTAIN
    trend = np.polyfit(range(len(amplitudes)), amplitudes, 1)[0]
    return POSITIVE if trend < threshold else NEGATIVE

def lf_fatigue_velocity_trend(data, tap_indices, window=10, threshold=-0.1):
    """LF: Check for negative trend in velocity over taps."""
    velocities = [np.mean(np.abs(data['velocity'][max(0, idx-window):min(len(data['velocity']), idx+window+1)]))
                  for idx in tap_indices]
    if len(velocities) < 2:
        return ABSTAIN
    trend = np.polyfit(range(len(velocities)), velocities, 1)[0]
    return POSITIVE if trend < threshold else NEGATIVE

def lf_fatigue_last_vs_first(data, tap_indices, ratio_threshold=0.8):
    """LF: Check ratio of last 3 taps vs first 3 taps (amplitude)."""
    amplitudes = compute_amplitude_range(data['finger_distance'], tap_indices)
    if len(amplitudes) < 6:
        return ABSTAIN
    first_three = np.mean(amplitudes[:3])
    last_three = np.mean(amplitudes[-3:])
    if first_three == 0:
        return ABSTAIN
    ratio = last_three / first_three
    return POSITIVE if ratio < ratio_threshold else NEGATIVE

def lf_fatigue_cycle_derivative(data, tap_indices, threshold=-0.01):
    """LF: Check derivative of movement amplitude across taps."""
    amplitudes = compute_amplitude_range(data['finger_distance'], tap_indices)
    if len(amplitudes) < 2:
        return ABSTAIN
    derivative = np.diff(amplitudes)
    return POSITIVE if np.mean(derivative) < threshold else NEGATIVE

def lf_fatigue_velocity_ratio(data, tap_indices, window=10, ratio_threshold=0.75):
    """LF: Check ratio of velocity in last vs first taps."""
    velocities = [np.mean(np.abs(data['velocity'][max(0, idx-window):min(len(data['velocity']), idx+window+1)]))
                  for idx in tap_indices]
    if len(velocities) < 6:
        return ABSTAIN
    first_three = np.mean(velocities[:3])
    last_three = np.mean(velocities[-3:])
    if first_three == 0:
        return ABSTAIN
    ratio = last_three / first_three
    return POSITIVE if ratio < ratio_threshold else NEGATIVE

# 4. Tremor
def lf_tremor_psd(data, freq_range=(4, 7), threshold=0.1):
    """LF: Check for high power in 4-7 Hz range (tremor frequency)."""
    freqs, psd = compute_psd(data['finger_distance'])
    if len(psd) == 0:
        return ABSTAIN
    freq_mask = (freqs >= freq_range[0]) & (freqs <= freq_range[1])
    tremor_power = np.sum(psd[freq_mask]) / (np.sum(psd) + 1e-10)
    return POSITIVE if tremor_power > threshold else NEGATIVE

def lf_tremor_oscillation_peaks(data, threshold=5):
    """LF: Check for oscillatory peaks in displacement."""
    peaks, _ = find_peaks(data['finger_distance'], distance=10)
    return POSITIVE if len(peaks) > threshold else NEGATIVE

def lf_tremor_high_freq_noise(data, threshold=0.05):
    """LF: Check for high-frequency micro-oscillations during rest."""
    freqs, psd = compute_psd(data['finger_distance'])
    if len(psd) == 0:
        return ABSTAIN
    high_freq_mask = freqs > 7
    high_freq_power = np.sum(psd[high_freq_mask]) / (np.sum(psd) + 1e-10)
    return POSITIVE if high_freq_power > threshold else NEGATIVE

def lf_tremor_cycle_irregularity(tap_indices, times, threshold=0.2):
    """LF: Check if inter-tap intervals are disrupted by tremor-like irregularities."""
    durations = compute_cycle_duration(tap_indices, times)
    if not durations or np.mean(durations) == 0:
        return ABSTAIN
    cv = np.std(durations) / np.mean(durations)
    return POSITIVE if cv > threshold else ABSTAIN

def lf_tremor_rest_oscillation(data, tap_indices, times, threshold=0.1):
    """LF: Check for oscillations during rest/hold phases."""
    # Assume rest periods are outside tap windows
    all_indices = set(range(len(data['finger_distance'])))
    tap_windows = set()
    window = 10
    for idx in tap_indices:
        tap_windows.update(range(max(0, idx-window), min(len(data['finger_distance']), idx+window+1)))
    rest_indices = list(all_indices - tap_windows)
    if not rest_indices:
        return ABSTAIN
    rest_data = data['finger_distance'][rest_indices]
    freqs, psd = compute_psd(rest_data)
    if len(psd) == 0:
        return ABSTAIN
    freq_mask = (freqs >= 4) & (freqs <= 7)
    tremor_power = np.sum(psd[freq_mask]) / (np.sum(psd) + 1e-10)
    return POSITIVE if tremor_power > threshold else NEGATIVE

# 5. Freezing or Movement Arrest
def lf_freezing_arrest_duration(data, times, threshold=0.5):
    """LF: Check for periods of no movement > 500ms."""
    velocity = np.abs(data['velocity'])
    arrests = np.where(velocity < 0.01)[0]
    if len(arrests) < 2:
        return NEGATIVE
    arrest_durations = np.diff(times[arrests])
    return POSITIVE if np.any(arrest_durations > threshold) else NEGATIVE

def lf_freezing_pose_similarity(data, threshold=0.95):
    """LF: Check for stagnant frames via pose similarity."""
    pose_vectors = data[['left_hand', 'right_hand']].values
    if len(pose_vectors) < 2:
        return ABSTAIN
    similarities = [cosine_similarity(pose_vectors[i].reshape(1, -1),
                                     pose_vectors[i+1].reshape(1, -1))[0, 0]
                    for i in range(len(pose_vectors)-1)]
    return POSITIVE if np.mean(similarities) > threshold else NEGATIVE

def lf_freezing_cycle_gaps(tap_indices, times, gap_threshold=0.5):
    """LF: Check for gaps in rhythmic tap intervals."""
    if len(tap_indices) < 2:
        return NEGATIVE
    gaps = np.diff(times[tap_indices])
    return POSITIVE if np.any(gaps > gap_threshold) else NEGATIVE

def lf_freezing_low_velocity_periods(data, times, threshold=0.01, duration=0.5):
    """LF: Check for extended low-velocity periods."""
    low_vel = np.where(np.abs(data['velocity']) < threshold)[0]
    if len(low_vel) < 2:
        return NEGATIVE
    low_vel_durations = np.diff(times[low_vel])
    return POSITIVE if np.any(low_vel_durations > duration) else NEGATIVE

def lf_freezing_arrest_count(data, threshold=3):
    """LF: Check number of motor arrests."""
    velocity = np.abs(data['velocity'])
    arrests = np.where(velocity < 0.01)[0]
    return POSITIVE if len(arrests) > threshold else NEGATIVE

# 6. Arrhythmic Motion
def lf_arrhythmic_cv_intervals(tap_indices, times, threshold=0.2):
    """LF: Check coefficient of variation of inter-tap intervals."""
    durations = compute_cycle_duration(tap_indices, times)
    if not durations or np.mean(durations) == 0:
        return ABSTAIN
    cv = np.std(durations) / np.mean(durations)
    return POSITIVE if cv > threshold else NEGATIVE

def lf_arrhythmic_frequency_deviation(tap_indices, times, threshold=0.15):
    """LF: Check tap-to-tap frequency deviation."""
    durations = compute_cycle_duration(tap_indices, times)
    if not durations or np.mean(durations) == 0:
        return ABSTAIN
    frequencies = [1 / d for d in durations]
    cv = np.std(frequencies) / np.mean(frequencies)
    return POSITIVE if cv > threshold else NEGATIVE

def lf_arrhythmic_autocorrelation(data, threshold=0.5):
    """LF: Check lack of periodicity via autocorrelation."""
    if len(data['finger_distance']) < 2:
        return ABSTAIN
    autocorr = np.correlate(data['finger_distance'], data['finger_distance'], mode='full')
    autocorr = autocorr[len(autocorr)//2:]
    if autocorr[0] == 0:
        return ABSTAIN
    max_autocorr = np.max(autocorr[1:]) / autocorr[0]
    return POSITIVE if max_autocorr < threshold else NEGATIVE

def lf_arrhythmic_amplitude_deviation(data, tap_indices, threshold=0.2):
    """LF: Check tap-to-tap amplitude deviation."""
    amplitudes = compute_amplitude_range(data['finger_distance'], tap_indices)
    if not amplitudes or np.mean(amplitudes) == 0:
        return ABSTAIN
    cv = np.std(amplitudes) / np.mean(amplitudes)
    return POSITIVE if cv > threshold else NEGATIVE

def lf_arrhythmic_irregular_peaks(data, times, threshold=0.3):
    """LF: Check irregularity in peak intervals."""
    peaks, _ = find_peaks(data['finger_distance'], distance=10)
    if len(peaks) < 2:
        return ABSTAIN
    intervals = np.diff(times[peaks])
    if np.mean(intervals) == 0:
        return ABSTAIN
    cv = np.std(intervals) / np.mean(intervals)
    return POSITIVE if cv > threshold else NEGATIVE

# 7. Asymmetry
def lf_asymmetry_speed_diff(data, threshold=0.2):
    """LF: Check difference in speed between left and right hands."""
    left_speed = np.mean(np.abs(data['left_hand']))
    right_speed = np.mean(np.abs(data['right_hand']))
    if left_speed + right_speed == 0:
        return ABSTAIN
    asymmetry = np.abs(right_speed - left_speed) / (right_speed + left_speed)
    return POSITIVE if asymmetry > threshold else NEGATIVE

def lf_asymmetry_amplitude_diff(data, tap_indices, threshold=0.2):
    """LF: Check difference in amplitude between hands."""
    left_amplitudes = compute_amplitude_range(data['left_hand'], tap_indices)
    right_amplitudes = compute_amplitude_range(data['right_hand'], tap_indices)
    if not left_amplitudes or not right_amplitudes:
        return ABSTAIN
    mean_left = np.mean(left_amplitudes)
    mean_right = np.mean(right_amplitudes)
    if mean_left + mean_right == 0:
        return ABSTAIN
    asymmetry = np.abs(mean_right - mean_left) / (mean_right + mean_left)
    return POSITIVE if asymmetry > threshold else NEGATIVE

def lf_asymmetry_temporal_delay(data, times, threshold=0.1):
    """LF: Check temporal delay between hands in bilateral tasks."""
    left_peaks, _ = find_peaks(data['left_hand'], distance=10)
    right_peaks, _ = find_peaks(data['right_hand'], distance=10)
    if len(left_peaks) == 0 or len(right_peaks) == 0 or len(left_peaks) != len(right_peaks):
        return ABSTAIN
    delays = np.abs(times[left_peaks] - times[right_peaks])
    return POSITIVE if np.mean(delays) > threshold else NEGATIVE

def lf_asymmetry_velocity_ratio(data, threshold=1.5):
    """LF: Check ratio of velocities between hands."""
    left_velocity = np.mean(np.abs(data['left_hand']))
    right_velocity = np.mean(np.abs(data['right_hand']))
    if min(left_velocity, right_velocity) == 0:
        return ABSTAIN
    ratio = max(left_velocity, right_velocity) / min(left_velocity, right_velocity)
    return POSITIVE if ratio > threshold else NEGATIVE

def lf_asymmetry_cycle_count(data, threshold=2):
    """LF: Check difference in number of cycles between hands."""
    left_peaks, _ = find_peaks(data['left_hand'], distance=10)
    right_peaks, _ = find_peaks(data['right_hand'], distance=10)
    diff = np.abs(len(left_peaks) - len(right_peaks))
    return POSITIVE if diff > threshold else NEGATIVE

# 8. Rigidity (indirect)
def lf_rigidity_limited_rom(data, threshold=20):
    """LF: Check if range of motion is limited."""
    if len(data['joint_angle']) == 0:
        return ABSTAIN
    rom = np.ptp(data['joint_angle'])
    return POSITIVE if rom < threshold else NEGATIVE

def lf_rigidity_uniform_velocity(data, threshold=0.05):
    """LF: Check for uniform velocity profiles (lack of acceleration peaks)."""
    if len(data['velocity']) < 2:
        return ABSTAIN
    velocity_std = np.std(np.diff(data['velocity']))
    return POSITIVE if velocity_std < threshold else NEGATIVE

def lf_rigidity_no_overshoot(data, tap_indices, threshold=0.1):
    """LF: Check for lack of rebound/overshoot in movements."""
    amplitudes = compute_amplitude_range(data['joint_angle'], tap_indices)
    if not amplitudes or np.mean(amplitudes) == 0:
        return ABSTAIN
    overshoot = np.std(amplitudes) / np.mean(amplitudes)
    return POSITIVE if overshoot < threshold else NEGATIVE

def lf_rigidity_stiff_trajectory(data, threshold=0.8):
    """LF: Check for stiff, linear-like trajectories."""
    trajectory = data['joint_angle']
    if len(trajectory) < 2:
        return ABSTAIN
    linearity = np.corrcoef(trajectory, np.arange(len(trajectory)))[0, 1]
    return POSITIVE if linearity > threshold else NEGATIVE

def lf_rigidity_low_acceleration(data, threshold=10):
    """LF: Check for low acceleration peaks."""
    if len(data['velocity']) < 2:
        return ABSTAIN
    acceleration = np.diff(data['velocity'])
    max_acc = np.max(np.abs(acceleration))
    return POSITIVE if max_acc < threshold else NEGATIVE

# 9. Dysmetria
def lf_dysmetria_overshoot(data, tap_indices, threshold=0.2):
    """LF: Check for overshooting tapping target."""
    amplitudes = compute_amplitude_range(data['finger_distance'], tap_indices)
    if not amplitudes or np.mean(amplitudes) == 0:
        return ABSTAIN
    cv = np.std(amplitudes) / np.mean(amplitudes)
    return POSITIVE if cv > threshold else NEGATIVE

def lf_dysmetria_path_deviation(data, threshold=0.1):
    """LF: Check for deviation from spatial path consistency."""
    trajectory = data['finger_distance']
    if len(trajectory) < 2:
        return ABSTAIN
    expected_path = np.linspace(trajectory[0], trajectory[-1], len(trajectory))
    deviation = np.mean(np.abs(trajectory - expected_path))
    return POSITIVE if deviation > threshold else NEGATIVE

def lf_dysmetria_endpoint_variability(data, tap_indices, threshold=0.15):
    """LF: Check for variability in tap endpoint positions."""
    endpoints = [data['finger_distance'][idx] for idx in tap_indices
                 if idx < len(data['finger_distance'])]
    if not endpoints or np.mean(endpoints) == 0:
        return ABSTAIN
    cv = np.std(endpoints) / np.mean(endpoints)
    return POSITIVE if cv > threshold else NEGATIVE

def lf_dysmetria_amplitude_irregularity(data, tap_indices, threshold=0.2):
    """LF: Check for irregular amplitude across taps."""
    amplitudes = compute_amplitude_range(data['finger_distance'], tap_indices)
    if not amplitudes or np.mean(amplitudes) == 0:
        return ABSTAIN
    cv = np.std(amplitudes) / np.mean(amplitudes)
    return POSITIVE if cv > threshold else NEGATIVE

def lf_dysmetria_peak_overshoot(data, threshold=15):
    """LF: Check for large peak deviations (overshooting)."""
    peaks, _ = find_peaks(data['finger_distance'], distance=10)
    if len(peaks) == 0:
        return ABSTAIN
    peak_values = data['finger_distance'][peaks]
    return POSITIVE if np.max(peak_values) > threshold else NEGATIVE