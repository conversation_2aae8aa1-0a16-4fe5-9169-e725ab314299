import os
from sklearn.ensemble import RandomForestClassifier
import sys
from sklearn.metrics import accuracy_score
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, confusion_matrix, mean_squared_error, r2_score
sys.path.insert(0, 'd:\\Atefeh\\Codes\\Python\\ParkinsonAssessment')
from datasets.load_data import load_labels_with_ID, load_emotion_task_ts_data, get_train_classifiers_set, merge_df_with_labels
from helpers.classification_analysis import load_trained_model, perform_analysis_cv, save_trained_model
from weak_supervision.LFBase import LFBaseClass

class LF3Class(LFBaseClass):
    def __init__(self, model_path = '.\\models\\best_lf3_v4.json'):
        
        self.au_sets = {
            'happy': [' AU06_r', ' AU12_r'],
            'disgust': [' AU09_r', ' AU15_r', ' AU25_r'],
            'sad': [' AU01_r', ' AU04_r', ' AU15_r'],
            'angry': [' AU04_r', ' AU05_r', ' AU07_r', ' AU23_r']
        }
        self.type='text'
        super().__init__(model_path=model_path)

    def prepare_dataset(self):
        action_units_df = load_emotion_task_ts_data(self.origin_folder, task='action_units', type=self.type, au_sets=self.au_sets)
        booth_updrs_df = load_labels_with_ID(os.path.join(self.origin_folder, 'docs'), 'booth_updrs.csv')
        merged_df = merge_df_with_labels(action_units_df, booth_updrs_df, self.predict)
        return merged_df

    def train_classifier(self):
        return super().train_classifier('AU06|AU12')

        
        
# lf3 = LF3Class()
# classifier, selected_features =  lf3.train_classifier()
# lf3.save_model(classifier, selected_features)
# lf3.evaluate_classifier(classifier, selected_features)
