"""
Configuration file for ChatGPT processing scripts.
Update these paths and settings as needed.
"""

# OpenAI Configuration
API_KEY = '********************************************************************************************************************************************************************'
MODEL = "gpt-4o-mini"  # or "gpt-4o", "gpt-4-turbo", etc.

# File Paths
CSV_PATH = r"G:\My Drive\Datasets\XAI\test_set_prompts.csv"
OUTPUT_PATH = r"G:\My Drive\Datasets\XAI"
IMAGE_BASE_PATH = r"G:\My Drive\Datasets\XAI\images"

# Processing Settings
MAX_RECORDS = None  # Set to a number to limit processing for testing, None for all
DELAY_BETWEEN_REQUESTS = 1  # Seconds to wait between API calls to avoid rate limiting
SAVE_FULL_PROMPTS = False  # Set to True to save full prompts in results (can be large)

# CSV Column Names (adjust if your CSV has different column names)
COLUMNS = {
    'file_name': 'file_name',
    'prompt': 'prompt', 
    'image_file': 'image_file'
}
