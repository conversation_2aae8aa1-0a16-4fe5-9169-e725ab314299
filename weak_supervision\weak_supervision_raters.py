import os
import pandas as pd
from snorkel.labeling.model.label_model import LabelModel

# === Configurations ===
task = 'postural_tremor'
file_location = rf'\\files.ubc.ca\team\PPRC\CAMERA\Booth_Processed\hand_movement\docs\{task}.csv'
destination_file = rf'\\files.ubc.ca\team\PPRC\CAMERA\Booth_Processed\hand_movement\docs\weak_supervision_{task}.csv'
ABSTAIN = -1  # Value used when label functions abstain

# === Load and Preprocess Labels ===
df_labels = pd.read_csv(file_location)
df_labels['Date'] = pd.to_datetime(df_labels['Date'], format='mixed').dt.strftime('%Y%m%d')
df_labels.set_index(['ID', 'Date'], inplace=True)
df_labels.columns = df_labels.columns.str.replace('Collection_UPDRS_', '', regex=False)

# Split left and right hand data
df_right = df_labels.filter(like='Right', axis=1).rename(columns=lambda col: col.replace('Right_', ''))
df_left = df_labels.filter(like='Left', axis=1).rename(columns=lambda col: col.replace('Left_', ''))
df_right['hand'] = 'right'
df_left['hand'] = 'left'

# Merge and re-index
df_union = pd.concat([df_right, df_left])
df_union.reset_index(inplace=True)
df_union.set_index(df_union['ID'].astype(str) + '_' + df_union['Date'] + '_' + df_union['hand'], inplace=True)
df_union.drop(columns=['ID', 'Date', 'hand'], inplace=True)

# === Clean Label Values ===
def extract_first_number(val):
    if isinstance(val, str):
        digits = ''.join(filter(str.isdigit, val))
        return int(digits[0]) if digits else None
    return val

df_union = df_union.applymap(extract_first_number)
df_union = df_union.apply(pd.to_numeric, errors='coerce')

# Majority voting
df_union['majority'] = df_union.mode(axis=1)[0]

# Optional: Keep single label source columns
df_union['single'] = df_union.filter(like='MG', axis=1)

# === Snorkel Weak Supervision ===
L_train = df_union.iloc[:, :5].fillna(ABSTAIN).astype(float).values
label_model = LabelModel(cardinality=5, verbose=True)
label_model.fit(L_train=L_train, n_epochs=100, log_freq=100, seed=123)

# Predict labels and probabilities
df_union["snorkel_label"] = label_model.predict(L=L_train)
probs = label_model.predict_proba(L=L_train)
for i in range(probs.shape[1]):
    df_union[f"snorkel_prob_{i}"] = probs[:, i]

# Save the final dataframe
df_union.to_csv(destination_file, index=False)
