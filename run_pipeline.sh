
#*************************************************************#
# Execute the DVC pipeline for copy and versioning data
#*************************************************************#

# Ensure the script exits if any command fails
set -e

# Define the base directories
RAW_DATA_DIR='\\files.ubc.ca\team\PPRC\Camera\CAMERA Booth Data\Booth'
PROCESSED_DATA_DIR='\\files.ubc.ca\team\PPRC\Camera\Booth_Processed'

# Ensure the base directories exist
if [ ! -d "$RAW_DATA_DIR" ]; then
  echo "Raw data directory does not exist: $RAW_DATA_DIR"
  exit 1
fi

if [ ! -d "$PROCESSED_DATA_DIR" ]; then
  echo "Processed data directory does not exist: $PROCESSED_DATA_DIR"
  exit 1
fi

# Run the DVC pipeline
dvc repro

# Check the status of the DVC pipeline
if [ $? -eq 0 ]; then
  echo "DVC pipeline ran successfully."
else
  echo "DVC pipeline failed."
  exit 1
fi

#*************************************************************#
# Execute the Jupyter Notebook to update demography
#*************************************************************#
papermill datasets/booth_data_demography.ipynb datasets/booth_data_demography_output.ipynb 

# Add the output notebook to Git
git add demography_output.ipynb

# Commit the changes if there are any
if git diff-index --quiet HEAD --; then
  echo "No changes to commit."
else
  git commit -m "Update demography analysis output"
  git push
  echo "Changes committed and pushed."
fi