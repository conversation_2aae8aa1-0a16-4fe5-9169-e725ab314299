import os
import pandas as pd
from sklearn.metrics import accuracy_score
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, confusion_matrix, mean_squared_error, r2_score
import joblib
import sys
sys.path.insert(0, 'd:\\Codes\\Python\\ParkinsonAssessment')
from helpers.classification_analysis import load_trained_model
from datasets.load_data import get_validations, load_emotion_task_ts_data, load_labels_with_ID, merge_df_with_labels

origin_folder = "D:\\Datasets\\Parkinson\\booth"
model_path = '.\\models\\best_lf3_v1.json'


# Read the emotion intensity data and the booth UPDRS score
emotion_df = load_emotion_task_ts_data(origin_folder, task='emotions', type='text')
booth_updrs_df = load_labels_with_ID(os.path.join(origin_folder, 'docs'), 'booth_updrs.csv')
predict = 'Facial_Expression_UPDRS (conventional)'
merged_df = merge_df_with_labels(emotion_df, booth_updrs_df, predict)
emotion_df = get_validations(emotion_df)




# Load the saved model
classifier, selected_features = load_trained_model(model_path)

# Prepare features and target variable
y = merged_df[predict]
X = merged_df[selected_features]
X.fillna(0, inplace=True)

# Predict on the validation set
y_pred = classifier.predict(X)

# Evaluate the classification model ans show confusion matrix accuracy and classification report
accuracy = accuracy_score(y, y_pred)
report = classification_report(y, y_pred)
cm = confusion_matrix(y, y_pred)

print(f'Accuracy: {accuracy}')
print('Classification Report:')
print(report)
print('Confusion Matrix:')
print(cm)

