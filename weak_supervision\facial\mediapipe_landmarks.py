import cv2
import mediapipe as mp
import pandas as pd
import os
from multiprocessing import Pool
import logging
logging.getLogger("mediapipe").setLevel(logging.ERROR)


# Initialize MediaPipe once globally to avoid pickling issues
mp_face_mesh = mp.solutions.face_mesh

def process_frame(frame_data):
    """Extracts landmarks from a single frame (top-level function for multiprocessing)."""
    frame_number, frame = frame_data
    face_mesh = mp_face_mesh.FaceMesh(static_image_mode=False, max_num_faces=1, refine_landmarks=True)

    frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
    results = face_mesh.process(frame_rgb)

    try:
        if results.multi_face_landmarks:
            for face_landmarks in results.multi_face_landmarks:
                landmarks = []
                for landmark in face_landmarks.landmark:
                    landmarks.extend([landmark.x, landmark.y, landmark.z])  # Store x, y, z

                return [frame_number] + landmarks
    except Exception as e:
        print(f"Error processing frame {frame_number}: {e}")

    return None  # If no face detected, return None

class VideoFacialLandmarkExtractor:
    def __init__(self, video_path, output_csv_folder):
        self.video_path = video_path
        self.output_csv_folder = output_csv_folder

    def extract_landmarks(self):
        """Extracts facial landmarks from the video."""
        video_name = os.path.splitext(os.path.basename(self.video_path))[0]
        landmarks_file = os.path.join(self.output_csv_folder, f'{video_name}_landmarks.csv')

        if os.path.exists(landmarks_file):
            return landmarks_file  # Skip if already processed

        cap = cv2.VideoCapture(self.video_path)

        frames = []
        frame_count = 0
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            frames.append((frame_count, frame))
            frame_count += 1

        cap.release()
        cv2.destroyAllWindows()

        # Use multiprocessing with an external function
        with Pool(15) as pool:
            landmarks_data = pool.map(process_frame, frames)

        # Filter out None values
        landmarks_data = [landmark for landmark in landmarks_data if landmark is not None]

        # Define column names (468 landmarks, each with x, y, z)
        columns = ['Frame'] + [f'Landmark_{i}_{axis}' for i in range(478) for axis in ['x', 'y', 'z']]
        landmarks_df = pd.DataFrame(landmarks_data, columns=columns)

        # Save to CSV
        landmarks_df.to_csv(landmarks_file, index=False)
        return landmarks_file

class FolderLandmarksExtractor:
    def __init__(self, video_folder_path, output_folder_path):
        self.video_folder_path = video_folder_path
        self.output_folder_path = output_folder_path

    def extract_landmarks(self):
        """Extract landmarks from all videos in the folder."""
        for video_file in os.listdir(self.video_folder_path):
            if video_file.endswith(('.mp4', '.avi', '.mov')):  # Ensure valid video files
                video_path = os.path.join(self.video_folder_path, video_file)
                output_csv_path = self.output_folder_path

                # Process each video
                video_landmark_extractor = VideoFacialLandmarkExtractor(video_path, output_csv_path)
                video_landmark_extractor.extract_landmarks()

if __name__ == '__main__':
    video_folder = r'\\files.ubc.ca\team\PPRC\CAMERA\Booth_Processed\facial_expression\basic_facial\text\videos'
    output_folder = r'\\files.ubc.ca\team\PPRC\CAMERA\Booth_Processed\facial_expression\basic_facial\text\mediapipe_face_landmarks'
    folder_extractor = FolderLandmarksExtractor(video_folder, output_folder)
    folder_extractor.extract_landmarks()