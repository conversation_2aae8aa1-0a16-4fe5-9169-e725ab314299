#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to read CSV file and extract JSON parts from response column, then convert to CSV.

This script:
1. Reads a CSV file with responses
2. Finds JSON parts in curly braces {} in the response column
3. Extracts and parses the JSON
4. Converts JSON data to CSV format
"""

import pandas as pd
import json
import re
import os
from pathlib import Path
import argparse
from typing import Dict, List, Optional, Any


def extract_json_from_text(text: str) -> List[Dict]:
    """
    Extract JSON objects from text that are enclosed in curly braces.
    
    Args:
        text: Text containing JSON objects
        
    Returns:
        List of parsed JSON objects
    """
    if not text or pd.isna(text):
        return []
    
    json_objects = []
    
    # Find all potential JSON objects in curly braces
    # This regex finds balanced curly braces
    brace_pattern = r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}'
    
    matches = re.findall(brace_pattern, str(text))
    
    for match in matches:
        try:
            # Try to parse as JSO<PERSON>
            json_obj = json.loads(match)
            json_objects.append(json_obj)
        except json.JSONDecodeError:
            # If direct parsing fails, try to clean up the text
            try:
                # Remove common issues like trailing commas, unquoted keys, etc.
                cleaned = clean_json_text(match)
                json_obj = json.loads(cleaned)
                json_objects.append(json_obj)
            except json.JSONDecodeError:
                print(f"Failed to parse JSON: {match[:100]}...")
                continue
    
    return json_objects


def clean_json_text(text: str) -> str:
    """
    Clean up common JSON formatting issues.
    
    Args:
        text: Raw JSON text
        
    Returns:
        Cleaned JSON text
    """
    # Remove trailing commas before closing braces/brackets
    text = re.sub(r',(\s*[}\]])', r'\1', text)
    
    # Fix unquoted keys (simple cases)
    text = re.sub(r'(\w+):', r'"\1":', text)
    
    # Fix single quotes to double quotes
    text = text.replace("'", '"')
    
    return text


def extract_json_from_csv(
    csv_path: str,
    response_column: str = 'response',
    output_path: str = None
) -> pd.DataFrame:
    """
    Extract JSON from CSV responses and convert to structured data.
    
    Args:
        csv_path: Path to input CSV file
        response_column: Name of column containing responses
        output_path: Path for output CSV file
        
    Returns:
        DataFrame with extracted JSON data
    """
    
    print(f"Reading CSV from: {csv_path}")
    
    # Read the CSV file
    try:
        df = pd.read_csv(csv_path, encoding='utf-8-sig')
        print(f"Loaded {len(df)} records")
    except Exception as e:
        print(f"Error reading CSV: {e}")
        return pd.DataFrame()
    
    # Check if response column exists
    if response_column not in df.columns:
        print(f"Column '{response_column}' not found in CSV")
        print(f"Available columns: {list(df.columns)}")
        return pd.DataFrame()
    
    # Extract JSON from each response
    extracted_data = []
    
    for index, row in df.iterrows():
        response_text = row[response_column]
        
        print(f"Processing row {index + 1}/{len(df)}")
        
        # Extract JSON objects from the response
        json_objects = extract_json_from_text(response_text)
        
        if json_objects:
            for json_obj in json_objects:
                # Create a record combining original row data with extracted JSON
                record = {
                    'original_index': index,
                    'file_name': row.get('file_name', ''),
                    'success': row.get('success', ''),
                    'timestamp': row.get('timestamp', ''),
                }
                
                # Add JSON fields with prefix to avoid conflicts
                if isinstance(json_obj, dict):
                    for key, value in json_obj.items():
                        # Flatten nested objects/arrays to strings
                        if isinstance(value, (dict, list)):
                            record[f'json_{key}'] = json.dumps(value)
                        else:
                            record[f'json_{key}'] = value
                else:
                    # If JSON is not a dict, store as string
                    record['json_content'] = json.dumps(json_obj)
                
                extracted_data.append(record)
        else:
            # No JSON found, keep original record
            record = {
                'original_index': index,
                'file_name': row.get('file_name', ''),
                'success': row.get('success', ''),
                'timestamp': row.get('timestamp', ''),
                'json_content': 'No JSON found'
            }
            extracted_data.append(record)
    
    # Create DataFrame from extracted data
    result_df = pd.DataFrame(extracted_data)
    
    print(f"Extracted {len(result_df)} records with JSON data")
    
    # Save to CSV if output path provided
    if output_path:
        try:
            result_df.to_csv(output_path, index=False, encoding='utf-8-sig')
            print(f"Results saved to: {output_path}")
        except Exception as e:
            print(f"Error saving CSV: {e}")
    
    return result_df


def analyze_json_structure(df: pd.DataFrame) -> None:
    """
    Analyze the structure of extracted JSON data.
    
    Args:
        df: DataFrame with extracted JSON data
    """
    print("\n=== JSON Structure Analysis ===")
    
    # Find all JSON columns
    json_columns = [col for col in df.columns if col.startswith('json_')]
    
    if not json_columns:
        print("No JSON columns found")
        return
    
    print(f"Found {len(json_columns)} JSON fields:")
    
    for col in json_columns:
        unique_count = df[col].nunique()
        non_null_count = df[col].notna().sum()
        print(f"  - {col}: {non_null_count} non-null values, {unique_count} unique values")
        
        # Show sample values
        sample_values = df[col].dropna().head(3).tolist()
        for i, val in enumerate(sample_values):
            val_str = str(val)[:50] + "..." if len(str(val)) > 50 else str(val)
            print(f"    Sample {i+1}: {val_str}")


def main():
    """Main function to handle command line arguments."""
    parser = argparse.ArgumentParser(
        description="Extract JSON from CSV response column and convert to structured CSV"
    )
    
    parser.add_argument(
        "--csv-path",
        default=r"G:\My Drive\Datasets\XAI\outputs\gpt5-outputs-s.csv",
        help="Path to input CSV file"
    )
    
    parser.add_argument(
        "--response-column",
        default="response",
        help="Name of column containing responses (default: response)"
    )
    
    parser.add_argument(
        "--output",
        default=r"G:\My Drive\Datasets\XAI\outputs\json-gpt5-outputs-s.csv",
        help="Path for output CSV file"
    )
    
    parser.add_argument(
        "--analyze",
        action="store_true",
        help="Show analysis of extracted JSON structure"
    )
    
    args = parser.parse_args()
    
    # Extract JSON from CSV
    result_df = extract_json_from_csv(
        args.csv_path,
        args.response_column,
        args.output
    )
    
    if not result_df.empty:
        print(f"\nExtracted data shape: {result_df.shape}")
        
        # Show analysis if requested
        if args.analyze:
            analyze_json_structure(result_df)
        
        # Show sample of results
        print(f"\nSample of extracted data:")
        print(result_df.head())
        
        # Show column names
        print(f"\nColumns in output:")
        for col in result_df.columns:
            print(f"  - {col}")


if __name__ == "__main__":
    main()
