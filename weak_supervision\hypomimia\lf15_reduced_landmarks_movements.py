import os
import pandas as pd
from sklearn.ensemble import RandomForestClassifier
import sys
sys.path.insert(0, 'd:\\Atefeh\\Codes\\Python\\ParkinsonAssessment')
from time_series.calculator import analyze_signal_shape
from weak_supervision.LFBase import LFBaseClass
from datasets.load_data import load_data_user, load_labels_with_ID, load_emotion_task_ts_data, get_train_classifiers_set, merge_df_with_labels
from helpers.classification_analysis import perform_analysis_cv, save_trained_model


class LF15Class(LFBaseClass):
    def __init__(self, model_path = '.\\models\\best_lf15_v2.json',predict='Mouth Moveme0t'):
        origin_folder = '\\\\files.ubc.ca\\team\\PPRC\\Camera\\Booth_Processed'
        super().__init__(model_path=model_path,predict=predict,origin_folder=origin_folder)
        
    
    def prepare_dataset(self):
        tasks = ['left', 'right']
        data = pd.DataFrame()  # Initialize an empty DataFrame to hold all data
        # Iterate over tasks
        for task in tasks:
            root_folder = f'{self.origin_folder}\\finger_tapping\\{task}\\csvs'
            # Iterate over all CSV files in the folder
            for file_name in os.listdir(root_folder):
                if file_name == f'mouth_movements_{task}.csv':
                    # Load CSV file and set 'ID' as the index
                    temp_data = pd.read_csv(os.path.join(root_folder, file_name))
                    if 'ID' in temp_data.columns:
                        temp_data.set_index('ID', inplace=True)
                    else:
                        print(f"No 'ID' column found in {file_name}")

                    # Join the data
                    if data.empty:
                        data = temp_data  # First data frame to join, no need for suffixes
                    else:
                        # Using join; aligns on index
                        data = data.join(temp_data, how='outer', rsuffix=f'_right', lsuffix=f'_left')
        
        happy_file = f'{self.origin_folder}\\facial_expression\\happy\\imitation\\csvs\\mouth_movements.csv'      
        temp_data = pd.read_csv(happy_file)
        if 'ID' in temp_data.columns:
            temp_data.set_index('ID', inplace=True)
        
        # Using join; aligns on index
        #data = data.join(temp_data, how='outer', rsuffix=f'_happy')       
         # Step 4: Merge the pivoted DataFrame with booth_updrs_df on the 'ID' column
        booth_updrs_df = load_labels_with_ID(os.path.join(self.origin_folder, 'docs'), 'booth_updrs.csv')
        
        data.fillna(0, inplace=True)
        #booth_updrs_df.dropna(subset=[self.predict], inplace=True)
        merged_df = merge_df_with_labels(data, booth_updrs_df, self.predict)
        #merged_df.dropna(subset=[self.predict], inplace=True)
        return merged_df

    def train_classifier(self):
        #return super().train_classifier('left|right')
        merged_df = self.prepare_dataset()
        merged_df = get_train_classifiers_set(merged_df)
        y = merged_df[self.predict]
        X = merged_df.filter(regex='Distance')
        X.fillna(0, inplace=True)
        #Use svm classifier with RFE feature selection and gaussian kernel
        from sklearn.svm import SVC
        classifier = SVC(kernel='rbf', random_state=33)
        scores, mean_score, selected_features = perform_analysis_cv(X, y, classifier=classifier, n_features_to_select=8, feature_selection_method='RFECV')
        classifier.fit(X[selected_features].values, y)
        print(f'Cross-validation scores: {scores}')
        print(f'Mean cross-validation score: {mean_score}')
        return classifier, selected_features
    
    def labeling_function(self, x):
        # remove nan values in x
        x = x.fillna(0)
        prediction = super().labeling_function(x)
        if prediction == 0:
            return -1
        else:
            
            return 4

        
model_path = f'.\\models\\best_LF15Class___v5.json'     
lf = LF15Class(model_path=model_path)
dataset = lf.prepare_dataset()
classifier, selected_features =  lf.train_classifier()

#lf.save_model(classifier, selected_features)
lf.evaluate_classifier(classifier, selected_features)


