
from pathlib import Path

import pandas as pd


class BaseDataReader:
    def __init__(self):
        pass

    def read_data(self):
        raise NotImplementedError

    @staticmethod
    def _read_csv(file_path, raise_error=False):
        if Path(file_path).exists():
            return pd.read_csv(file_path)
        else:
            if raise_error:
                raise FileNotFoundError(f"File not found: {file_path}")
            else:
                print(f"File not found: {file_path}")
                return None
            
            

class TappingDataReader(BaseDataReader):
    def __init__(self, distances_path, features_path, labels_path=None):
        super().__init__()
        self.distances_path = distances_path
        self.features_path = features_path
        self.labels_path = labels_path

    def read_data_distance_feature(self):
        distances_data = self._read_csv(self.distances_path)
        features_data = self._read_csv(self.features_path, raise_error=True)
        return distances_data, features_data
    
    
    def read_labels_data(self):
        if self.labels_path is None:
            self.labels_path = r'\\files.ubc.ca\team\PPRC\Camera\Booth_Results\reports\csvs\weak_supervision_results.csv'
        return self._read_csv(self.labels_path, raise_error=True)['Prediction']
          