import os
from copy_videos import copy_videos
from config import BASE_RAW_DIRECTORY, BASE_PROCESSED_DIRECTORY

tasks = ['both_still', 'left_open_close', 'left_up_down', 'right_open_close', 'right_up_down']

parent_task = 'hand_movement'
for task in tasks:
    dest_directory = os.path.join(BASE_PROCESSED_DIRECTORY, f'{parent_task}\\{task}\\videos')
    if not os.path.exists(dest_directory):
        os.makedirs(dest_directory)
    copy_videos(BASE_RAW_DIRECTORY, dest_directory, task, None, parent_task)
