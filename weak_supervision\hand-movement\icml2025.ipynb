{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": []}], "source": ["#Failure analysis for Clinicl\n", "\n", "import os\n", "import pandas as pd\n", "import numpy as np\n", "\n", "dir_path = r'D:\\Daneshgah\\Tez\\ICML2025\\data\\prescence'\n", "failure_percents = []\n", "#read each csv in directory\n", "for filename in os.listdir(dir_path):\n", "    file_path = os.path.join(dir_path, filename)\n", "    df = pd.read_csv(file_path)\n", "    if filename.endswith('.csv') and filename.startswith('left'):\n", "        df['failure_percent'] = (df['total_frames']-df['frames_with_left_hand'] + df['frames_with_multiple_left_hands'])/df['total_frames']\n", "    else:\n", "        df['failure_percent'] = (df['total_frames']-df['frames_with_right_hand'] + df['frames_with_multiple_right_hands'])/df['total_frames']\n", "        \n", "    failure_percents.append(df['failure_percent'])\n", "    \n", "#calculate the mean and std of failure_percent\n", "failure_percents = pd.concat(failure_percents)\n", "mean = failure_percents.mean()\n", "std = failure_percents.std()\n", "print(f\"Mean: {mean}, Std: {std}\")\n", "        \n", "    "]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": []}], "source": ["# Event Counting reading\n", "import os\n", "import pandas as pd\n", "import numpy as np\n", "\n", "nt_path = r'D:\\Daneshgah\\Tez\\ICML2025\\data\\number_of_taps.csv'\n", "df_number_of_taps = pd.read_csv(nt_path)\n", "\n", "\n", "\n", "event_path = r'D:\\Daneshgah\\Tez\\ICML2025\\data\\hand_event_analysis.csv'\n", "df_event = pd.read_csv(event_path)\n", "#seperate left and right hand finger_tapping analysis\n", "df_taps_alg = pd.DataFrame(columns=['ID', 'hand', 'count'])\n", "\n", "df_taps_alg['ID'] = df_event['User_ID']\n", "df_taps_alg['hand'] = 'left'\n", "df_taps_alg['count'] = df_event['left_tap_count']\n", "\n", "df_right = df_event[['User_ID', 'right_tap_count']].rename(columns={'User_ID': 'ID', 'right_tap_count': 'count'}).copy()\n", "df_right['hand'] = 'right'\n", "#add df_right to df_taps_alg\n", "df_taps_alg = pd.concat([df_taps_alg, df_right], ignore_index=True)\n", "\n", "\n", "df_taps_alg = df_taps_alg.join(df_number_of_taps.set_index(['ID','hand'])['Expert_Count'], on=['ID','hand'], rsuffix='_nt')\n", "\n", "df_taps_alg.dropna(inplace=True)\n", "df_taps_alg['diff'] = np.abs(df_taps_alg['count'] - df_taps_alg['Expert_Count'])/df_taps_alg['Expert_Count']\n", "#set 1 if it was more than 1\n", "df_taps_alg['diff'] = df_taps_alg['diff'].apply(lambda x: 1 if x > 1 else x)\n", "\n", "mean = df_taps_alg['diff'].mean()\n", "std = df_taps_alg['diff'].std()\n", "print(f\"Mean: {mean}, Std: {std}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": []}], "source": ["#create T-SNE plot\n", "\n", "origin_folder = r'\\\\files.ubc.ca\\team\\PPRC\\Camera\\Booth_projects\\VQAE'\n", "web_df = pd.read_csv(fr'{origin_folder}\\Csvs\\web_merge.csv')\n", "web_df.drop(columns=['Video Name','Right Hand Taps','Left Hand Taps','Hand Taps','Expert_Count'], inplace=True)\n", "web_df.set_index('file_name', inplace=True)\n", "\n", "#turn tuple to multiply of them\n", "web_df['resolution'] = web_df['resolution'].apply(lambda x: x.replace('(','').replace(')',''))\n", "web_df['resolution'] = web_df['resolution'].apply(lambda x: int(x.split(',')[0])*int(x.split(',')[1])/10000)\n", "\n", "\n", "#read booth df\n", "booth_df = pd.read_csv(fr'{origin_folder}\\Csvs\\booth_features.csv')\n", "booth_df.set_index('file_name', inplace=True)\n", "booth_df['tap_diff']  = 0\n", "#show tsne \n", "\n", "import pandas as pd\n", "from sklearn.manifold import TSNE\n", "import plotly.express as px\n", "\n", "# Drop NaNs and label column\n", "data = web_df.drop(columns=['tap_diff']).dropna()\n", "labels = web_df['tap_diff'].loc[data.index]\n", "\n", "\n", "# Fit t-SNE\n", "tsne = TSNE(n_components=2, random_state=22, perplexity=30)\n", "tsne_results = tsne.fit_transform(data)\n", "\n", "# Create a DataFrame for plotting\n", "tsne_df = pd.DataFrame(tsne_results, columns=['x', 'y'])\n", "tsne_df['label'] = labels.reset_index(drop=True)\n", "\n", "# Plot using Plotly\n", "fig = px.scatter(\n", "    tsne_df, x='x', y='y',\n", "    color='label',\n", "    color_continuous_scale='RdYlGn_r',  # green (low) to red (high)\n", "    labels={'label': 'tap_diff'},\n", "    title='t-SNE visualization',\n", ")\n", "\n", "# Customize layout\n", "fig.update_traces(marker=dict(size=10))  # bigger dots\n", "fig.update_layout(\n", "    width=700,\n", "    height=700,\n", "    plot_bgcolor='white',\n", "    paper_bgcolor='white',\n", "    coloraxis_colorbar=dict(title='tap_diff')\n", ")\n", "\n", "fig.show()\n", "\n"]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": []}, {"name": "stdout", "output_type": "stream", "text": []}], "source": ["import pandas as pd\n", "import umap\n", "import plotly.express as px\n", "\n", "# Drop NaNs and label column\n", "data = web_df.drop(columns=['tap_diff','resolution']).dropna()\n", "labels = web_df['tap_diff'].loc[data.index]\n", "\n", "# Fit UMAP\n", "umap_model = umap.UMAP(n_components=2, random_state=42)\n", "umap_results = umap_model.fit_transform(data)\n", "\n", "# Create a DataFrame for plotting\n", "umap_df = pd.DataFrame(umap_results, columns=['x', 'y'])\n", "umap_df['label'] = labels.reset_index(drop=True)\n", "\n", "# Plot using Plotly\n", "fig = px.scatter(\n", "    umap_df, x='x', y='y',\n", "    color='label',\n", "    color_continuous_scale='RdYlGn_r',  # green (low) to red (high)\n", "    labels={'label': 'tap_diff'},\n", "    title='UMAP visualization',\n", ")\n", "\n", "# Customize layout\n", "fig.update_traces(marker=dict(size=10))  # bigger dots\n", "fig.update_layout(\n", "    width=700,\n", "    height=700,\n", "    plot_bgcolor='white',\n", "    paper_bgcolor='white',\n", "    coloraxis_colorbar=dict(title='tap_diff')\n", ")\n", "\n", "fig.show()\n"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": []}, {"name": "stdout", "output_type": "stream", "text": []}, {"name": "stdout", "output_type": "stream", "text": []}], "source": ["import pandas as pd\n", "from sklearn.manifold import TSNE\n", "import umap\n", "import plotly.express as px\n", "\n", "# Load and preprocess web_df\n", "origin_folder = r'\\\\files.ubc.ca\\team\\PPRC\\Camera\\Booth_projects\\VQAE'\n", "web_df = pd.read_csv(fr'{origin_folder}\\Csvs\\web_merge.csv')\n", "web_df.drop(columns=['Video Name', 'Right Hand Taps', 'Left Hand Taps', 'Hand Taps', 'Expert_Count'], inplace=True)\n", "web_df.set_index('file_name', inplace=True)\n", "web_df['resolution'] = web_df['resolution'].apply(lambda x: x.replace('(', '').replace(')', ''))\n", "web_df['resolution'] = web_df['resolution'].apply(lambda x: int(x.split(',')[0]) * int(x.split(',')[1]) / 10000)\n", "\n", "# Load and preprocess booth_df\n", "booth_df = pd.read_csv(fr'{origin_folder}\\Csvs\\booth_features.csv')\n", "booth_df.set_index('file_name', inplace=True)\n", "booth_df['tap_diff'] = 0\n", "\n", "booth_df['resolution'] = booth_df['resolution'].apply(lambda x: x.replace('(', '').replace(')', ''))\n", "booth_df['resolution'] = booth_df['resolution'].apply(lambda x: int(x.split(',')[0]) * int(x.split(',')[1]) / 10000)\n", "\n", "# Combine both datasets (only common columns will be used)\n", "combined_df = pd.concat([web_df, booth_df], axis=0, join='inner')\n", "combined_df = combined_df.dropna()\n", "\n", "# Create source label\n", "source = ['web'] * len(web_df) + ['booth'] * len(booth_df)\n", "source = pd.Series(source, index=list(web_df.index) + list(booth_df.index))\n", "source = source.loc[combined_df.index]\n", "\n", "# Extract features and labels\n", "features = combined_df.drop(columns=['tap_diff'])\n", "labels = combined_df['tap_diff']\n", "\n", "# ========== t-SNE ==========\n", "tsne = TSNE(n_components=2, random_state=42, perplexity=30)\n", "tsne_results = tsne.fit_transform(features)\n", "\n", "tsne_df = pd.DataFrame(tsne_results, columns=['x', 'y'])\n", "tsne_df['label'] = labels.reset_index(drop=True)\n", "tsne_df['source'] = source.reset_index(drop=True)\n", "\n", "fig_tsne = px.scatter(\n", "    tsne_df, x='x', y='y',\n", "    color='label',\n", "    symbol='source',\n", "    symbol_map={'web': 'star', 'booth': 'circle'},\n", "    color_continuous_scale='RdYlGn_r',\n", "    labels={'label': 'tap_diff', 'source': 'Data Source'},\n", "    title='t-SNE: Web vs Booth'\n", ")\n", "\n", "fig_tsne.update_traces(marker=dict(size=10))\n", "fig_tsne.update_layout(\n", "    width=700, height=700,\n", "    plot_bgcolor='white',\n", "    paper_bgcolor='white',\n", "    coloraxis_colorbar=dict(title='tap_diff')\n", ")\n", "fig_tsne.show()\n", "\n", "# ========== UMAP ==========\n", "umap_model = umap.UMAP(n_components=2, random_state=42)\n", "umap_results = umap_model.fit_transform(features)\n", "\n", "umap_df = pd.DataFrame(umap_results, columns=['x', 'y'])\n", "umap_df['label'] = labels.reset_index(drop=True)\n", "umap_df['source'] = source.reset_index(drop=True)\n", "\n", "fig_umap = px.scatter(\n", "    umap_df, x='x', y='y',\n", "    color='label',\n", "    symbol='source',\n", "    symbol_map={'web': 'star', 'booth': 'circle'},\n", "    color_continuous_scale='RdYlGn_r',\n", "    labels={'label': 'tap_diff', 'source': 'Data Source'},\n", "    title='UMAP: Web vs Booth'\n", ")\n", "\n", "fig_umap.update_traces(marker=dict(size=10))\n", "fig_umap.update_layout(\n", "    width=700, height=700,\n", "    plot_bgcolor='white',\n", "    paper_bgcolor='white',\n", "    coloraxis_colorbar=dict(title='tap_diff')\n", ")\n", "fig_umap.show()\n"]}], "metadata": {"kernelspec": {"display_name": "assessment", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.5"}}, "nbformat": 4, "nbformat_minor": 2}