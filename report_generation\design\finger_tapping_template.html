<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Finger Tapping Report</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/styles.css">
</head>
<body>

<!-- Header Section -->
<div class="header">
    <h1>Finger Tapping Report</h1>
</div>

<div class="report-container">
    <!-- Task Overview Card -->
    <div class="card">
        <h3 class="card-title">Task Overview</h3>
        <div class="row">
            <div class="col-md-9">
                <p>
                    In the <strong>Finger Tapping Task</strong>, each hand is tested separately to evaluate fine motor control. The patient taps their index finger on their thumb 10 times, aiming to perform the taps as quickly and with as large an amplitude as possible. Performance is rated based on speed, amplitude, hesitations, halts, and decrements in amplitude. A score of <strong>0</strong> indicates normal tapping with no issues, while scores range from <strong>1</strong> (slight interruptions or slowing) to <strong>4</strong> (severe impairment).
                </p>
            </div>
            <div class="col-md-3 text-center" style="margin-top: 15px;">
                <img src="./resources/finger-tapping-task.png" alt="Finger Tapping Task" class="img-fluid rounded">
            </div>
        </div>
    </div>

    <!-- Results Summary Card -->
    <div class="card">
        <h3 class="card-title">Results Summary</h3>
        <table class="table table-bordered">
            <thead>
                <tr>
                    <th>Hand</th>
                    <th>MDS-UPDRS Score</th>
                    <th>Interpretation</th>
                    <th>Percentile</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Left</td>
                    <td>{{ left_features['updrs score'] }}</td>
                    <td>{{ left_features['interpretation'] }}</td>
                    <td>
                        <img src="data:image/png;base64,{{ left_percentile_chart }}" alt="UPDRS Distribution Chart Left" class="img-fluid">
                        <br/>z-score: {{ left_features['z-score'] }}
                    </td>
                </tr>
                <tr>
                    <td>Right</td>
                    <td>{{ right_features['updrs score'] }}</td>
                    <td>{{ right_features['interpretation'] }}</td>
                    <td>
                        <img src="data:image/png;base64,{{ right_percentile_chart }}" alt="UPDRS Distribution Chart Right" class="img-fluid">
                        <br/>z-score: {{ right_features['z-score'] }}
                    </td>
                </tr>
            </tbody>
        </table>
    </div>

    <!-- Detailed Performance Metrics Card -->
    <div class="card">
        <h3 class="card-title">Detailed Performance Metrics</h3>
        <table class="table table-bordered">
            <thead>
                <tr>
                    <th>Metric</th>
                    <th>Left Hand</th>
                    <th>Right Hand</th>
                    <th>Reference Range*</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Speed (taps/sec)</td>
                    <td>{{ left_features['speed'] }}</td>
                    <td>{{ right_features['speed'] }}</td>
                    <td>2.0 - 3.0</td>
                </tr>
                <tr>
                    <td>Total Number of Taps</td>
                    <td>{{ left_features['taps'] }}</td>
                    <td>{{ right_features['taps'] }}</td>
                    <td></td>
                </tr>
                <tr>
                    <td>Amplitude (max. angle)</td>
                    <td>{{ left_features['amplitude'] }}</td>
                    <td>{{ right_features['amplitude'] }}</td>
                    <td>70 - 90</td>
                </tr>
                <tr>
                    <td>Hesitations/Halts</td>
                    <td>{{ left_features['hesitation/halt'] }}</td>
                    <td>{{ right_features['hesitation/halt'] }}</td>
                    <td>0</td>
                </tr>
                <tr>
                    <td>Decrementing Amplitude</td>
                    <td>{{ left_features['decrement amplitude'] }}</td>
                    <td>{{ right_features['decrement amplitude'] }}</td>
                    <td>0 - 10</td>
                </tr>
            </tbody>
        </table>
        <p style="font-size: 0.8rem; font-style: italic;">
            * Reference ranges are approximate and based on data from the healthy finger-tapping population.
        </p>
    </div>

    <!-- Finger Distance Chart Card -->
    <div class="card">
        <h3 class="card-title">Fingers Distance Over Time</h3>
        <div class="row">
            <div class="col-md-6 text-center">
                <h5>Left Hand</h5>
                <img src="data:image/png;base64,{{ left_chart }}" alt="Finger Tapping Task" class="img-fluid rounded">  
            </div>
            <div class="col-md-6 text-center">
                <h5>Right Hand</h5>
                <img src="data:image/png;base64,{{ right_chart }}" alt="Finger Distance Chart Right Hand" class="img-fluid">
            </div>
        </div>
    </div>

    <!-- Gallery Section -->
    <div class="card">
        <h3 class="card-title">Finger Tapping Gallery</h3>
        <div class="gallery-container">
            <div class="row">
                <div class=" col-md-3 gallery-item">
                    <img src="data:image/png;base64,{{ left_min_image }}" alt="Left Hand Tapping">
                </div>
                <div class="col-md-3 gallery-item">
                    <img src="data:image/png;base64,{{ left_max_image }}" alt="Left Hand Open">
                </div>
                <div class="col-md-3 gallery-item">
                    <img src="data:image/png;base64,{{ right_min_image}}" alt="Right Hand Tapping">
                </div>
                <div class="col-md-3 gallery-item">
                    <img src="data:image/png;base64,{{ right_max_image }}" alt="Right Hand Open">
                </div>

            </div>
        </div>
    </div>
</div>

<!-- Footer Section -->
<div class="footer">
    <p>Patient ID: 07137 | Date: 2024-06-11</p>
</div>

<script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@4.5.2/dist/js/bootstrap.bundle.min.js"></script>

</body>
</html>
