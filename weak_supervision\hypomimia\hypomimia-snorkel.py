import sys

from sklearn.metrics import accuracy_score
from sklearn.metrics import classification_report, confusion_matrix
sys.path.insert(0, 'd:\\Codes\\Python\\ParkinsonAssessment')
import os
from datasets.load_data import get_validations, load_emotion_task_ts_data, load_labels_with_ID, merge_df_with_labels
from weak_supervision.SnorkelBase import SnorkelBase
from weak_supervision.hypomimia.lf10_blink_rate import LF10Class
from weak_supervision.hypomimia.lf3_AU_intensity import LF3Class
from weak_supervision.hypomimia.lf4_emotion_intensity import LF4Class
from weak_supervision.hypomimia.lf7_neutral_dominance import LF7Class
from weak_supervision.hypomimia.lf9_reduced_eyebrow import LF9Class

class HypomimiaSnorkel(SnorkelBase):
    def run(self):
        self.define_labeling_functions()
        self.create_df()
        probabilistic_labels, L_train = self.create_labels()

                # Evaluating individual labeling functions
        true_labels = self.df[self.predict]
        true_labels.fillna(0, inplace=True) 
        for i, lf in enumerate(self.labeling_functions):
            lf_name = lf.name
              # Your ground truth column
            lf_labels = L_train[:, i]
             # Fill NaN values with -1
            valid_indices = lf_labels != -1  # Filter out unlabeled instances
            if any(valid_indices):
                print(f"Evaluating {lf_name}:")
                accuracy = accuracy_score(true_labels[valid_indices], lf_labels[valid_indices])
                report = classification_report(true_labels[valid_indices], lf_labels[valid_indices], zero_division=0)
                print(f"Accuracy: {accuracy}")
                print("Classification Report:")
                print(report)
                
        self.df['predicted_labels'] = probabilistic_labels.argmax(axis=1)
        val_df = self.df
        # Evaluate overall performance
        y_pred = val_df['predicted_labels']
        y_true = true_labels
        accuracy = accuracy_score(y_true, y_pred)
        report = classification_report(y_true, y_pred)
        cm = confusion_matrix(y, y_pred)
        print(f'Accuracy: {accuracy}')
        print('Classification Report:')
        print(report)
        print('Confusion Matrix:')
        print(cm)
        return accuracy



hs = HypomimiaSnorkel(labeling_function_classes=[LF3Class, LF4Class, LF9Class, LF7Class, LF10Class ],predict='Facial_Expression_UPDRS (conventional)')
hs.run()
