import matplotlib.pyplot as plt
import os

def plot_time_series(file_name, y, x=None, title = 'Distance Over Time' ,mins=None, maxes=None, output_dir=None, xlabel='Frame', ylabel='Distance'):
    plt.figure(figsize=(10, 6))
    if x is None:
        plt.plot(y, label=ylabel)
    else:
        plt.plot(x, y)
    
    if mins is not None:
        plt.scatter(mins, [y[i] for i in mins], color='red', label='Min', marker='x')
    if maxes is not None:
        plt.scatter(maxes, [y[i] for i in maxes], color='green', label='Max', marker='o')
    plt.xlabel(xlabel)
    plt.ylabel(ylabel)
    plt.title(title)
    plt.legend()
    plt.grid(True)
    
    if output_dir is None:
        plt.show()
    else:
        output_path = os.path.join(output_dir, f'{file_name}')
        plt.savefig(output_path)
        plt.close()
        
def plot_time_series_start_end(file_name, y, start_index, end_index, x=None, title='Distance Over Time', mins=None, maxes=None, output_dir=None, xlabel='Frame', ylabel='Distance'):
    plt.figure(figsize=(10, 6))
    
    if x is None:
        x = range(len(y))
    
    # Plot the entire time series
    #plt.plot(x, y, label=ylabel, color='blue')
    
    # Highlight the selected segment
    plt.plot(x[start_index:end_index+1], y[start_index:end_index+1], color='blue', label='Tapping Segment')
    
    if mins is not None:
        mins = [i for i in mins if i >= start_index and i <= end_index]
        plt.scatter(mins, [y[i] for i in mins], color='red', label='Tap', marker='x')
    if maxes is not None:
        maxes = [i for i in maxes if i >= start_index and i <= end_index]
        plt.scatter(maxes, [y[i] for i in maxes], color='green', label='Open', marker='o')
    
    plt.xlabel(xlabel)
    plt.ylabel(ylabel)
    plt.title(title)
    plt.legend()
    plt.grid(True)
    
    if output_dir is None:
        plt.show()
    else:
        output_path = os.path.join(output_dir, f'{file_name}')
        plt.savefig(output_path)
        plt.close()