import os
from sklearn.ensemble import RandomForestClassifier
import sys
sys.path.insert(0, 'd:\\Atefeh\\Codes\\Python\\ParkinsonAssessment')
from weak_supervision.LFBase import LFBaseClass
from datasets.load_data import load_labels_with_ID, load_emotion_task_ts_data, get_train_classifiers_set, merge_df_with_labels
from helpers.classification_analysis import perform_analysis_cv, save_trained_model


class LF4Class(LFBaseClass):
    def __init__(self, model_path = '.\\models\\best_lf4_v3.json'):
        
        self.type='text'
        super().__init__(model_path=model_path)

    def prepare_dataset(self):
        df = load_emotion_task_ts_data(self.origin_folder, task='emotions', type=self.type)
        booth_updrs_df = load_labels_with_ID(os.path.join(self.origin_folder, 'docs'), 'booth_updrs.csv')
        merged_df = merge_df_with_labels(df, booth_updrs_df, self.predict)
        return merged_df

    def train_classifier(self):
        return super().train_classifier('mean|median|max|min|variance|skewness|kurtosis')

        
        
# lf = LF4Class()
# classifier, selected_features =  lf.train_classifier()
# # lf.save_model(classifier, selected_features)
# lf.evaluate_classifier(classifier, selected_features)


