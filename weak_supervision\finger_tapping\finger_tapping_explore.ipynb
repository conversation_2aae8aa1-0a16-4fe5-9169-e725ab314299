import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
import os
from IPython.display import display, HTML

def load_features_labels(features_file, labels_file):
    features_df = pd.read_csv(features_file)
    labels_df = pd.read_csv(labels_file)
    return features_df, labels_df

def merge_features_labels(features_df, labels_df):
    merged_df = pd.merge(features_df, labels_df, on='file_name')
    return merged_df

def plot_feature_distributions(df,output_dir):
    feature_columns = df.columns.difference(['file_name', 'label'])
    
    for feature in feature_columns:
        if  'Unnamed' in feature:
            continue
        fig_violin = px.violin(df, x='label', y=feature, box=True, points="all", title=f'Violin Plot of {feature}')
        
        display(HTML(f'<h2>Violin Plot of {feature}</h2>'))
        display(fig_violin)
        
        #save fig  results as html
        fig_violin.write_html(os.path.join(output_dir, f'{feature}_violin.html'))
        
        


if __name__ == "__main__":
    features_file = r'\\files.ubc.ca\team\PPRC\Camera\Booth_Results\finger_tapping_ws\features\left_features_extracted.csv'
    labels_file = r'\\files.ubc.ca\team\PPRC\Camera\Booth_Results\finger_tapping_ws\labels\left.csv'
    output_dir = r'\\files.ubc.ca\team\PPRC\Camera\Booth_Results\finger_tapping_ws\compare_features'
    

    features_df, labels_df = load_features_labels(features_file, labels_file)
    merged_df = merge_features_labels(features_df, labels_df)
    plot_feature_distributions(merged_df,output_dir)



def get_ground_truth_label(x):
    # If the file name starts with '07', the label is NEGATIVE, otherwise POSITIVE
    return 0 if x.startswith('07') else 1
for task in ['left']:
    features_file = fr'\\files.ubc.ca\team\PPRC\Camera\Booth_Results\finger_tapping_ws\features\all_features_extracted.csv'
    data = pd.read_csv(features_file)
    L_train = data.drop(columns=['file_name']).to_numpy()
    file_names = data['file_name']

    # Determine the ground truth labels based on file_name
    #abels_file = r'\\files.ubc.ca\team\PPRC\Camera\Booth_Results\finger_tapping_ws\labels\left.csv'
    #labels_df = pd.read_csv(labels_file)
    #data = pd.merge(data, labels_df, on='file_name')
    ground_truths = data['file_name'].apply(get_ground_truth_label).to_numpy()
    
    #train a random forest classifier to predict the labels
    from sklearn.ensemble import RandomForestClassifier
    from sklearn.model_selection import cross_val_score
    clf = RandomForestClassifier(n_estimators=200,random_state=32)
    scores = cross_val_score(clf, L_train[:,-22:], ground_truths, cv=5, scoring='accuracy' )
    print(f'Accuracy for {task} task: {scores.mean()}')
    
    
    

import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestClassifier
from sklearn.svm import SVC
from xgboost import XGBClassifier
from sklearn.feature_selection import SelectKBest, f_classif
from sklearn.model_selection import GridSearchCV, cross_val_score
from sklearn.pipeline import Pipeline

# Define the classifiers and their parameter grids
classifiers = {
 #   'RandomForest': RandomForestClassifier(random_state=32),
    'SVM': SVC(random_state=32),
#    'XGBoost': XGBClassifier(random_state=32)
}

param_grids = {
    # 'RandomForest': {
    #     'clf__n_estimators': [100, 200, 300],
    #     'clf__max_depth': [None, 10, 20, 30]
    # },
    'SVM': {
        'clf__C': [0.1, 1, 10, 100],
        'clf__gamma': ['scale', 'auto'],
        'clf__kernel': ['linear', 'rbf']
    },
    # 'XGBoost': {
    #     'clf__n_estimators': [100, 200, 300],
    #     'clf__learning_rate': [0.01, 0.1, 0.2],
    #     'clf__max_depth': [3, 5, 7]
    # }
}

# Perform grid search for each classifier with feature selection
best_estimators = {}
for name, clf in classifiers.items():
    print(f"Running GridSearchCV for {name}...")
    pipeline = Pipeline([
        ('feature_selection', SelectKBest(score_func=f_classif, k=10)),
        ('clf', clf)
    ])
    grid_search = GridSearchCV(estimator=pipeline, param_grid=param_grids[name], 
                               scoring='accuracy', cv=5, n_jobs=-1)
    grid_search.fit(L_train[:, :], ground_truths)
    best_estimators[name] = grid_search.best_estimator_
    print(f"Best parameters for {name}: {grid_search.best_params_}")
    print(f"Best accuracy for {name}: {grid_search.best_score_}\n")

# Compare the best models
for name, estimator in best_estimators.items():
    scores = cross_val_score(estimator, L_train[:, -22:], ground_truths, cv=5, scoring='accuracy')
    print(f'Accuracy for {name}: {scores.mean()}')

#Merge all
source = r'\\files.ubc.ca\team\PPRC\Camera\Booth_Results\finger_tapping_ws\features\right_features_extracted.csv'
dest = r'\\files.ubc.ca\team\PPRC\Camera\Booth_Processed\docs\new booth videos sheet.xlsx'
#read the data
data = pd.read_csv(source)
data['patient info'] = data['file_name'].str.split('_').str[0]
#change it to int
data['patient info'] = data['patient info'].astype(int)
#read the excel file
sheet = pd.read_excel(dest)
#remove first row
sheet = sheet.iloc[1:]

#merge the two dataframes
merged = pd.merge(data, sheet, on='patient info')
#save the merged dataframe
merged.to_csv(r'\\files.ubc.ca\team\PPRC\Camera\Booth_Results\finger_tapping_ws\features\right_features_extracted_with_labels.csv', index=False)

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import confusion_matrix, accuracy_score
from snorkel.labeling.model.label_model import LabelModel 
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split

# Constants for labels
NEGATIVE = 0
POSITIVE = 1



def run_weak_supervision(L_train, ground_truths, output_file,names):

    # Train the label model
    label_model = LabelModel(cardinality=2, verbose=True)
    label_model.fit(L_train=L_train, n_epochs=500, log_freq=100, seed=123)

    # Predict on the training set
    preds_train, probs = label_model.predict(L=L_train, return_probs=True)

    # Calculate accuracy
    accuracy = accuracy_score(y_test, preds_train)
    print(f"Accuracy: {accuracy}")

    # Optionally, save or return the predicted labels
    #data['predicted_labels'] = preds_train
    #data.to_csv(output_file, index=False)

    # Plot confusion matrix
    cm = confusion_matrix(ground_truths, preds_train)
    plt.figure(figsize=(8, 6))
    sns.heatmap(cm, annot=True, fmt="d", cmap="Blues", xticklabels=['Non PD', 'PD'], yticklabels=['Non PD', 'PD'])
    plt.xlabel('Predicted Label')
    plt.ylabel('True Label')
    plt.title('Confusion Matrix')
    plt.show()

    # Scatter plot from confusion matrix with a little noise added to the above results
    noise = np.random.normal(0, 0.01, preds_train.shape)
    noisy_preds_train = preds_train + noise
    plt.figure(figsize=(8, 6))
    plt.scatter(ground_truths, noisy_preds_train, alpha=0.6)
    plt.xlabel('True Label')
    plt.ylabel('Predicted Label with Noise')
    plt.title('Scatter Plot with Noise')
    plt.show()
    
    
    # Create the combined DataFrame
    combined_df = pd.DataFrame(L_train,columns=names)
    #combined_df['prob_label'] = probs[:, POSITIVE]
    combined_df['y_pred'] = preds_train

    # Create a column for y-tick labels based on ground truth
    combined_df['subject_label'] = ['PD_{}'.format(i) if gt == 1 else 'HC_{}'.format(i) for i, gt in enumerate(ground_truths)]

    # Sort the DataFrame based on the subject_label
    combined_df = combined_df.sort_values(by='subject_label').reset_index(drop=True)

    # Extract the sorted y-tick labels
    y_labels = combined_df['subject_label']

    # Drop the subject_label column for heatmap plotting
    combined_df = combined_df.drop(columns=['subject_label'])

    # Create a custom color map
    cmap = sns.color_palette(["white", "blue", "red"])

    # Create a figure with specified aspect ratio to ensure square cells
    plt.figure(figsize=(12, 8))
    ax = sns.heatmap(combined_df, cmap=cmap, cbar=False, yticklabels=y_labels, square=True, linewidths=0.5, linecolor='black')
    plt.title('Heatmap of Labeling Functions, Probabilistic Label, and y_pred')

    # Draw the grid
    ax.set_xticks([x + 0.5 for x in ax.get_xticks()], minor=True)
    ax.set_yticks([y + 0.5 for y in ax.get_yticks()], minor=True)
    ax.grid(which='minor', color='black', linestyle='-', linewidth=1)

    plt.show()


for task in ['left']:     
    labels_file = fr'\\files.ubc.ca\team\PPRC\Camera\Booth_Results\finger_tapping_ws\LFs\{task}_labels_2.csv'
    labels_df = pd.read_csv(labels_file)
    output_file = fr'\\files.ubc.ca\team\PPRC\Camera\Booth_Results\finger_tapping_ws\snorkel_output\{task}_snorkel.csv'
    file_names = labels_df['file_name']
    ground_truths = file_names.apply(get_ground_truth_label)
    _, L_train, _, y_test = train_test_split(labels_df.drop(columns=['file_name']).to_numpy(), ground_truths, test_size=0.5, random_state=42)
    ground_truths = y_test
    
    #sort L_train based on the ground truth
    run_weak_supervision(L_train, ground_truths, output_file,labels_df.columns[1:])


import pandas as pd
import numpy as np
# Define tasks
tasks = ['left', 'right']

# Initialize an empty list to store DataFrames
dfs = []

# Loop through tasks and concatenate files
for task in tasks:
    #file_1 = rf'\\files.ubc.ca\\team\\PPRC\\Camera\\Booth_Results\\finger_tapping_ws\\LFs\\{task}_labels_2.csv'
    file_2 = rf'\\files.ubc.ca\\team\\PPRC\\Camera\\Booth_Results\\finger_tapping_ws\\LFs\\{task}_labels.csv'
    
    # Read the CSV files
    #df1 = pd.read_csv(file_1)
    df2 = pd.read_csv(file_2)
    
    # Concatenate the two DataFrames
    #df = pd.concat([df1, df2])
    df = df2
    # Extract the id from the file_name column
    df['id'] = df['file_name'].str.split('_').str[0]
    
    # Add the task as a column
    df['task'] = task
    
    # Append to the list
    dfs.append(df)

# Concatenate all DataFrames
all_data = pd.concat(dfs)

# Pivot the data to get separate columns for left and right features
pivot_data = all_data.pivot_table(index='id', columns='task', values=[col for col in all_data.columns if col not in ['id', 'task', 'file_name']], aggfunc='first')

# Flatten the hierarchical columns
pivot_data.columns = ['_'.join(col).strip() for col in pivot_data.columns.values]

# Reset index to get 'id' back as a column
pivot_data.reset_index(inplace=True)
pivot_data.fillna(-1, inplace=True)
# Order columns to have left first, then right
left_columns = [col for col in pivot_data.columns if col.endswith('_left')]
right_columns = [col for col in pivot_data.columns if col.endswith('_right')]
ordered_columns = ['id'] + left_columns + right_columns

# Reorder the columns
pivot_data = pivot_data[ordered_columns]


import matplotlib.patches as mpatches
from snorkel.labeling.model.label_model import LabelModel
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import confusion_matrix, accuracy_score
from sklearn.model_selection import train_test_split
from sklearn.metrics import recall_score
from sklearn.metrics import f1_score
NEGATIVE = 0
POSITIVE = 1
def get_ground_truth_label(x):
    # If the file name starts with '07', the label is NEGATIVE, otherwise POSITIVE
    return NEGATIVE if x.startswith('07') else POSITIVE
def run_weak_supervision_new(L_train, ground_truths, output_file,names):

    # Train the label model
    label_model = LabelModel(cardinality=2, verbose=True)
    label_model.fit(L_train=L_train, n_epochs=100, log_freq=100, seed=12)

    # Predict on the training set
    preds_train, probs = label_model.predict(L=L_train, return_probs=True)

    # Calculate accuracy
    accuracy = accuracy_score(y_test, preds_train)
    print(f"Accuracy: {accuracy}")
    # f1-score

    f1 = f1_score(y_test, preds_train)
    print(f"F1 Score: {f1}")
    # recall score
    recall = recall_score(y_test, preds_train)
    print(f"Recall Score: {recall}")


    # Plot confusion matrix
    cm = confusion_matrix(ground_truths, preds_train)
    plt.figure(figsize=(8, 6))
    sns.heatmap(cm, annot=True, fmt="d", cmap="Blues", xticklabels=['Non PD', 'PD'], yticklabels=['Non PD', 'PD'])
    plt.xlabel('Predicted Label')
    plt.ylabel('True Label')
    plt.title('Confusion Matrix')
    plt.show()
    
    


    # Assuming L_train, names, preds_train, and ground_truths are already defined

    # Create the combined DataFrame
    combined_df = pd.DataFrame(L_train, columns=names)
    combined_df['y_pred'] = preds_train
    

    # Create a column for y-tick labels based on ground truth
    combined_df['subject_label'] = ['PD_{}'.format(i) if gt == 1 else 'HC_{}'.format(i) for i, gt in enumerate(ground_truths)]

    # Sort the DataFrame based on the subject_label
    #combined_df = combined_df.sort_values(by='subject_label').reset_index(drop=True)

    # Extract the sorted y-tick labels
    y_labels = combined_df['subject_label']

    # Drop the subject_label column for heatmap plotting
    combined_df = combined_df.drop(columns=['subject_label'])

    # Calculate accuracy of each column with respect to y_pred
    accuracies = combined_df.apply(lambda col: (col == combined_df['y_pred']).mean())


    # Sort columns based on their accuracy
    sorted_columns = accuracies.sort_values(ascending=True).index
    
    for col in sorted_columns:
        print(f'{col}: {accuracies[col]}')
        
        

    
    # Reorder the DataFrame based on sorted columns
    combined_df = combined_df[sorted_columns]
    combined_df= combined_df.iloc()[:,17:]
    combined_df['prob_label'] = probs[:, 1]*2-1
    #sort rows based on  probs
    combined_df= combined_df.iloc()[30:-100,:]
    #combined_df = combined_df.sort_values(by='prob_label').reset_index(drop=True)
 
    
    
    #change -1 to 0 and 0 to -1
    combined_df = combined_df.replace(-1,2)
    combined_df = combined_df.replace(0,-1)
    combined_df = combined_df.replace(2,0)
    
    # Create a figure with a specified aspect ratio to ensure square cells
    plt.figure(figsize=(22, 18))
    ax = sns.heatmap(combined_df, cmap='coolwarm', cbar=False, yticklabels=y_labels[30:-100], square=True, linewidths=0.5, linecolor='black')
    plt.title('Heatmap of Labeling Functions, Probabilistic Label, and y_pred')

    # Draw the grid
    ax.set_xticks([x + 0.5 for x in ax.get_xticks()], minor=True)
    ax.set_yticks([y + 0.5 for y in ax.get_yticks()], minor=True)
    ax.grid(which='minor', color='gray', linestyle='-', linewidth=1)
    
    # Create custom legend
    cmap = plt.get_cmap('coolwarm')
    legend_labels = {-1: 'Non PD', 0: 'Abstain', 1: 'PD'}
    colors = [cmap(0), cmap(0.5), cmap(1.0)]
    patches = [mpatches.Patch(color=colors[i], label=legend_labels[key]) for i, key in enumerate(legend_labels)]
    plt.legend(handles=patches, title='', bbox_to_anchor=(1.05, 1), loc='upper left')


    plt.show()
    return combined_df, label_model



ground_truths = pivot_data['id'].apply(get_ground_truth_label)
_, L_train, _, y_test = train_test_split(pivot_data.drop(columns=['id']).to_numpy(), ground_truths, test_size=0.9, random_state=66)
ground_truths = y_test

names = pivot_data.columns[1:]

#filter =[1,2,3,16,14]
values=['_CO_','_PD_','_MD_','_IN_','_SB_','_SC_','_DN_','_SP_','power','num_peaks','peak_amplitude']
filter=[index for index, element in enumerate(names) if any(substring in element for substring in values)]
#filter=[]
#filter L_train based on filter
L_train = L_train[:,[i for i in range(len(names)) if i not in filter]]
names = [names[i] for i in range(len(names)) if i not in filter]
output_file = fr'\\files.ubc.ca\team\PPRC\Camera\Booth_Results\finger_tapping_ws\snorkel_output\all_snorkel.csv'
#sort L_train based on the ground truth
combined_df,label_model = run_weak_supervision_new(L_train[:,:], ground_truths, output_file,names)

import seaborn as sns
import matplotlib.pyplot as plt
plt.figure(figsize=(18, 16))
# Create a DataFrame with y_pred, L_train, and ground truth
df_corr = pd.DataFrame(combined_df.iloc()[:,15:-3])
df_corr['y'] = ground_truths

# Compute the correlation matrix
corr_matrix = df_corr.corr()

# Plot the correlation matrix as a heatmap
sns.heatmap(corr_matrix, annot=True, cmap='coolwarm')

# Set the title of the plot
plt.title('Correlation Matrix')

# Show the plot
plt.show()


import pandas as pd
import networkx as nx
import numpy as np
import matplotlib.pyplot as plt

# Create a graph
G = nx.Graph()

# Add nodes for each column in the correlation matrix
print (corr_matrix.columns)
corr_matrix.columns = [
       'Consistency', 'Frequency',
       'Mean Distance', 'Max Distance',
       'Amplitude Variation', 'Amplitude Decrement',
       'Noisy Classifier', 'Noisy SVM Classifier', 'Noisy RF Classifier',
       'Y']
for column in corr_matrix.columns:
    G.add_node(column)


# Add edges based on the correlation matrix
threshold = 0.2  # Higher threshold for sparser graph
for i in range(len(corr_matrix.columns)):
    for j in range(i + 1, len(corr_matrix.columns)):
        if abs(corr_matrix.iloc[i, j]) > threshold:
            G.add_edge(corr_matrix.columns[i], corr_matrix.columns[j], weight=corr_matrix.iloc[i, j])

# Draw the graph
pos = nx.spring_layout(G, seed=57)  # For consistent layout
plt.figure(figsize=(14, 10))

# Draw nodes with size proportional to their degree and color based on degree
degrees = dict(G.degree)
node_color = [degrees[node] for node in G.nodes()]
node_size = [degrees[node] * 300 for node in G.nodes()]

# Set the last node ('y') to red
colors = [plt.cm.viridis(degrees[node]) if node != 'Y' else 'red' for node in G.nodes()]

nx.draw_networkx_nodes(G, pos, node_size=node_size, node_color=colors, cmap=plt.cm.viridis, alpha=0.7)

# Draw edges with width proportional to the edge weights
edges = G.edges(data=True)
weights = [d['weight'] for (u, v, d) in edges]
nx.draw_networkx_edges(G, pos, edgelist=edges, width=[abs(w) * 5 for w in weights], edge_color='gray', alpha=0.5)

# Draw labels with font size proportional to node degree
nx.draw_networkx_labels(G, pos, font_size=12, font_weight='bold')

# Create a custom colorbar
sm = plt.cm.ScalarMappable(cmap=plt.cm.viridis, norm=plt.Normalize(vmin=min(node_color), vmax=max(node_color)))
sm.set_array([])
#plt.colorbar(sm, label='Node Degree')

plt.title('Graphical Model of Labeling Functions and True Label Based on Correlation')
plt.show()

# Get the adjacency matrix
adj_matrix = nx.adjacency_matrix(G)
adj_dense = adj_matrix.todense()

# Convert to a DataFrame for better visualization
adj_df = pd.DataFrame(adj_dense, index=G.nodes(), columns=G.nodes())

print(adj_df)




# Get the adjacency matrix
adj_matrix = nx.adjacency_matrix(G)
adj_dense = adj_matrix.todense()

# Convert to a DataFrame for better visualization
adj_df = pd.DataFrame(adj_dense, index=G.nodes(), columns=G.nodes())

print(adj_df)
adj_df.to_csv(r'\\files.ubc.ca\team\PPRC\Camera\Booth_Results\finger_tapping_ws\adjacency_matrix.csv', index=False)


import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.animation import FuncAnimation
from scipy.signal import find_peaks

plt.switch_backend('Agg')

def count_taps_and_amplitudes(distances, hand_height, min_prominence=30):
    distances = np.array(distances, dtype=float)
    hand_height = np.array(hand_height, dtype=float)
    from scipy.signal import argrelextrema
    taps_indices = argrelextrema(distances, np.less)[0]
    min_prominence = 0.05
    peaks, _ = find_peaks(-distances, prominence=min_prominence, distance=5, height=-0.2)
    taps_indices = np.intersect1d(taps_indices, peaks)
    amplitudes = []
    for index in taps_indices:
        start = max(0, index - 20)
        end = min(len(distances), index + 20)
        local_max = np.max(distances[start:end])
        amplitude = local_max - distances[index]
        normalized_amplitude = amplitude / hand_height[index] if hand_height[index] != 0 else amplitude
        amplitudes.append(normalized_amplitude)
    tap_count = len(taps_indices)
    return tap_count, amplitudes, taps_indices

def animate_distance_over_time(file_name, distances, taps_indices, output_dir, fps=60):
    fig, ax = plt.subplots(figsize=(20, 6))
    ax.plot(distances, label='Distance')
    ax.scatter(taps_indices, [distances[i] for i in taps_indices], color='red', label='Taps', marker='x')
    moving_dot, = ax.plot([], [], 'ro', markersize=30)  # Increase the markersize here
    
    ax.set_xlabel('Frame')
    ax.set_ylabel('Distance')
    ax.set_title(f'Distance Between Index and Tip Finger Over Time')
    ax.legend()
    ax.grid(True)

    def init():
        moving_dot.set_data([], [])
        return moving_dot,

    def animate(i):
        moving_dot.set_data([i], [distances[i]])
        return moving_dot,

    ani = FuncAnimation(fig, animate, init_func=init, frames=len(distances), interval=1000/fps, blit=True)

    output_path = os.path.join(output_dir, f'{file_name}_distance_animation.mp4')
    ani.save(output_path, writer='ffmpeg', fps=fps)

    plt.close(fig)

def extract_features_from_file(file_path, output_dir, fps=30):
    df = pd.read_csv(file_path)
    distances = df['Finger Distance'].values
    hand_height = df['Hand BBox Height'].values

    distances = distances / np.max(distances)
    tap_count, amplitudes, taps_indices = count_taps_and_amplitudes(distances, hand_height)

    if len(taps_indices) == 0:
        raise ValueError("taps_indices is empty")

    animate_distance_over_time(os.path.basename(file_path), distances, taps_indices, output_dir, fps)

def process_dataset(dataset_folder, output_dir, fps=60):
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    for root, _, files in os.walk(dataset_folder):
        for file in files:
            if file.endswith('.csv'):
                try:
                    print(f'Processing {file}')
                    file_path = os.path.join(root, file)
                    extract_features_from_file(file_path, output_dir, fps)
                except Exception as e:
                    print(f'Error processing {file}: {str(e)}')

if __name__ == "__main__":
    dataset_folder = r'\\files.ubc.ca\team\PPRC\Camera\Booth_Results\finger_tapping_ws\special_cases'  # Replace with your input folder path
    output_dir = r'.'  # Replace with your output folder path
    fps = 60
    process_dataset(dataset_folder, output_dir, fps)


import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from sklearn.metrics import confusion_matrix
fl = r'\\files.ubc.ca\team\PPRC\Camera\Booth_Processed\docs\finger_left_right.csv'
df = pd.read_csv(fl)

# Confusion Matrix
cm = confusion_matrix(df['left'], df['right'])
cm_df = pd.DataFrame(cm, index=[f'Left_{i}' for i in range(cm.shape[0])], columns=[f'Right_{i}' for i in range(cm.shape[1])])

# Plotly Confusion Matrix Heatmap
fig_cm = px.imshow(cm_df, text_auto=True, color_continuous_scale='Blues', title='Confusion Matrix for Expert Labels')
fig_cm.update_yaxes(autorange="reversed")
fig_cm.show()

# Confusion Matrix Percentage
cm_percent = cm / cm.sum(axis=1)[:, None]
cm_percent_df = pd.DataFrame(cm_percent, index=[f'Left_{i}' for i in range(cm.shape[0])], columns=[f'Right_{i}' for i in range(cm.shape[1])])

# Plotly Confusion Matrix Percentage Heatmap
#two digit
cm_percent_df = cm_percent_df.round(2)
fig_cm_percent = px.imshow(cm_percent_df, text_auto=True, color_continuous_scale='Blues', title='Confusion Matrix Percentage for Expert Labels')
fig_cm_percent.update_yaxes(autorange="reversed")
fig_cm_percent.show()

# Bar Plot showing portion of data where left and right are the same
df['same'] = df['left'] == df['right']
same_counts = df.groupby('left')['same'].mean().reset_index()
same_counts.columns = ['class', 'portion_same']

print(same_counts)
fig_bar = px.bar(same_counts, x='class', y='portion_same', title='Portion of Data where Left and Right are the Same', labels={'portion_same': 'Portion Same'})
fig_bar.update_traces(texttemplate='%{y:.2%}', textposition='outside')
fig_bar.show()

# Distribution Plot for combined left and right data
df_melted = df.melt(value_vars=['left', 'right'], var_name='hand', value_name='score')

fig_dist = px.histogram(df_melted, x='score', color='hand', barmode='overlay', title='Distribution of Scores for Left and Right Hands', labels={'score': 'Score'})
fig_dist.update_traces(texttemplate='%{y}', textposition='outside', hoverinfo='skip')  # Adding numbers above the bars
fig_dist.show()


from scipy.stats import ttest_rel
# Perform paired t-test
#df where left or right are 0   or 1
df_s = df[(df['left'] == 0) | (df['left'] == 1)] 
t_stat, p_value = ttest_rel(df_s['left'], df_s['right'])

print(f'T-statistic: {t_stat}')
print(f'P-value: {p_value}')


from scipy.stats import chi2_contingency
# Create contingency table
contingency_table = pd.crosstab(df_s['left'], df_s['right'])

# Perform Chi-square test
chi2, p, dof, ex = chi2_contingency(contingency_table)

print(f'Chi-square statistic: {chi2}')
print(f'P-value: {p}')



# Calculate disease severity as the maximum of left or right scores
df['severity'] = df[['left', 'right']].mean(axis=1)

# Calculate asymmetry index
df['asymmetry'] = np.abs(df['left'] - df['right'])

# Create contingency table for asymmetry index and disease severity
contingency_table = pd.crosstab(df['asymmetry'], df['severity'])

# Perform Chi-square test
chi2, p, dof, ex = chi2_contingency(contingency_table)

print(f'Chi-square statistic: {chi2}')
print(f'P-value: {p}')

import numpy as np
from scipy.stats import chi2_contingency
df_them = pd.read_csv(r'D:\Datasets\Parkinson\finger_tapping\merge.csv')
# Calculate disease severity as the maximum of left or right scores
df_them = df_them[df_them['diagnosed']=='yes']
df_them['severity'] = df_them[['Rating Left', 'Rating Right']].mean(axis=1)

# Calculate asymmetry index
df_them['asymmetry'] = np.abs(df_them['Rating Left'] - df_them['Rating Right'])

# Create contingency table for asymmetry index and disease severity
contingency_table = pd.crosstab(df_them['asymmetry'], df_them['severity'])

# Perform Chi-square test
chi2, p, dof, ex = chi2_contingency(contingency_table)

print(f'Chi-square statistic: {chi2}')
print(f'P-value: {p}')


#snorkel weak supervision
import pandas as pd
from snorkel.labeling.model.label_model import LabelModel 


# Constants
ABSTAIN = -1  # Use this if some of your LFs abstain

# Step 1: Create L matrix from the 5 LF columns
# Assume df_union columns are exactly the weak labels
L_train = df_union.fillna(ABSTAIN).iloc[:, :5].astype(float).values


# Step 2: Fit the Label Model
label_model = LabelModel(cardinality=5, verbose=True)  # change to 3 if multiclass
label_model.fit(L_train=L_train, n_epochs=500, log_freq=100, seed=123)

# Step 3: Predict labels (hard and probabilistic)
df_union["snorkel_label"] = label_model.predict(L=L_train)
df_union["snorkel_prob_0"] = label_model.predict_proba(L=L_train)[:, 0]  # probability of class 0
df_union["snorkel_prob_1"] = label_model.predict_proba(L=L_train)[:, 1]  # probability of class 1
df_union["snorkel_prob_2"] = label_model.predict_proba(L=L_train)[:, 2]  # probability of class 2
df_union["snorkel_prob_3"] = label_model.predict_proba(L=L_train)[:, 3]  # probability of class 3
df_union["snorkel_prob_4"] = label_model.predict_proba(L=L_train)[:, 4]  # probability of class 4

df_union.to_csv(r'\\files.ubc.ca\team\PPRC\CAMERA\Booth_Processed\finger_tapping\docs\weak_supervision.csv', index=True)

import pandas as pd

# Load the two Excel files
file1 = r'\\files.ubc.ca\team\PPRC\Camera\Video Assessment_Atefeh\Video_quality_feed\merged.csv'
file2 = r'\\files.ubc.ca\team\PPRC\Camera\Booth_Results\finger_tapping_ws\Experiment2\csvs\number_of_taps.csv'

df1 = pd.read_csv(file1)
df2 = pd.read_csv(file2)
df2['ID'] = df2['ID'].astype(int)

# Merge the dataframes based on 'ID' and 'hand' columns
merged_df = pd.merge(df2, df1[['ID', 'hand', 'Expert_Count', 'UPDRS']], on=['ID', 'hand'], how='left')

# Update 'Expert_count' and 'UPDRS' columns in df2 with values from df1
df2['Expert_Count'] = merged_df['Expert_Count']
df2['UPDRS'] = merged_df['UPDRS']

# Save the updated df2 to a new Excel file
df2.to_csv(r'\\files.ubc.ca\team\PPRC\Camera\Booth_Results\finger_tapping_ws\Experiment2\csvs\updated_file2.csv')

print("Merging completed and saved to 'updated_file2.xlsx'")

import pandas as pd
import numpy as np

file1 = r'\\files.ubc.ca\team\PPRC\Camera\Booth_Results\finger_tapping_ws\Experiment2\csvs\number_of_taps.csv'
df = pd.read_csv(file1)

# calculate the mean square error and accuracy of each column with respect to the expert count
mse = []
accuracy = []
df = df.dropna(subset=['Expert_Count'])

for col in ['taps_finger_distance','taps_normalized','taps_angular']:
    df['diff'] = np.abs(df['Expert_Count'] - df[col])
    df['max'] = df[['Expert_Count', col]].max(axis=1)
    mse.append(np.mean(df['diff']/df['max'])**2)
    accuracy.append(np.mean(df['diff']<3))
    print (col, mse[-1], accuracy[-1])
    
df['diff'] = np.abs(df['Expert_Count'] - df['taps_finger_distance'])
df_number_of_taps = df

#Target is find the optimum way to calculate number of finger tappings
from scipy.signal import argrelextrema
import pandas as pd
import numpy as np
import os
from scipy.signal import find_peaks
from scipy.signal import savgol_filter

right_files = r'\\files.ubc.ca\team\PPRC\Camera\Booth_Processed\finger_tapping\right\distances'
left_files = r'\\files.ubc.ca\team\PPRC\Camera\Booth_Processed\finger_tapping\left\distances'
files =  df['file_name']

def count_taps_and_amplitudes(distances, min_prominence=0.35, distance = 10, height_multiplyer = 4):
    distances = np.array(distances, dtype=float)
    
    taps_indices = argrelextrema(distances, np.less)[0]
    peaks, _ = find_peaks(-distances, prominence=min_prominence, distance=distance, height=- np.max([height_multiplyer * np.min(distances),1.5]))
    taps_indices = np.intersect1d(taps_indices, peaks)
    tap_count = len(taps_indices)
    amplitudes = []
    for index in taps_indices:
        start = max(0, index - 20)
        end = min(len(distances), index + 20)
        local_max = np.max(distances[start:end])
        amplitude = local_max - distances[index]
        amplitudes.append(amplitude)
    tap_count = len(taps_indices)
    return tap_count, amplitudes, taps_indices

def smooth_signal( signal, window_length=20, polyorder=2):
    return signal
    if len(signal) < window_length:
        return signal
    return savgol_filter(signal, window_length=window_length, polyorder=polyorder)

# Define the parameter grid
distance_types = ['Finger Distance', 'Finger Normalized Distance', 'Angular Distance']
distance_types = [ 'Finger Normalized Distance']
min_prominences = [0.05, 0.1, 0.15,0.25,0.35,0.75]
min_prominences = [0.35]
height_multipliers = [0.2,0.25, 0.3]
height_multipliers = [2,3,4,5]
distances = [6,7,8]

# Initialize variables to store the best parameters and their corresponding metrics
best_params = None
best_mse = float('inf')
best_accuracy = 0

# Iterate over all combinations of parameters
for distance_type in distance_types:
    for min_prominence in min_prominences:
        for height_multiplier in height_multipliers:
            for distance in distances:
                for file in files:
                    distances_df = pd.DataFrame()
                    if file in os.listdir(right_files):
                        distances_df = pd.read_csv(os.path.join(right_files, file))
                    elif file in os.listdir(left_files):
                        distances_df = pd.read_csv(os.path.join(left_files, file))
                    else: 
                        continue
                    
                    smoothed_distances = smooth_signal(distances_df[distance_type])
                    tap_count, amplitudes, taps_indices = count_taps_and_amplitudes(
                        distances_df[distance_type], 
                        min_prominence=min_prominence, 
                        distance=distance, 
                        height_multiplyer=height_multiplier
                    )
                    
                    df.loc[df['file_name'] == file, 'taps'] = tap_count
                
                df['diff'] = np.abs(df['Expert_Count'] - df['taps'])
                df['max'] = df[['Expert_Count', 'taps']].max(axis=1)
                mse = (np.mean(df['diff']/df['max'])**2)
                accuracy = np.mean(df['diff'] < 3)
                true_counted = np.sum(df['diff'])/np.sum(df['Expert_Count'])
                
                print(f"Params: {distance_type}, {min_prominence}, {height_multiplier}, {distance}")
                print(f"MSE: {mse}, Accuracy: {accuracy}, True Counted: {true_counted}")
                
                # Update the best parameters if current metrics are better
                if mse < best_mse or (mse == best_mse and accuracy > best_accuracy):
                    best_params = (distance_type, min_prominence, height_multiplier, distance)
                    best_mse = mse
                    best_accuracy = accuracy

# Output the best parameters and their corresponding metrics
print(f"Best Params: {best_params}")
print(f"Best MSE: {best_mse}, Best Accuracy: {best_accuracy}")

    

    


#PCA Analysis
import pandas as pd
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt
import seaborn as sns

# Load the data
# df = pd.read_csv('your_dataset.csv')

# Assuming df is already loaded and preprocessed
# Remove non-numeric columns and handle missing values if necessary
X = df.drop(columns=['file_name'])
X.fillna(0, inplace=True)

# Standardize the data
X_standardized = StandardScaler().fit_transform(X)

# Run PCA
pca = PCA(n_components=3)
principal_components = pca.fit_transform(X_standardized)

# Create a DataFrame with the principal components
pca_df = pd.DataFrame(data=principal_components, columns=['Principal Component 1', 'Principal Component 2','principal_components 3'])

# Plot the explained variance
plt.figure(figsize=(10, 6))
plt.plot(range(1, len(pca.explained_variance_ratio_) + 1), pca.explained_variance_ratio_, marker='o', linestyle='--')
plt.title('Explained Variance by Principal Components')
plt.xlabel('Number of Principal Components')
plt.ylabel('Explained Variance Ratio')
plt.show()

# Plot the principal components
plt.figure(figsize=(10, 6))
plt.scatter(pca_df['Principal Component 1'], pca_df['Principal Component 2'], alpha=0.7)
plt.title('Principal Components of the Dataset')
plt.xlabel('Principal Component 1')
plt.ylabel('Principal Component 2')
plt.grid(True)
plt.show()

# Show the loadings
loadings = pd.DataFrame(pca.components_.T, columns=['Principal Component 1', 'Principal Component 2'], index=X.columns)
print("Loadings of the Principal Components:")
print(loadings)


import numpy as np
import pandas as pd
from scipy.signal import find_peaks, argrelextrema, butter, filtfilt
import matplotlib.pyplot as plt

def count_taps(distances, min_prominence=0.15, distance=10, height_multiplier=0.3):
    distances = np.array(distances, dtype=float)
    taps_indices = argrelextrema(distances, np.less)[0]
    peaks, _ = find_peaks(-distances, prominence=min_prominence, distance=distance, height=-height_multiplier * max(distances))
    taps_indices = np.intersect1d(taps_indices, peaks)
    tap_count = len(taps_indices)
    return tap_count, taps_indices

def calculate_amplitudes(distances, taps_indices):
    amplitudes = []
    max_amplitude_positions = []
    for i, index in enumerate(taps_indices):
        if i == len(taps_indices) - 1:
            break
        start = max(0, index)
        end = min(len(distances), taps_indices[i + 1])
        local_max = np.max(distances[start:end])
        amplitude = local_max
        amplitudes.append(amplitude)
        max_amplitude_positions.append(np.argmax(distances[start:end]) + start)
    return max_amplitude_positions, amplitudes

def butter_lowpass_filter(data, cutoff, fs, order=5):
    nyquist = 0.5 * fs
    normal_cutoff = cutoff / nyquist
    b, a = butter(order, normal_cutoff, btype='low', analog=False)
    y = filtfilt(b, a, data)
    return y

def split_segments(distances, taps_indices, max_gap=100):
    
    max_gap = np.mean(np.diff(taps_indices)) * 3
    segments = []
    start_idx = 0
    for i in range(1, len(taps_indices)):
        if taps_indices[i] - taps_indices[i - 1] > max_gap:
            segments.append((start_idx, taps_indices[i - 1]))
            start_idx = taps_indices[i]
    segments.append((start_idx, len(distances) - 1))
    return segments

def select_best_segment(distances, segments):
    best_segment = None
    max_taps = 0
    min_taps =8
    for start, end in segments:
        segment_distances = distances[start:end + 1]
        tap_count, _ = count_taps(segment_distances)
        
        if tap_count > max_taps and tap_count > min_taps:
            max_taps = tap_count
            best_segment = (start, end)
    if best_segment is None:
        best_segment = (segments[0][0], segments[-1][1])
    return best_segment

# Load your data
#data = pd.read_csv(r'\\files.ubc.ca\team\PPRC\Camera\Booth_Processed\finger_tapping\left\distances\31457_20240626_left_finger_tapping_distances.csv')
#data = pd.read_csv(r'\\files.ubc.ca\team\PPRC\Camera\Booth_Processed\finger_tapping\left\distances\39200_20240404_left_finger_tapping_distances.csv')
#data = pd.read_csv(r'\\files.ubc.ca\team\PPRC\Camera\Booth_Processed\finger_tapping\left\distances\38519_20230605_left_finger_tapping_distances.csv')
data = pd.read_csv(r'\\files.ubc.ca\team\PPRC\Camera\Booth_Processed\finger_tapping\left\distances\36297_20230411_left_finger_tapping_distances.csv')
distances = data['Finger Normalized Distance']

# Define filter parameters
cutoff = 1.0  # Desired cutoff frequency of the filter, in Hz
fs = 10.0     # Sample rate, in Hz (adjust based on your data's frame rate)
order = 5     # Order of the filter

# Apply low-pass filter to the data
cleaned_distances = butter_lowpass_filter(distances, cutoff, fs, order)

# Count taps and calculate amplitudes on the cleaned data
tap_count, taps_indices = count_taps(cleaned_distances)
max_amplitude_positions, amplitudes = calculate_amplitudes(cleaned_distances, taps_indices)

# Split the data into segments based on the gap between taps
segments = split_segments(cleaned_distances, taps_indices)

# Select the best segment with the highest number of taps
best_segment = select_best_segment(cleaned_distances, segments)

# Extract the best segment for plotting and saving
best_segment_distances = cleaned_distances[best_segment[0]:best_segment[1] + 1]

# Recalculate taps and amplitudes for the best segment
tap_count, taps_indices = count_taps(best_segment_distances)
max_amplitude_positions, amplitudes = calculate_amplitudes(best_segment_distances, taps_indices)

# Plot the original and cleaned data with the best segment highlighted
plt.figure(figsize=(10, 6))
plt.plot(distances, label='Original Data')
plt.plot(np.arange(best_segment[0], best_segment[1] + 1), best_segment_distances, label='Best Segment', color='orange')
plt.scatter(np.arange(best_segment[0], best_segment[1] + 1)[taps_indices], best_segment_distances[taps_indices], color='red', marker='o', label='Taps')
plt.scatter(np.arange(best_segment[0], best_segment[1] + 1)[max_amplitude_positions], amplitudes, color='green', marker='x', label='Peaks')
plt.xlabel('Frame')
plt.ylabel('Distance')
plt.legend()
plt.title('Original Data with Best Segment Highlighted')
plt.show()

# Save the cleaned data for the best segment to a new CSV file
cleaned_data_df = pd.DataFrame({'Distance': best_segment_distances})
cleaned_data_df.to_csv('cleaned_best_segment_data.csv', index=False)


import pandas as pd
import os

original_folder = r'\\files.ubc.ca\team\PPRC\Camera\Booth_Results\finger_tapping_ws\Experiment2\csvs'
files = ['CAMERA Study Booth - Tracking Log_KW-No Names.csv', 
         'CAMERA Study Booth - Tracking Log_MG.csv', 
         'CAMERA Study Booth - Tracking Log_SA.csv',
         'CAMERA Study Booth - Tracking Log_WM.csv']
suffixes = ['_KW', '_MG', '_SA', '_WM']

# Initialize an empty DataFrame to store combined results
combined_df = None

# Loop through the files and suffixes
for file, suffix in zip(files, suffixes):
    # Read the CSV file with proper encoding
    df = pd.read_csv(os.path.join(original_folder, file), encoding='ISO-8859-1')
    
    # Set the second row as header
    df.columns = df.iloc[0]
    df = df.iloc[1:]

    # Ensure ID and Date columns are present
    if 'ID' not in df.columns or 'Date' not in df.columns:
        raise ValueError(f"ID and Date columns must be present in the file: {file}")
    
    # Extract the relevant columns (Right_Collection_UPDRS and Left_Collection_UPDRS)
    df = df[['ID', 'Date', 'Right_Collection_UPDRS', 'Left_Collection_UPDRS']]
    
    # Rename the columns to include the suffix
    df = df.rename(columns={
        'Right_Collection_UPDRS': 'Right_Collection_UPDRS' + suffix,
        'Left_Collection_UPDRS': 'Left_Collection_UPDRS' + suffix
    })
    
    # Merge the DataFrame with the combined DataFrame based on ID and Date
    if combined_df is None:
        combined_df = df
    else:
        combined_df = pd.merge(combined_df, df, on=['ID', 'Date'], how='outer')

# Save the combined DataFrame to a CSV file
combined_df.to_csv(os.path.join(original_folder, 'combined.csv'), index=False)
combined_df.set_index(['ID', 'Date'], inplace=True)

print("Combination complete. The result is saved as 'combined.csv'.")

#drop where all values are missing
combined_df = combined_df.dropna(how='all')

import pandas as pd
import numpy as np
import seaborn as sns
import matplotlib.pyplot as plt
def prepare_updrs_df(combined_df, raters, hands):
    """
    Prepare the DataFrame with each row representing either left or right hand UPDRS for each rater.
    
    Parameters:
    - combined_df: The original DataFrame containing UPDRS data.
    - raters: List of rater identifiers.
    - hands: List of hand identifiers ('Left', 'Right').
    
    Returns:
    - updrs_df: A new DataFrame with rows representing each rater's left or right hand UPDRS.
    """
    rows = []
    for rater in raters:
        for hand in hands:
            hand_label = f'{hand}_Collection_UPDRS_{rater}'
            temp_df = combined_df[[hand_label]].copy()
            temp_df.columns = ['UPDRS']
            temp_df['Index'] = combined_df.index.astype(str) + f'_{hand}'
            temp_df.set_index('Index', inplace=True)
            rows.append(temp_df)
    
    updrs_df = pd.concat(rows)
    return updrs_df

def plot_updrs_confusion_matrices(updrs_cols, df, title):
    """
    Plot confusion matrices for given UPDRS columns.

    Parameters:
    - updrs_cols: List of column names to be analyzed.
    - df: DataFrame containing the UPDRS data.
    - title: Title for the plot.

    Returns:
    - None
    """
    # Fill NaN values with a specific value (e.g., -1) to represent NaN in the confusion matrix
    updrs_df = df[updrs_cols].fillna(-1)

    # Convert values to integers, turning non-integer values to NaN
    updrs_df = updrs_df.apply(pd.to_numeric, errors='coerce').astype('Int64')

    # Define the categories
    categories = [0, 1, 2, 3, 4]
    num_raters = len(updrs_cols)

    # Set up the matplotlib figure with a grid
    fig, axes = plt.subplots(num_raters, num_raters, figsize=(16, 16))

    # Iterate over all pairs of raters to compute and plot the confusion matrix
    for i in range(num_raters):
        for j in range(num_raters):
            # Initialize the confusion matrix
            confusion_matrix = np.zeros((len(categories), len(categories)))
            
            # Calculate the confusion matrix for each pair of raters
            for k in range(len(categories)):
                for l in range(len(categories)):
                    confusion_matrix[k, l] = np.sum(
                        (updrs_df.iloc[:, i] == categories[k]) & (updrs_df.iloc[:, j] == categories[l])
                    )
            
            # Calculate the percentage matrix
            percentage_matrix = (confusion_matrix / np.sum(confusion_matrix)) * 100
            
            # Annotate both number and percentage
            annotations = np.array([
                [f"{int(confusion_matrix[k, l])}\n({percentage_matrix[k, l]:.1f}%)" for l in range(len(categories))]
                for k in range(len(categories))
            ])
            
            # Plot the heatmap using Seaborn
            sns.heatmap(confusion_matrix, annot=annotations, fmt="", cmap="Greens", 
                        cbar=(i == 0 and j == num_raters - 1), xticklabels=categories, 
                        yticklabels=categories, ax=axes[i, j], square=True)
            
            # Set the titles for the heatmaps
            if i == 0:
                axes[i, j].set_title(updrs_cols[j], fontsize=10)
            if j == 0:
                axes[i, j].set_ylabel(updrs_cols[i], fontsize=10)

    # Adjust the layout
    plt.tight_layout()
    plt.suptitle(title, y=1.02, fontsize=16)
    plt.show()

# Assuming combined_df is the DataFrame containing all raters' data

# Call the function for Left_Collection_UPDRS
left_updrs_cols = [col for col in combined_df.columns if 'Left_Collection_UPDRS' in col]
plot_updrs_confusion_matrices(left_updrs_cols, combined_df, "4x4 Confusion Matrices for Left_Collection_UPDRS Agreement Between Raters")

# Call the function for Right_Collection_UPDRS
right_updrs_cols = [col for col in combined_df.columns if 'Right_Collection_UPDRS' in col]
plot_updrs_confusion_matrices(right_updrs_cols, combined_df, "4x4 Confusion Matrices for Right_Collection_UPDRS Agreement Between Raters")

# Combine Left and Right UPDRS for each rater
raters = ['KW', 'MG', 'SA', 'WM']  # Example rater identifiers
combined_updrs = pd.DataFrame()
left_df = combined_df[[col for col in combined_df.columns if 'Left_Collection_UPDRS' in col]]
right_df = combined_df[[col for col in combined_df.columns if 'Right_Collection_UPDRS' in col]]
left_df.columns = raters
right_df.columns = raters

# Combine left_df and right_df
combined_updrs = pd.concat([left_df, right_df], axis=0)

# Plot the confusion matrices for the combined data
plot_updrs_confusion_matrices(raters,combined_updrs, "4x4 Confusion Matrices for Combined Left and Right Collection UPDRS Agreement Between Raters")


#generate the correlation matrix between the raters from the combined_updrs drop -1

combined_updrs = combined_updrs.replace(-1, np.nan)
combined_updrs = combined_updrs.apply(pd.to_numeric, errors='coerce').astype('Int64')
correlation_matrix = combined_updrs.corr()
plt.figure(figsize=(10, 8))
sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', fmt=".2f")
plt.title('Correlation Matrix of UPDRS Scores Between Raters')
plt.show()

combined_df = pd.read_csv(r'\\files.ubc.ca\team\PPRC\Camera\Booth_Results\finger_tapping_ws\Experiment2\csvs\combined.csv')
#change date format to YYYYMMDD
combined_df['Date'] = pd.to_datetime(combined_df['Date']).dt.strftime('%Y%m%d')
combined_df.dropna(subset=['ID', 'Date'], inplace=True)

#create a new column for ID
#add 0 to ID become 6 digit
combined_df['ID'] = combined_df['ID'].astype(str).str.zfill(5)
combined_df['Date'] = combined_df['Date'].astype(str)

# Combine Left and Right UPDRS for each rater
raters = ['KW', 'MG', 'SA', 'WM']  # Example rater identifiers
combined_updrs = pd.DataFrame()
left_df = combined_df[[col for col in combined_df.columns if 'Left_Collection_UPDRS' in col]]
right_df = combined_df[[col for col in combined_df.columns if 'Right_Collection_UPDRS' in col]]
left_df.columns = raters
right_df.columns = raters

# Create new columns for ID in left_df and right_df
left_df['ID'] = combined_df['ID'] + '_' + combined_df['Date'] + '_left'
right_df['ID'] = combined_df['ID'] + '_' + combined_df['Date'] + '_right'


# Combine left_df and right_df
combined_updrs = pd.concat([left_df, right_df], axis=0)

combined_updrs.to_csv(r'\\files.ubc.ca\team\PPRC\Camera\Booth_Results\finger_tapping_ws\Experiment2\csvs\combined_updrs.csv', index=False)