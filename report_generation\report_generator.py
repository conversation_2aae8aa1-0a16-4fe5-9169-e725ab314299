import os
import sys
# Adjust the working directory to the project root
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.join(current_dir, '..')
sys.path.insert(0, project_root)
os.chdir(project_root)
from pathlib import Path
from config import ADMIN, BASE_RAW_DIRECTORY, BASE_RESULTS_DIRECTORY, TASKS, BASE_PROCESSED_DIRECTORY
from report_generation.base_report_generation.dataset_loader import DatasetLoader
from report_generation.finger_tappping_report.finger_tapping_processor import FingerTappingProcessor
from report_generation.facial_expression_report.faical_expression_processor import FacialExpressionProcessor  
from report_generation.finger_tappping_report.finger_tapping_report_generator import FingerTappingReportGenerator
from report_generation.finger_tappping_report.finger_tapping_updrs_model import FingerTappingModel
from report_generation.finger_tappping_report.finger_tapping_report_builder import FingerTappingReportBuilder
import traceback
#TODO Turn this to Class
def process_user_data(user_id, report_sections, base_path, output_dir):
    """
    Main function to process user data and generate YAML outputs.
    """
    data_loader = DatasetLoader(base_path)
    
    try:
        # Get the latest available date for the user
        user_dates = data_loader.get_dates(user_id)
        if not user_dates:
            raise ValueError(f"No available dates found for user {user_id}.")
        
        date = user_dates[-1]  # Assume we're using the most recent date
        user_data = data_loader.get_tasks(user_id, date)
        yml_files = {}

        # Process each report section
        for section in report_sections:
            if section in user_data:
                file = process_section(data_loader, user_id, date, section, output_dir)
                yml_files[section] = f'{file}'
            else:
                print(f"Section {section} not found for {user_id} on {date}.")
        
        return yml_files, date

    except Exception as e:
        print(f"Error processing data for user {user_id}: {e}")
        traceback.print_exc() 
        return {}, None

def finger_tapping_report_generate(section_output_dir, user_id, date,subtasks,data_loader:DatasetLoader):
    """
    Generate the Finger Tapping report.
    """
    section = 'finger_tapping'
    processor = FingerTappingProcessor(BASE_PROCESSED_DIRECTORY)
    updrs_model = FingerTappingModel(classifier_pipeline_filename='./models/finger_tapping_classifier_pipeline_v1.pkl', regressor_model_filename='./models/finger_tapping_regressor_v1.pkl')
    for subtask in subtasks:
        # Check if the subtask exists and process the corresponding video
        if data_loader.get_subtask(user_id, date, section, subtask):
            video_path = data_loader.get_subtask(user_id, date, section, subtask) 
            print(f"Processing {subtask} for {section}")
            
            # Process the video and save outputs
            hand = 'left' if 'left' in subtask else 'right'
            processor.process(user_id, date, hand, video_path, output_dir)
        else:
            print(f"Subtask {subtask} not found for {section} in {user_id} on {date}")
            
    left_video_path = data_loader.get_subtask(user_id, date, section, 'left_finger_tapping') 
    right_video_path = data_loader.get_subtask(user_id, date, section, 'right_finger_tapping')           
    finger_tapping_report_generator = FingerTappingReportGenerator(name = f'{user_id}_{date}', model=updrs_model, output_path=section_output_dir, left_video_path=left_video_path, right_video_path=right_video_path)
    yml_file = finger_tapping_report_generator.generate_report()
    
    generator = FingerTappingReportBuilder(
        yaml_file=yml_file,
        template_file=r".\report_generation\design\finger_tapping_template.html", #TODO better path
        output_file=rf".\report_generation\outputs\finger_tapping_report_{user_id}.html",
        user_role=ADMIN
        )
    generator.generate_report()
    
    
    print(f"Finished processing section: {section} for user {user_id} on {date}")
    return yml_file

def facial_expression_report_generate(section_output_dir, user_id, date,subtasks,data_loader):
    #processor = FacialExpressionProcessor(video_path, subtask)
    pass


def process_section(data_loader, user_id, date, section, output_dir):
    """
    Process a specific section (e.g., facial-expression, hand-movement) for the given user and date.
    """
    print(f"Processing section: {section} for user {user_id} on {date}")
    subtasks = TASKS[section].keys()

    section_output_dir = output_dir / section
    section_output_dir.mkdir(parents=True, exist_ok=True)
    
    # Choose the appropriate processor
    if section == 'finger_tapping':
        finger_tapping_report_generate(section_output_dir, user_id, date,subtasks,data_loader)
    elif section == 'facial_expression':
        facial_expression_report_generate(section_output_dir, user_id, date,subtasks)
    else:
        print(f"Unsupported section: {section}")
        return



if __name__ == "__main__":
    user_id = '25934'
    
    report_sections = ['finger_tapping']  # Sections to include in the report
    base_path = Path(BASE_RAW_DIRECTORY)
    
    for user_id in DatasetLoader(base_path).list_users(): 
        user_id = '07610'
        output_dir = Path(BASE_RESULTS_DIRECTORY) / 'reports' / 'users' / user_id
        # if output_dir.exists():   
        #     print(f"Output directory exists for {user_id}. Skipping.")
        #     continue
        process_user_data(user_id, report_sections, base_path, output_dir)
        break
        

