#compare two pdf
import pandas as pd
import numpy as np

path_truth = "G:\My Drive\Datasets\XAI\merged_output.csv"
path_gpt   = "G:\My Drive\Datasets\XAI\outputs\json-gpt5-outputs-s.csv"

dfa = pd.read_csv(path_truth)
dfb = pd.read_csv(path_gpt)

# --- keys ---
dfa["join_key"] = dfa["ID"].astype(str).str.strip().str.lower()
dfb["join_key"] = dfb["file_name"].astype(str).str.strip().str.lower()

# --- map GPT JSON to comparable fields ---
def to_binary(x):
    if pd.isna(x): return np.nan
    x = str(x).strip().lower()
    if x in {"absent","no","none","false","0"}: return 0
    if x in {"present","yes","true","1"}: return 1
    if x in {"very mild","mild","moderate","severe","very_severe"}: return 0.5
    return np.nan

gpt = dfb[["join_key",
           "json_Amplitude Decrement","json_Low Speed","json_Low Amplitude",
           "json_Halts/Hesitations","json_Bradykinesia","json_MDS-UPDRS Score"]].copy()
gpt.columns = ["join_key","json_amp_decrement","json_low_speed","json_low_amplitude",
               "json_halts_hesitations","json_bradykinesia","json_updrs"]
for c in ["json_amp_decrement","json_low_speed","json_low_amplitude","json_halts_hesitations","json_bradykinesia"]:
    gpt[c] = gpt[c].apply(to_binary)
gpt["json_updrs"] = pd.to_numeric(gpt["json_updrs"], errors="coerce").round().clip(0,4).astype("Int64")

truth_cols = {
    "AMPLITUDE_True_Label":"AMPLITUDE",
    "DECRAMPLITUDE_True_Label":"DECRAMPLITUDE",
    "HALT_HESITATION_True_Label":"HALT_HESITATION",
    "SLOWNESS_True_Label":"BRADYKINESIA",
    "severity_truth_label":"UPDRS",
    'AMPLITUDE_Predicted_Label':'AMPLITUDE_Predicted_Label',
    "DECRAMPLITUDE_Predicted_Label":"DECRAMPLITUDE_Predicted_Label",
    "HALT_HESITATION_Predicted_Label":"HALT_HESITATION_Predicted_Label",
    "SLOWNESS_Predicted_Label":"SLOWNESS_Predicted_Label",
    "BRADYKINESIA_Predicted_Label":"BRADYKINESIA_Predicted_Label",
    "severity_predicted_label":"severity_predicted_label",
    
}
# truth_cols = {
#     "AMPLITUDE_Predicted_Label":"AMPLITUDE",
#     "DECRAMPLITUDE_Predicted_Label":"DECRAMPLITUDE",
#     "SLOWNESS_Predicted_Label":"SLOWNESS",
#     "HALT_HESITATION_Predicted_Label":"HALT_HESITATION",
#     "BRADYKINESIA_Predicted_Label":"BRADYKINESIA",
#     "severity_predicted_label":"UPDRS",
# }

truth = dfa[["join_key"] + list(truth_cols.keys())].rename(columns=truth_cols)

m = truth.merge(gpt, on="join_key", how="inner")
m = m.dropna(subset=["json_updrs"])

def metrics(y_true, y_pred):
    mask = ~(y_true.isna() | y_pred.isna())
    if mask.sum() == 0: return np.nan, np.nan, 0
    yt, yp = y_true[mask].astype(float), y_pred[mask].astype(float)
    mae = np.abs(yt - yp).mean()
    acc = (yt == yp).mean()
    return float(mae), float(acc), int(mask.sum())

pairs = [
    ("AMPLITUDE","AMPLITUDE_Predicted_Label"),
    ("DECRAMPLITUDE","DECRAMPLITUDE_Predicted_Label"),
    ("HALT_HESITATION","HALT_HESITATION_Predicted_Label"),
    ("BRADYKINESIA","SLOWNESS_Predicted_Label"),
    ("UPDRS","severity_predicted_label"),
]
pairs = [
    ("AMPLITUDE","json_low_amplitude"),
    ("DECRAMPLITUDE","json_amp_decrement"),
    ("HALT_HESITATION","json_halts_hesitations"),
    ("BRADYKINESIA","json_bradykinesia"),
    ("UPDRS","json_updrs"),
]
rows = []
for tcol, gcol in pairs:
    mae, acc, n = metrics(m[tcol], m[gcol])
    rows.append({"target": tcol, "n": n, "mae": mae, "accuracy": acc})
out = pd.DataFrame(rows)

#out.to_csv("/mnt/data/gpt_vs_truth_metrics.csv", index=False)
# m[["join_key","UPDRS","json_updrs","AMPLITUDE","json_low_amplitude",
#    "DECRAMPLITUDE","json_amp_decrement","SLOWNESS","json_low_speed",
#    "HALT_HESITATION","json_halts_hesitations","BRADYKINESIA","json_bradykinesia"]].to_csv(
#    "/mnt/data/gpt_vs_truth_perrow.csv", index=False)

#compute average accuracy and mae

print(out)
