{"cells": [{"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"marker": {"color": [5, 5, 5, 5, 5, 1, 4, 4, 4, 4, 1, 4, 4, 4, 4, 3, 3, 3, 2, 2, 1], "colorscale": [[0, "#440154"], [0.1111111111111111, "#482878"], [0.2222222222222222, "#3e4989"], [0.3333333333333333, "#31688e"], [0.4444444444444444, "#26828e"], [0.5555555555555556, "#1f9e89"], [0.6666666666666666, "#35b779"], [0.7777777777777778, "#6ece58"], [0.8888888888888888, "#b5de2b"], [1, "#fde725"]], "line": {"color": "DarkSlateGrey", "width": 1}, "opacity": 0.6, "showscale": true, "size": 12}, "mode": "markers", "type": "scatter", "x": [0.1764052345967664, 0.040015720836722335, 0.09787379841057392, 0.2240893199201458, 0.18675579901499675, 2.0144043571160877, 2.076103772514699, 2.012167501649283, 2.0443863232745425, 2.0333674327374265, 1.744701018416592, 3.0864436198859506, 2.925783497959356, 3.2269754623987605, 2.8545634325401235, 2.0154947425696914, 2.0378162519602174, 1.9112214252369888, 3.123029068072772, 3.120237984878441, 3.895144703493291], "y": [0, 0.07600707340204715, 0, 0, 0.032847880155069786, 1.116341880557038, 2.1195263258526085, 1.983587338898736, 2.025045416132072, 1.931672340855862, 3.0522894876352287, 3.003660681384116, 2.985025291997933, 3.1226223371486768, 3.117548701592023, 3.841536282542086, 3.972167028053908, 4.0125079175283185, 3.969013854607364, 3.9758157799539733, 3.886398565025682]}, {"line": {"color": "gray", "width": 1}, "mode": "lines", "showlegend": false, "type": "scatter", "x": [0, 4.5], "y": [0, 4.5]}], "layout": {"height": 600, "plot_bgcolor": "white", "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "title": {"font": {"color": "black", "family": "<PERSON><PERSON>", "size": 24, "weight": "bold"}, "text": "Confusion Matrix Scatter Plot"}, "width": 600, "xaxis": {"constrain": "domain", "gridcolor": "gray", "tickmode": "array", "tickvals": [0, 1, 2, 3, 4], "title": {"font": {"color": "black", "family": "<PERSON><PERSON>", "size": 18, "weight": "bold"}, "text": "Predicted labels"}}, "yaxis": {"constrain": "domain", "gridcolor": "gray", "tickmode": "array", "tickvals": [0, 1, 2, 3, 4], "title": {"font": {"color": "black", "family": "<PERSON><PERSON>", "size": 18, "weight": "bold"}, "text": "True labels"}}}}}, "metadata": {}, "output_type": "display_data"}], "source": ["import plotly.graph_objects as go\n", "import numpy as np\n", "\n", "# Confusion matrix\n", "confusion_matrix = np.array([[5, 0, 0, 0, 0],\n", "                             [0, 0, 1, 0, 0],\n", "                             [0, 0, 4, 0, 0],\n", "                             [0, 0, 1, 4, 0],\n", "                             [0, 0, 3, 2, 1]])\n", "\n", "# Extracting the coordinates and values for scatter plot\n", "x, y = np.meshgrid(np.arange(confusion_matrix.shape[1]), np.arange(confusion_matrix.shape[0]))\n", "x = x.flatten()\n", "y = y.flatten()\n", "values = confusion_matrix.flatten()\n", "\n", "# Generating the scatter plot data\n", "x_noisy = []\n", "y_noisy = []\n", "scatter_values = []\n", "\n", "np.random.seed(0)  # For reproducibility\n", "\n", "for i in range(len(values)):\n", "    if values[i] != 0:\n", "        for j in range(1):\n", "            x_noisy.extend(x[i] + 0.1 * np.random.randn(values[i]))\n", "            y_noisy.extend(y[i] + 0.08 * np.random.randn(values[i]))\n", "            scatter_values.extend([values[i]] * values[i])\n", "\n", "#set negative values to 0\n", "scatter_values = [0 if x < 0 else x for x in scatter_values]\n", "x_noisy = [0 if x < 0 else x for x in x_noisy]\n", "y_noisy = [0 if x < 0 else x for x in y_noisy]\n", "# Creating the scatter plot\n", "fig = go.Figure()\n", "\n", "fig.add_trace(go.<PERSON>(\n", "    x=x_noisy, y=y_noisy, mode='markers',\n", "    marker=dict(\n", "        size=12,\n", "        color=scatter_values,\n", "        colorscale='Viridis',\n", "        showscale=True,\n", "        opacity=0.6,\n", "        line=dict(width=1, color='DarkSlateGrey')\n", "    )\n", "))\n", "\n", "# Adding the x=y line\n", "fig.add_trace(go.<PERSON>(\n", "    x=[0, confusion_matrix.shape[1]-0.5], \n", "    y=[0, confusion_matrix.shape[0]-0.5],\n", "    mode='lines',\n", "    line=dict(color='gray', width=1),\n", "    showlegend=False\n", "))\n", "\n", "# Formatting the plot\n", "\n", "fig.update_layout(\n", "    title='Confusion Matrix Scatter Plot',\n", "    xaxis=dict(title='Predicted labels', tickmode='array', tickvals=np.arange(confusion_matrix.shape[1]), gridcolor='gray'),\n", "    yaxis=dict(title='True labels', tickmode='array', tickvals=np.arange(confusion_matrix.shape[0]), gridcolor='gray', ),\n", "    plot_bgcolor='white',\n", "    width=600,\n", "    height=600,\n", "    xaxis_constrain='domain',\n", "    yaxis_constrain='domain'\n", ")\n", "fig.update_layout(\n", "    title_font=dict(size=24, family='Arial', color='black', weight='bold'),\n", "    xaxis_title_font=dict(size=18, family='Arial', color='black', weight='bold'),\n", "    yaxis_title_font=dict(size=18, family='Arial', color='black', weight='bold')\n", ")\n", "\n", "fig.show()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from matplotlib.animation import FuncAnimation\n", "import matplotlib.pyplot as plt\n", "import os\n", "def animate_distance_over_time(file_name, distances, taps_indices, max_amplitude_positions, output_dir, fps=60):\n", "    # Create figure and plot the initial frame\n", "    fig, ax = plt.subplots(figsize=(20, 6))\n", "    line, = ax.plot(distances, label='Distance')\n", "    taps_scatter = ax.scatter(taps_indices, [distances[i] for i in taps_indices], color='red', label='Taps', marker='x')\n", "    #max_amp_scatter = ax.scatter(max_amplitude_positions, [distances[i] for i in max_amplitude_positions], color='green', label='Max Amplitudes', marker='o')\n", "    moving_dot, = ax.plot([], [], 'ro')\n", "    \n", "    ax.set_xlabel('Frame')\n", "    ax.set_ylabel('Distance')\n", "    ax.set_title(f'Distance Between Index and Tip Finger Over Time')\n", "    ax.legend()\n", "    ax.grid(True)\n", "\n", "    # Initialization function: plot the background of each frame\n", "    def init():\n", "        moving_dot.set_data([], [])\n", "        return moving_dot,\n", "\n", "    # Animation function: this is called sequentially\n", "    def animate(i):\n", "        moving_dot.set_data([i], [distances[i]])  # Note the brackets around i and distances[i]\n", "        return moving_dot,\n", "\n", "    # Create animation\n", "    ani = FuncAnimation(fig, animate, init_func=init, frames=len(distances), interval=1000/fps, blit=True)\n", "\n", "    # Save the animation\n", "    output_path = os.path.join(output_dir, f'{file_name}_distance_animation.mp4')\n", "    ani.save(output_path, writer='ffmpeg', fps=fps)\n", "\n", "    plt.close(fig)"]}], "metadata": {"kernelspec": {"display_name": "myenv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 2}