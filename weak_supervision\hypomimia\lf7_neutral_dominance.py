import os
import pandas as pd
from sklearn.ensemble import RandomForestClassifier
import sys
sys.path.insert(0, 'd:\\Atefeh\\Codes\\Python\\ParkinsonAssessment')
from weak_supervision.LFBase import LFBaseClass
from datasets.load_data import load_labels_with_ID, load_emotion_task_ts_data, get_train_classifiers_set, merge_df_with_labels
from helpers.classification_analysis import perform_analysis_cv, save_trained_model


class LF7Class(LFBaseClass):
    def __init__(self, model_path = '.\\models\\best_lf7_v3.json'):
        self.neutral_threshold=0.5
        self.type='text'
        
        super().__init__(model_path=model_path)
        
    # Define the function to count neutral frames
    def count_neutral_frames(self,df):
        neutral_frames = (df['neutral'] > self.neutral_threshold).sum()
        return neutral_frames

    def prepare_dataset(self):
        # Create an empty list to store the results
        neutral_results = []

        # Iterate over tasks and emotions
        for task in self.tasks:
            for emotion in self.emotions:
                root_folder = f'{self.origin_folder}\\facial_expression\\{emotion}\\{task}\\emotions'
                
                # Iterate over all CSV files in the folder
                for file_name in os.listdir(root_folder):
                    try:
                        if file_name.endswith('.csv'):
                            # Read the data from the CSV file
                            file_path = os.path.join(root_folder, file_name)
                            data = pd.read_csv(file_path)
                            
                            # Extract ID from file_name (assuming ID is part of the file_name)
                            # Modify this extraction logic based on your actual file naming convention
                            id_ = file_name.split('_')[0]
                            
                            # Calculate the number of neutral frames in each video
                            neutral_frames = self.count_neutral_frames(data)
                            
                            # Calculate the total number of frames in that video
                            num_frames = len(data)
                            
                            # Calculate the neutral expression rate (frames per second)
                            # Assuming video frame rate is 30 fps
                            neutral_rate = neutral_frames / (num_frames / self.frame_rate)
                            
                            # Store the results
                            neutral_results.append({
                                'ID': id_,
                                'emotion_task': f'{emotion}_{task}',
                                'neutral_rate': neutral_rate
                            })
                    except:
                        continue

        # Create a DataFrame from the results
        neutral_data_df = pd.DataFrame(neutral_results)
        # Drop duplicates to ensure unique entries for each ID and emotion_task combination
        neutral_data_df = neutral_data_df.drop_duplicates(subset=['ID', 'emotion_task'])
        # Pivot the DataFrame to get columns for each task_emotion combination
        neutral_data_pivot_df = neutral_data_df.pivot(index='ID', columns='emotion_task', values='neutral_rate').reset_index()
        #add neutral to column names
        neutral_data_pivot_df.columns = ['ID'] + [f'neutral_{col}' for col in neutral_data_pivot_df.columns if col != 'ID']
        neutral_data_pivot_df['ID'] = neutral_data_pivot_df['ID'].astype(int)
        neutral_data_pivot_df.set_index('ID', inplace=True)
        
        # Step 4: Merge the pivoted DataFrame with booth_updrs_df on the 'ID' column
        booth_updrs_df = load_labels_with_ID(os.path.join(self.origin_folder, 'docs'), 'booth_updrs.csv')
        merged_df = merge_df_with_labels(neutral_data_pivot_df, booth_updrs_df, self.predict)
        
        
        return merged_df

    def train_classifier(self):
        return super().train_classifier('neutral_.*')

        
        
# lf = LF7Class()
# classifier, selected_features =  lf.train_classifier()
# lf.save_model(classifier, selected_features)
# lf.evaluate_classifier(classifier, selected_features)

