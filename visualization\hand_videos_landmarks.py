import cv2
import mediapipe as mp
import os

# Initialize MediaPipe Hands model
mp_hands = mp.solutions.hands
mp_drawing = mp.solutions.drawing_utils

# Colors for different hands
hand_colors = [(0, 0, 255), (0, 255, 0)]

def display_hand_landmarks(video_path, output_path):
    hands = mp_hands.Hands(
        static_image_mode=False,
        max_num_hands=1,
        min_detection_confidence=0.9,
        min_tracking_confidence=0.5)

    cap = cv2.VideoCapture(video_path)

    # Get video properties
    frame_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    frame_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_path, fourcc, fps, (frame_width, frame_height))

    while cap.isOpened():
        ret, frame = cap.read()
        if not ret:
            break

        # Convert the frame to RGB
        frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        results = hands.process(frame_rgb)

        # Draw hand landmarks
        if results.multi_hand_landmarks:
            for idx, hand_landmarks in enumerate(results.multi_hand_landmarks):
                hand_color = hand_colors[idx % len(hand_colors)]
                mp_drawing.draw_landmarks(
                    frame, hand_landmarks, mp_hands.HAND_CONNECTIONS,
                    mp_drawing.DrawingSpec(color=hand_color, thickness=2, circle_radius=2),
                    mp_drawing.DrawingSpec(color=hand_color, thickness=2, circle_radius=2))
                if idx == 1:
                    print('2 hands detected')

        # Write the frame with landmarks to the output video
        out.write(frame)

        # Display the frame with landmarks
        cv2.imshow('Hand Landmarks', frame)

        # Break the loop on 'q' key press
        if cv2.waitKey(1) & 0xFF == ord('q'):
            break

    cap.release()
    out.release()
    cv2.destroyAllWindows()
    hands.close()

if __name__ == '__main__':
    # Directory containing videos
    video_path = r'\\files.ubc.ca\team\PPRC\MCKEoWNLAB\H20-00572_All-Dressed\Second_Visit\Video data (raw - DO NOT ALTER)\95002\Trial 1\Finger tapping\left_finger_tapping.mp4'
    output_path = r'd:\Codes\Python\ParkinsonAssessment\output\left_finger_tapping_with_landmarks.mp4'
    display_hand_landmarks(video_path, output_path)
