# Test Set Prompt Generation Script

This script (`generate_prompts_for_test_set.py`) generates prompts for test set users by filling a template with user data from the merged output CSV file.

## Overview

The script performs the following steps:
1. Reads user information from `merged_output.csv`
2. Identifies test set users from `test-set-balanced.csv`
3. Loads a prompt template file
4. Fills the template with user data for each test set user
5. Generates image file paths based on the file naming pattern
6. Creates a new CSV with file names, filled prompts, and image paths

## Usage

### Basic Usage with Default Paths

```bash
python generate_prompts_for_test_set.py
```

This uses the default paths:
- Merged output: `G:\My Drive\Datasets\XAI\merged_output.csv`
- Test set: `G:\My Drive\Datasets\XAI\test-set-balanced.csv`
- Template: `G:\My Drive\Datasets\XAI\report_prompt.txt`
- Output: `G:\My Drive\Datasets\XAI\test_set_prompts.csv`
- Image base path: `G:\My Drive\Datasets\XAI\images`

### Custom Paths

```bash
python generate_prompts_for_test_set.py \
    --merged-output "path/to/merged_output.csv" \
    --test-set "path/to/test-set-balanced.csv" \
    --template "path/to/prompt_template.txt" \
    --output "path/to/output_prompts.csv" \
    --image-base-path "path/to/images"
```

## Input Files

### 1. Merged Output CSV (`merged_output.csv`)
Contains user information and clinical data. Expected columns might include:
- `ID` or `file_name`: User identifier
- Clinical measurements (UPDRS scores, distances, etc.)
- Demographic information (age, gender, etc.)
- Any other data to be included in prompts

### 2. Test Set CSV (`test-set-balanced.csv`)
Contains the list of users in the test set. Should have a column with user IDs that match the merged output file.

### 3. Prompt Template (`report_prompt.txt`)
A text file with placeholders for user data. Use Python string formatting syntax:
```
Patient ID: {ID}
Age: {age}
UPDRS Score: {updrs_score}
...
```

## Output

### Generated CSV Structure
The output CSV contains three columns:
- `file_name`: The user identifier from the input data
- `prompt`: The filled template with user-specific data
- `image_file`: Path to the corresponding full_chart image

### Image Path Generation
Images are generated following the pattern:
```
{base_path}/{user_id}/finger_tapping/images/{user_id}_{date}_distance_full_chart_{hand}.png
```

If the file ID follows the pattern `user_id_date_hand`, it will be parsed automatically.

## Template Creation

### Template Syntax
Use Python string formatting with curly braces:
```
Patient Information:
- ID: {ID}
- Age: {age}
- Gender: {gender}

Clinical Data:
- UPDRS Score: {updrs_score}
- Mean Distance: {finger_distance_mean}
```

### Available Placeholders
The placeholders depend on the columns in your `merged_output.csv` file. Common ones might include:
- `ID`, `user_id`, `file_name`
- `age`, `gender`
- `updrs_score`
- `finger_distance_mean`, `finger_distance_std`
- `angular_distance_mean`, `angular_distance_std`
- `tap_frequency`, `movement_amplitude`

### Example Template
See `example_prompt_template.txt` for a sample template structure.

## Error Handling

The script includes error handling for:
- Missing files
- Column name mismatches
- Template formatting errors
- Individual row processing failures

If errors occur, the script will:
- Print informative error messages
- Show available column names
- Continue processing other rows when possible

## Troubleshooting

### No Test Set Records Found
If no test set records are found:
1. Check that the ID columns match between files
2. Verify the test set file format
3. Check for case sensitivity or extra whitespace

### Template Errors
If template filling fails:
1. Check that all placeholders in the template exist as columns
2. Verify the column names in the merged output file
3. Use the error messages to identify missing keys

### Column Name Issues
The script tries multiple common column names:
- `ID`, `id`, `file_name`, `filename`, `user_id`, `File`, `file`

If your files use different column names, you may need to rename them or modify the script.

## Integration with Existing Code

This script follows the same pattern as the existing `chatgpt-file.py` in the Foundation models directory and can be easily integrated into the existing workflow.

The output CSV can be used directly with OpenAI API calls or other LLM processing pipelines.
