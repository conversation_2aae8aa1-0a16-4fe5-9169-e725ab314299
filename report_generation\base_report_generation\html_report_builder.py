import yaml
from jinja2 import Template
import base64

class HTMLReportBuilder:
    def __init__(self, yaml_file, template_file, output_file):
        self.yaml_file = yaml_file
        self.template_file = template_file
        self.output_file = output_file

    def load_yaml(self):
        with open(self.yaml_file, 'r') as file:
            data = yaml.safe_load(file)
        return data

    def load_template(self):
        with open(self.template_file, 'r') as file:
            template_content = file.read()
        return template_content

    def image_to_base64(self, image_path):
        # Convert the image to base64
        with open(image_path, 'rb') as img_file:
            return base64.b64encode(img_file.read()).decode('utf-8')

    def generate_report(self, rendered_html):
        # Save the rendered HTML to the output file
        with open(self.output_file, 'w') as file:
            file.write(rendered_html)
        print(f"Report generated successfully: {self.output_file}")

