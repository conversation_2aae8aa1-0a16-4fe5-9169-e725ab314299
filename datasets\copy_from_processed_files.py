import os
import shutil

#foreach emotion and task find specific folder and copy all them to another direectory

source = "//files.ubc.ca/team/PPRC/Camera/Booth_Processed/"
destination = "Z:"

def task_copy(source, destination):
    look_fors = ['landmarks']

    emotions = ['disgust', 'angry', 'happy', 'sad']
    tasks = ['imitation','text']

    for emotion in emotions:
        for task in tasks:
            for look_for in look_fors:
                folder_path = os.path.join(source, emotion, task,look_for)
                if os.path.exists(folder_path):
                    destination_path = os.path.join(destination, emotion, task,look_for)
                    shutil.copytree(folder_path, destination_path)

def copy_directory(source, destination, ignore_types=('.png', '.mp4'), skip_existing=True, ignore_folders=None):
    if ignore_folders is None:
        ignore_folders = []

    if not os.path.exists(destination):
        os.makedirs(destination)

    for item in os.listdir(source):
        if item in ignore_folders:
            continue  # Skip the folders listed in ignore_folders

        src_path = os.path.join(source, item)
        dst_path = os.path.join(destination, item)

        if os.path.isdir(src_path):
            copy_directory(src_path, dst_path, ignore_types, skip_existing, ignore_folders)
        elif not src_path.endswith(ignore_types):
            if not skip_existing or not os.path.exists(dst_path):
                shutil.copy2(src_path, dst_path)
    

if __name__ == '__main__':
    folders_to_ignore = ['videos', 'frames']
    copy_directory(source, destination, ignore_folders=folders_to_ignore)