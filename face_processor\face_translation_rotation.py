import cv2
import mediapipe as mp
import numpy as np

# Initialize MediaPipe Face Mesh
mp_face_mesh = mp.solutions.face_mesh
face_mesh = mp_face_mesh.FaceMesh(static_image_mode=False, max_num_faces=1, refine_landmarks=True)

# Start video capture
cap = cv2.VideoCapture(0)

while cap.isOpened():
    ret, frame = cap.read()
    if not ret:
        break

    # Convert frame to RGB
    rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

    # Process frame with Face Mesh
    results = face_mesh.process(rgb_frame)

    # Get frame dimensions
    h, w, _ = frame.shape
    center_x, center_y = w // 2, h // 2  # Image center

    if results.multi_face_landmarks:
        for face_landmarks in results.multi_face_landmarks:
            # Extract key points
            nose = face_landmarks.landmark[1]
            left_eye = face_landmarks.landmark[33]
            right_eye = face_landmarks.landmark[263]
            chin = face_landmarks.landmark[199]
            forehead = face_landmarks.landmark[10]

            # Convert normalized coordinates to pixel values
            nose_pt = (int(nose.x * w), int(nose.y * h))
            left_eye_pt = (int(left_eye.x * w), int(left_eye.y * h))
            right_eye_pt = (int(right_eye.x * w), int(right_eye.y * h))
            chin_pt = (int(chin.x * w), int(chin.y * h))
            forehead_pt = (int(forehead.x * w), int(forehead.y * h))

            # Compute rotation angles
            dx = right_eye_pt[0] - left_eye_pt[0]
            dy = right_eye_pt[1] - left_eye_pt[1]
            roll = np.degrees(np.arctan2(dy, dx))  # Tilt angle (Roll)

            dx_nose = nose_pt[0] - (left_eye_pt[0] + right_eye_pt[0]) / 2
            yaw = np.degrees(np.arctan2(dx_nose, dx))  # Yaw angle (Left-Right)

            # Corrected Pitch calculation
            dy_nose = nose_pt[1] - forehead_pt[1]
            dy_chin = chin_pt[1] - forehead_pt[1]
            pitch = np.degrees(np.arctan2(dy_nose, dy_chin))  # Corrected Up-Down angle

            # Compute head position (X, Y, Z)
            head_x = nose_pt[0] - center_x  # Left (-) / Right (+)
            head_y = nose_pt[1] - center_y  # Up (-) / Down (+)
            head_z = np.linalg.norm(np.array(left_eye_pt) - np.array(right_eye_pt))  # Distance between eyes

            # Display angles & position
            cv2.putText(frame, f"Yaw: {yaw:.2f}", (30, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
            cv2.putText(frame, f"Pitch: {pitch:.2f}", (30, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 0), 2)
            cv2.putText(frame, f"Roll: {roll:.2f}", (30, 90), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 2)

            cv2.putText(frame, f"X: {head_x:.2f}", (30, 120), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)
            cv2.putText(frame, f"Y: {head_y:.2f}", (30, 150), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 165, 0), 2)
            cv2.putText(frame, f"Z: {head_z:.2f}", (30, 180), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 255), 2)

            # Draw key points
            cv2.circle(frame, nose_pt, 5, (0, 255, 0), -1)
            cv2.circle(frame, left_eye_pt, 5, (255, 0, 0), -1)
            cv2.circle(frame, right_eye_pt, 5, (255, 0, 0), -1)
            cv2.circle(frame, chin_pt, 5, (0, 0, 255), -1)
            cv2.circle(frame, forehead_pt, 5, (255, 255, 0), -1)

    cv2.imshow("Head Pose & Position Estimation", frame)

    if cv2.waitKey(1) & 0xFF == ord('q'):
        break

cap.release()
cv2.destroyAllWindows()
