#!/usr/bin/env python3
"""
Simple script to run ChatGPT over prompts from CSV file with images.
Based on the existing chatgpt-file.py pattern.
"""

import pandas as pd
import os
import json
import time
from openai import OpenAI
from chatgpt_config import *

# Initialize OpenAI client
client = OpenAI(api_key=API_KEY)

def create_file(file_path):
    """Upload file to OpenAI Files API."""
    try:
        with open(file_path, "rb") as file_content:
            result = client.files.create(
                file=file_content,
                purpose="vision",
            )
            return result.id
    except Exception as e:
        print(f"Error uploading file {file_path}: {e}")
        return None

def process_with_chatgpt(prompt, image_path):
    """Send prompt and image to ChatGPT."""
    try:
        # Upload image
        file_id = create_file(image_path)
        if not file_id:
            return None
        
        # Send request using the same pattern as chatgpt-file.py
        response = client.responses.create(
            model=MODEL,
            input=[{
                "role": "user",
                "content": [
                    {"type": "input_text", "text": prompt},
                    {"type": "input_image", "file_id": file_id},
                ],
            }],
        )
        
        return response.output_text
        
    except Exception as e:
        print(f"Error processing with ChatGPT: {e}")
        return None

def main():
    """Main processing function."""
    print(f"Loading CSV from: {CSV_PATH}")
    
    # Read CSV file
    try:
        df = pd.read_csv(CSV_PATH)
        print(f"Loaded {len(df)} records")
    except Exception as e:
        print(f"Error loading CSV: {e}")
        return
    
    # Validate required columns
    required_columns = [COLUMNS['file_name'], COLUMNS['prompt'], COLUMNS['image_file']]
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        print(f"Error: Missing required columns: {missing_columns}")
        print(f"Available columns: {list(df.columns)}")
        return

    # Limit records if specified
    if MAX_RECORDS:
        df = df.head(MAX_RECORDS)
        print(f"Processing first {len(df)} records (limited by MAX_RECORDS)")
    
    # Process each record
    results = []
    
    for index, row in df.iterrows():
        file_name = row[COLUMNS['file_name']]
        prompt = row[COLUMNS['prompt']]
        image_file = row[COLUMNS['image_file']]
        
        print(f"\nProcessing {index + 1}/{len(df)}: {file_name}")
        
        # Construct full image path
        if os.path.isabs(image_file):
            image_path = image_file
        else:
            image_path = os.path.join(IMAGE_BASE_PATH, image_file)
        
        # Check if image exists
        if not os.path.exists(image_path):
            print(f"  Warning: Image not found: {image_path}")
            results.append({
                "file_name": file_name,
                "prompt": prompt[:100] + "..." if len(prompt) > 100 else prompt,
                "image_path": image_path,
                "success": False,
                "error": "Image file not found",
                "output": None,
                "model": MODEL,
                "timestamp": pd.Timestamp.now().isoformat()
            })
            continue
        
        print(f"  Image: {image_path}")
        print(f"  Prompt preview: {prompt[:100]}...")
        
        # Process with ChatGPT
        output = process_with_chatgpt(prompt, image_path)
        
        # Collect results
        result = {
            "file_name": file_name,
            "prompt": prompt if SAVE_FULL_PROMPTS else (prompt[:100] + "..." if len(prompt) > 100 else prompt),
            "image_path": image_path,
            "success": output is not None,
            "output": output,
            "model": MODEL,
            "timestamp": pd.Timestamp.now().isoformat()
        }
        
        if output:
            result["error"] = None
            print(f"  ✓ Success")
            print(f"  Response preview: {output[:100]}...")
        else:
            result["error"] = "Failed to get response"
            print(f"  ✗ Failed")
        
        results.append(result)

        # Add delay to avoid rate limiting
        if DELAY_BETWEEN_REQUESTS > 0:
            time.sleep(DELAY_BETWEEN_REQUESTS)
    
    # Save results
    print(f"\nSaving results to: {OUTPUT_PATH}")
    try:
        with open(OUTPUT_PATH, "w", encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        # Also save as CSV for easy viewing
        csv_output = OUTPUT_PATH.replace('.json', '_summary.csv')
        results_df = pd.DataFrame([
            {
                'file_name': r['file_name'],
                'success': r['success'],
                'error': r.get('error', ''),
                'response_preview': r['output'][:100] + '...' if r['output'] else '',
                'timestamp': r['timestamp']
            }
            for r in results
        ])
        results_df.to_csv(csv_output, index=False)
        
        print(f"Results also saved to: {csv_output}")
        
    except Exception as e:
        print(f"Error saving results: {e}")
        return
    
    # Summary
    successful = sum(1 for r in results if r['success'])
    failed = len(results) - successful
    
    print(f"\nProcessing completed:")
    print(f"  Total records: {len(results)}")
    print(f"  Successful: {successful}")
    print(f"  Failed: {failed}")
    
    if failed > 0:
        print(f"\nFailed records:")
        for r in results:
            if not r['success']:
                print(f"  - {r['file_name']}: {r.get('error', 'Unknown error')}")

if __name__ == "__main__":
    main()
