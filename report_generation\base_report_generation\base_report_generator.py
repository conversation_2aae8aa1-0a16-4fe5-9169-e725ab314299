import os
from config import BASE_PROCESSED_DIRECTORY
import yaml
import numpy as np

class YAMLProcessor:
    def __init__(self, file_path):
        self.file_path = file_path

    def clean_data(self, data):
        # Recursively clean complex data types in nested dictionaries
        if isinstance(data, dict):
            return {k: self.clean_data(v) for k, v in data.items() if v is not None}
        elif isinstance(data, np.generic):  # For numpy scalar types
            return data.item()  # Convert numpy scalar to native Python type
        elif isinstance(data, (list, tuple)):
            return [self.clean_data(i) for i in data]
        else:
            return data

    def save_yaml(self, data):
        cleaned_data = self.clean_data(data)
        with open(self.file_path, 'w') as yaml_file:
            yaml.dump(cleaned_data, yaml_file, default_flow_style=False, allow_unicode=True)


            
class BaseReportGenerator:
    def __init__(self, output_path, output_name):
        self.output_path = output_path
        self.output_name = output_name
        self.features = {}
        self.charts = {}
        self.data_folder = BASE_PROCESSED_DIRECTORY
    
    def _default_path(self, side, data_type):
        """
        Generate a default file path for a given side (right/left) and data type (distances/features).
        """
        return self.data_folder / side / data_type/ f'{self.output_name}_{side}_finger_tapping_{data_type}.csv'
    
    def generate_report(self):
        # Generate the report
        self.feature_generator()
        self.image_generator()
        return self.write_to_yaml()
        
    def feature_generator(self):
        # Generate features for the report
        raise NotImplementedError

    def image_generator(self):
        # Generate charts for the report
        raise NotImplementedError

    
    def write_to_yaml(self,output_file=None):
        # use features dictionary and charts dictionary to write to a YAML file with output_name in output_path
        if not output_file:
            output_file = self.output_path / f'{self.output_name}.yml'  # Output file path
        yaml_processor = YAMLProcessor(output_file)
        yaml_processor.save_yaml({
            'images': self.images,
            'features': self.features
        })
        return output_file
        