# Configuration for base directories
BASE_RAW_DIRECTORY = r'\\files.ubc.ca\team\PPRC\Camera\CAMERA Booth Data\Booth'
BASE_PROCESSED_DIRECTORY = r'\\files.ubc.ca\team\PPRC\Camera\Booth_Processed'
BASE_RESULTS_DIRECTORY = r'\\files.ubc.ca\team\PPRC\Camera\Booth_Results'

# Define the task structure with subtasks and video paths
TASKS = {
    "facial_expression": {
        "neutral": "basic_facial/neutral_facial_expression.mp4",
        "imitation_angry": "imitation/facial_mimicry_angry.mp4",
        "imitation_sad": "imitation/facial_mimicry_sad.mp4",
        "imitation_happy": "imitation/facial_mimicry_happy.mp4",
        "imitation_disgust": "imitation/facial_mimicry_disgust.mp4",
        "text_angry" : "text/facial_expression_angry.mp4",
        "text_sad": "text/facial_expression_sad.mp4",
        "text_happy": "text/facial_expression_happy.mp4",
        "text_disgust": "text/facial_expression_disgust.mp4"
        
    },
    "hand_movement": {
        "both_still": "both_still.mp4",
        "left_open_close": "left_open_close.mp4",
        "right_open_close": "right_open_close.mp4",
        "left_up_down": "left_up_down.mp4",
        "right_up_down": "right_up_down.mp4"
    },
    "finger_tapping": {
        "left_finger_tapping": "left_finger_tapping.mp4",
        "right_finger_tapping": "right_finger_tapping.mp4"
    },
    "eye_movement": {   
        "OKN_task": "okn/okn_task.mp4",
        "calibration": "calibration/calibration.mp4"
    }
}

MAIN_TASKS = list(TASKS.keys())


colors = {
    "white": "#ffffff",
    "red_1": "#e15c5b",
    "red_2": "#e98685",
    "red_3": "#ef7259",
    "pink_1": "#f39683",
    "pink_2": "#f07258",
    "pink_3": "#f49d8a",
    "light_pink": "#fcdcd1",
    "orange_1": "#f58a65",
    "orange_2": "#ffebe4",
    "orange_3": "#f8a88c",
    "light_orange": "#fdede7",
    "secondary": "#f89970",
    "peach_2": "#f9a682",
    "peach_3": "#ffc7ae",
    "light_peach": "#fac19a",
    "orange_4": "#f8ac78",
    "light_orange_2": "#fce5d5",
    "light_orange_3": "#fefcf9",
    "green_blue_1": "#bad8d5",
    "teal_1": "#2d887f",
    "teal_2": "#569f98",
    "primary": "#18615a",
    "light_teal": "#abcfcc",
    "pale_green_blue_1": "#8ebfbb",
    "dark_green": "#0e3f3b",
    "light_blue_1": "#b9d7d5",
    "light_gray_blue": "#e4f0f0"
}

DOCTOR = "doctor"
ADMIN = "admin"
PATIENT = "patient"
