#!/usr/bin/env python3
"""
<PERSON>ript to copy images with 'full_chart' in their name from users/[user_id]/finger_tapping/images directories
to a specified destination directory.

This script searches for images containing 'full_chart' in their filename within the finger tapping
image directories and copies them to a centralized location.
"""

import os
import shutil
import glob
from pathlib import Path
import argparse
from typing import List, Optional

from config import BASE_PROCESSED_DIRECTORY


def find_full_chart_images(base_directory: str, pattern: str = "*full_chart*") -> List[str]:
    """
    Find all images with 'full_chart' in their name within finger_tapping/images directories.
    
    Args:
        base_directory: Base directory to search in (e.g., reports/users or Booth_Results/reports/users)
        pattern: File pattern to search for (default: "*full_chart*")
    
    Returns:
        List of full paths to matching image files
    """
    image_files = []
    base_path = Path(base_directory)
    
    if not base_path.exists():
        print(f"Warning: Base directory {base_directory} does not exist")
        return image_files
    
    # Search pattern: users/*/finger_tapping/images/*full_chart*
    search_pattern = base_path / "users" / "*" / "finger_tapping" / "images" / pattern
    
    # Find all matching files
    matching_files = glob.glob(str(search_pattern), recursive=True)
    
    # Filter for common image extensions
    image_extensions = {'.png', '.jpg', '.jpeg', '.gif', '.bmp', '.tiff', '.svg'}
    
    for file_path in matching_files:
        file_ext = Path(file_path).suffix.lower()
        if file_ext in image_extensions:
            image_files.append(file_path)
    
    return sorted(image_files)


def copy_images(source_files: List[str], destination_dir: str, preserve_structure: bool = False) -> None:
    """
    Copy image files to the destination directory.
    
    Args:
        source_files: List of source file paths
        destination_dir: Destination directory path
        preserve_structure: If True, preserve the user_id subdirectory structure
    """
    dest_path = Path(destination_dir)
    dest_path.mkdir(parents=True, exist_ok=True)
    
    copied_count = 0
    failed_count = 0
    
    for source_file in source_files:
        try:
            source_path = Path(source_file)
            
            if preserve_structure:
                # Extract user_id from path (assuming pattern: .../users/USER_ID/finger_tapping/images/...)
                path_parts = source_path.parts
                users_index = path_parts.index('users')
                user_id = path_parts[users_index + 1]
                
                # Create user subdirectory in destination
                user_dest_dir = dest_path
                user_dest_dir.mkdir(exist_ok=True)
                dest_file_path = user_dest_dir / source_path.name
            else:
                # Flatten structure - all files go directly to destination
                dest_file_path = dest_path / source_path.name
            
            # Handle filename conflicts by adding a suffix
            counter = 1
            original_dest = dest_file_path
            while dest_file_path.exists():
                stem = original_dest.stem
                suffix = original_dest.suffix
                dest_file_path = original_dest.parent / f"{stem}_{counter}{suffix}"
                counter += 1
            
            # Copy the file
            shutil.copy2(source_file, dest_file_path)
            print(f"Copied: {source_file} -> {dest_file_path}")
            copied_count += 1
            
        except Exception as e:
            print(f"Failed to copy {source_file}: {e}")
            failed_count += 1
    
    print(f"\nCopy operation completed:")
    print(f"  Successfully copied: {copied_count} files")
    print(f"  Failed: {failed_count} files")


def main():
    """Main function to handle command line arguments and execute the copy operation."""
    parser = argparse.ArgumentParser(
        description="Copy images with 'full_chart' in their name from finger_tapping directories"
    )
    
    parser.add_argument(
        "source_base_dir",
        help="Base directory containing users/[user_id]/finger_tapping/images structure"
    )
    
    parser.add_argument(
        "destination_dir",
        help="Destination directory to copy images to"
    )
    
    parser.add_argument(
        "--pattern",
        default="*full_chart*",
        help="File pattern to search for (default: *full_chart*)"
    )
    
    parser.add_argument(
        "--preserve-structure",
        action="store_true",
        help="Preserve user_id subdirectory structure in destination"
    )
    
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Show what would be copied without actually copying files"
    )
    
    args = parser.parse_args()
    
    # Find all matching images
    print(f"Searching for images matching '{args.pattern}' in {args.source_base_dir}")
    image_files = find_full_chart_images(args.source_base_dir, args.pattern)
    
    if not image_files:
        print("No matching images found.")
        return
    
    print(f"Found {len(image_files)} matching images:")
    for img_file in image_files:
        print(f"  {img_file}")
    
    if args.dry_run:
        print(f"\nDry run mode - would copy {len(image_files)} files to {args.destination_dir}")
        if args.preserve_structure:
            print("Structure would be preserved (user_id subdirectories)")
        else:
            print("Files would be flattened to destination directory")
        return
    
    # Copy the images
    print(f"\nCopying images to {args.destination_dir}")
    copy_images(image_files, args.destination_dir, args.preserve_structure)


def copy_full_chart_images_default():
    """
    Convenience function to copy full_chart images using default paths from the project configuration.
    Uses the BASE_RESULTS_DIRECTORY from config.py if available.
    """
    try:
        from config import BASE_RESULTS_DIRECTORY
        source_base = BASE_RESULTS_DIRECTORY + r"\reports"
        destination = BASE_PROCESSED_DIRECTORY+r".\finger_tapping\plots\full-chart"

        print(f"Using default source: {source_base}")
        print(f"Using default destination: {destination}")

        # Find and copy images
        image_files = find_full_chart_images(source_base)
        if image_files:
            copy_images(image_files, destination, preserve_structure=True)
        else:
            print("No full_chart images found in default location.")

    except ImportError:
        print("Could not import config.py. Please run with command line arguments.")
        print("Example: python copy_full_chart_images.py <source_base_dir> <destination_dir>")


if __name__ == "__main__":
    import sys

    # If no arguments provided, try to use default paths
    if len(sys.argv) == 1:
        copy_full_chart_images_default()
    else:
        main()
