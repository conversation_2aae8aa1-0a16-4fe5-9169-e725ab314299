{"cells": [{"cell_type": "code", "execution_count": 1, "id": "b11b2238", "metadata": {"execution": {"iopub.execute_input": "2024-07-17T20:58:19.635988Z", "iopub.status.busy": "2024-07-17T20:58:19.635988Z", "iopub.status.idle": "2024-07-17T21:02:24.950754Z", "shell.execute_reply": "2024-07-17T21:02:24.950754Z"}, "papermill": {"duration": 245.320919, "end_time": "2024-07-17T21:02:24.953142", "exception": false, "start_time": "2024-07-17T20:58:19.632223", "status": "completed"}, "tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Task</th>\n", "      <th>Data Count</th>\n", "      <th>Unique Users</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>facial_expression_text_disgust</td>\n", "      <td>210</td>\n", "      <td>204</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>facial_expression_text_angry</td>\n", "      <td>210</td>\n", "      <td>204</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>facial_expression_text_happy</td>\n", "      <td>210</td>\n", "      <td>204</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>facial_expression_text_sad</td>\n", "      <td>210</td>\n", "      <td>204</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>facial_expression_imitation_disgust</td>\n", "      <td>209</td>\n", "      <td>203</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>facial_expression_imitation_angry</td>\n", "      <td>209</td>\n", "      <td>203</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>facial_expression_imitation_happy</td>\n", "      <td>209</td>\n", "      <td>203</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>facial_expression_imitation_sad</td>\n", "      <td>210</td>\n", "      <td>204</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>finger_tapping_left_NA</td>\n", "      <td>211</td>\n", "      <td>205</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>finger_tapping_right_NA</td>\n", "      <td>213</td>\n", "      <td>207</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>hand_movement_both_still_NA</td>\n", "      <td>210</td>\n", "      <td>204</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>hand_movement_left_open_close_NA</td>\n", "      <td>212</td>\n", "      <td>205</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>hand_movement_left_up_down_NA</td>\n", "      <td>211</td>\n", "      <td>204</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>hand_movement_right_open_close_NA</td>\n", "      <td>211</td>\n", "      <td>205</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>hand_movement_right_up_down_NA</td>\n", "      <td>209</td>\n", "      <td>203</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                   Task  Data Count  Unique Users\n", "0        facial_expression_text_disgust         210           204\n", "1          facial_expression_text_angry         210           204\n", "2          facial_expression_text_happy         210           204\n", "3            facial_expression_text_sad         210           204\n", "4   facial_expression_imitation_disgust         209           203\n", "5     facial_expression_imitation_angry         209           203\n", "6     facial_expression_imitation_happy         209           203\n", "7       facial_expression_imitation_sad         210           204\n", "8                finger_tapping_left_NA         211           205\n", "9               finger_tapping_right_NA         213           207\n", "10          hand_movement_both_still_NA         210           204\n", "11     hand_movement_left_open_close_NA         212           205\n", "12        hand_movement_left_up_down_NA         211           204\n", "13    hand_movement_right_open_close_NA         211           205\n", "14       hand_movement_right_up_down_NA         209           203"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import os\n", "import pandas as pd\n", "BASE_RAW_DIRECTORY = r'\\\\files.ubc.ca\\team\\PPRC\\Camera\\CAMERA Booth Data\\Booth'\n", "\n", "def analyze_videos(src_directory, task, emotion, parent_task, data_summary, unique_users):\n", "    # Iterate over each user's folder in the source directory\n", "    for user_id in os.listdir(src_directory):\n", "        user_path = os.path.join(src_directory, user_id)\n", "        # Check if it's a directory (to handle user folders)\n", "        if os.path.isdir(user_path):\n", "            unique_users.add(user_id)\n", "            # Iterate over each timestamp folder in the user's folder\n", "            for timestamp in os.listdir(user_path):\n", "                timestamp_path = os.path.join(user_path, timestamp)\n", "                # Check if it's a directory (to handle timestamp folders)\n", "                if os.path.isdir(timestamp_path):\n", "                    face_path = os.path.join(timestamp_path, parent_task)\n", "                    if emotion:\n", "                        face_path = os.path.join(face_path, f'{task}')\n", "                    # Check if the target folder exists\n", "                    if os.path.isdir(face_path):\n", "                        # Count all videos in the target folder\n", "                        for video in os.listdir(face_path):\n", "                            video_path = os.path.join(face_path, video)\n", "                            # Check if it's a file (to handle only video files)\n", "                            if os.path.isfile(video_path) and ((emotion is None and task in video) or f'{emotion}' in video) and '.mp4' in video: \n", "                                # Update data summary\n", "                                task_key = f\"{parent_task}_{task if task else 'neutral'}_{emotion if emotion else 'NA'}\"\n", "                                data_summary[task_key]['data_count'] += 1\n", "                                data_summary[task_key]['users'].add(user_id)\n", "\n", "def initialize_data_summary(tasks, emotions, parent_task):\n", "    data_summary = {}\n", "    for task in tasks:\n", "        if emotions:\n", "            for emotion in emotions:\n", "                key = f\"{parent_task}_{task}_{emotion}\"\n", "                data_summary[key] = {'data_count': 0, 'users': set()}\n", "        else:\n", "            key = f\"{parent_task}_{task}_NA\"\n", "            data_summary[key] = {'data_count': 0, 'users': set()}\n", "    return data_summary\n", "\n", "if __name__ == '__main__':\n", "    # Connect to shared drive and analyze videos\n", "    base_directory = BASE_RAW_DIRECTORY\n", "    \n", "    parent_tasks = {\n", "        'facial_expression': {'tasks': ['text', 'imitation'], 'emotions': ['disgust', 'angry', 'happy', 'sad']},\n", "        'finger_tapping': {'tasks': ['left', 'right'], 'emotions': None},\n", "        'hand_movement': {'tasks': ['both_still', 'left_open_close', 'left_up_down', 'right_open_close', 'right_up_down'], 'emotions': None},\n", "    }\n", "\n", "    # Initialize data summary dictionary\n", "    data_summary = {}\n", "    unique_users = set()\n", "\n", "    for parent_task, details in parent_tasks.items():\n", "        data_summary.update(initialize_data_summary(details['tasks'], details['emotions'], parent_task))\n", "\n", "    for parent_task, details in parent_tasks.items():\n", "        tasks = details['tasks']\n", "        emotions = details['emotions']\n", "        for task in tasks:\n", "            if emotions:\n", "                for emotion in emotions:\n", "                    analyze_videos(base_directory, task, emotion, parent_task, data_summary, unique_users)\n", "            else:\n", "                analyze_videos(base_directory, task, None, parent_task, data_summary, unique_users)\n", "\n", "    # # Additional facial expression task\n", "    # parent_task = 'facial_expression'\n", "    # emotion = 'basic_facial'\n", "    # task = 'neutral'\n", "    # analyze_videos(base_directory, emotion, task, parent_task, data_summary, unique_users)\n", "\n", "    # Convert data summary to DataFrame\n", "    data_summary_df = pd.DataFrame([\n", "        {'Task': key, 'Data Count': value['data_count'], 'Unique Users': len(value['users'])}\n", "        for key, value in data_summary.items()\n", "    ])\n", "\n", "data_summary_df\n"]}, {"cell_type": "code", "execution_count": 2, "id": "6785fc37", "metadata": {"execution": {"iopub.execute_input": "2024-07-17T21:02:24.956954Z", "iopub.status.busy": "2024-07-17T21:02:24.956954Z", "iopub.status.idle": "2024-07-17T21:02:24.964864Z", "shell.execute_reply": "2024-07-17T21:02:24.964864Z"}, "papermill": {"duration": 0.013181, "end_time": "2024-07-17T21:02:24.966323", "exception": false, "start_time": "2024-07-17T21:02:24.953142", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Healthy Control : 50\n", "People with PD : 149\n", "Total Unique Users : 199\n"]}], "source": ["#Count number of healthy unique Ids that start with 07\n", "count = 0\n", "for user in unique_users:\n", "    if user.startswith('07'):\n", "        count += 1\n", "\n", "#remove not number ids\n", "unique_users = [user for user in unique_users if user.isnumeric()]\n", "        \n", "print(f'Healthy Control : {count}')\n", "print(f'People with PD : {len(unique_users) - count}')\n", "print(f'Total Unique Users : {len(unique_users)}')"]}, {"cell_type": "code", "execution_count": 3, "id": "64459857", "metadata": {"execution": {"iopub.execute_input": "2024-07-17T21:02:24.971326Z", "iopub.status.busy": "2024-07-17T21:02:24.971326Z", "iopub.status.idle": "2024-07-17T21:02:24.974738Z", "shell.execute_reply": "2024-07-17T21:02:24.974738Z"}, "papermill": {"duration": 0.009948, "end_time": "2024-07-17T21:02:24.976271", "exception": false, "start_time": "2024-07-17T21:02:24.966323", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['25533', '23284', '31100', '32200', '18317', '26692', '17980', '25779', '14555', '36564', '38046', '33164', '30148', '31437', '28813', '18372', '07755', '32594', '38256', '21401', '32519', '36660', '24192', '39712', '07629', '38255', '38519', '07652', '33151', '17434', '28637', '07536', '07985', '22224', '07677', '17599', '15813', '07152', '07681', '33430', '07900', '34914', '38215', '07580', '26407', '07634', '07640', '29570', '27123', '30279', '07488', '07888', '35154', '32282', '07284', '19124', '07794', '32424', '07225', '25957', '38050', '24860', '38141', '35816', '07819', '25260', '29377', '16219', '17000', '07609', '27425', '28350', '22227', '15377', '26546', '28225', '29157', '07610', '38902', '23191', '30104', '07727', '33023', '31240', '26145', '07771', '21696', '07795', '07735', '40046', '28411', '24798', '07597', '32853', '36220', '27423', '31318', '25793', '28408', '07155', '07884', '07501', '07987', '39528', '07288', '07776', '38073', '31769', '33527', '33166', '07452', '07855', '30961', '24475', '31231', '25934', '07814', '31457', '32160', '38100', '07587', '36407', '07852', '36880', '24601', '28641', '07415', '07571', '29880', '36297', '32541', '24757', '25439', '39200', '31961', '34492', '38210', '24622', '40613', '07566', '39686', '07822', '36532', '35246', '20715', '17200', '28726', '19015', '30593', '31182', '36581', '28715', '22178', '34965', '34509', '07928', '36436', '30982', '24352', '10039', '25352', '29079', '35623', '30414', '07137', '30893', '30009', '31092', '34417', '07182', '16827', '28731', '20959', '28615', '24889', '07518', '28321', '19091', '39274', '39685', '07334', '07578', '17202', '31848', '16883', '35747', '28630', '23160', '33927', '31319', '07628', '07417', '07762', '18198', '24318', '33749', '34142', '28550', '07709']\n"]}], "source": ["print(unique_users)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}, "papermill": {"default_parameters": {}, "duration": 248.424437, "end_time": "2024-07-17T21:02:25.332659", "environment_variables": {}, "exception": null, "input_path": "datasets/booth_data_demography.ipynb", "output_path": "datasets/booth_data_demography_output.ipynb", "parameters": {}, "start_time": "2024-07-17T20:58:16.908222", "version": "2.6.0"}}, "nbformat": 4, "nbformat_minor": 5}