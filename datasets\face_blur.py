import cv2

# Load the pre-trained face detection model (Haar Cascade classifier)
face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')

# Function to blur the detected face
def blur_face(frame, x, y, w, h):
    # Extract the region of interest (ROI) for the face
    face_roi = frame[y:y+h, x:x+w]
    
    # Apply a Gaussian blur to the face ROI
    blurred_face = cv2.GaussianBlur(face_roi, (99, 99), 30)
    
    # Replace the original face ROI with the blurred one
    frame[y:y+h, x:x+w] = blurred_face

    return frame

# Open the video capture
video_path = r'\\files.ubc.ca\team\PPRC\Camera\Booth_Processed\finger_tapping\left\videos\30961_20230913_left_finger_tapping.mp4'  # Path to your video file
cap = cv2.VideoCapture(video_path)

# Get video writer setup to save the output video
fourcc = cv2.VideoWriter_fourcc(*'mp4v')  # Video codec
output_path = 'output_blurred_video.mp4'  # Path to save output video
fps = int(cap.get(cv2.CAP_PROP_FPS))
width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))

# Variable to store the coordinates and size of the first detected face
tracked_face = None

while True:
    ret, frame = cap.read()
    
    # If the frame is read correctly, proceed
    if not ret:
        break
    
    # Convert the frame to grayscale for face detection
    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
    
    if tracked_face is None:  # If we haven't found a face yet
        # Detect faces in the frame
        faces = face_cascade.detectMultiScale(gray, scaleFactor=1.1, minNeighbors=5, minSize=(30, 30))
        
        if len(faces) > 0:
            # If at least one face is found, track the first one
            tracked_face = faces[0]
    
    # If a face is already being tracked, use its last known position and size
    if tracked_face is not None:
        x, y, w, h = tracked_face
        frame = blur_face(frame, x, y, w, h)
    
        # Optionally, re-detect the face position but keep the original size
        faces = face_cascade.detectMultiScale(gray, scaleFactor=1.1, minNeighbors=5, minSize=(30, 30))
        if len(faces) > 0:
            # Update the position but retain the original size (w, h)
            x, y, _, _ = faces[0]  # Use the new position but ignore size
            tracked_face = (x, y, w, h)  # Keep the original width and height
    
    # Write the processed frame to the output video
    out.write(frame)
    
    # Optionally, display the frame (can be commented out if not needed)
    cv2.imshow('Blurring Faces', frame)
    if cv2.waitKey(1) & 0xFF == ord('q'):
        break

# Release the video capture and writer objects
cap.release()
out.release()
cv2.destroyAllWindows()
