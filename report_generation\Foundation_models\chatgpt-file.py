# read csv file
import pandas as pd
import os

import pandas as pd
import json
import os
from openai import OpenAI

api_key = '********************************************************************************************************************************************************************'
client = OpenAI(api_key=api_key)
# Function to create a file with the Files API
def create_file(file_path):
  with open(file_path, "rb") as file_content:
    result = client.files.create(
        file=file_content,
        purpose="vision",
    )
    return result.id

def fill_template(template, row):
    return template.format(**row.to_dict())

# Configuration
model = "gpt-4.1-mini-2025-04-14"
csv_path = r"G:\My Drive\Datasets\XAI\merged_output_models.csv"
template_path = r"G:\My Drive\Datasets\XAI\report_prompt.txt"
output_path = r"G:\My Drive\Datasets\XAI\gpt_outputs.json"
plots_path = r"G:\My Drive\Datasets\XAI\images"

# Load template from file
with open(template_path, 'r', encoding='utf-8') as file:
    template = file.read()

# Read CSV
df = pd.read_csv(csv_path)

# Placeholder for all results
results = []

# Iterate over rows and call GPT API
for index, row in df.iterrows():
    file_id = row["ID"]
    user_id, date, hand = file_id.split("_")
    #if user is in test set
    
    image_path = os.path.join(plots_path, user_id , 'finger_tapping', 'images', f"{user_id}_{date}_distance_full_chart_{hand}.png")

    print(f"Processing: {user_id} {date} {hand}")

    try:
        # Example GPT API call
        response = client.responses.create(
            model=model,
            input=[{
                "role": "user",
                "content": [
                    {"type": "input_text", "text": fill_template(template, row)},
                    {"type": "input_image", "file_id": create_file(image_path)},
                ],
            }],
        )

        # Collect output
        results.append({
            "file_id": file_id,
            "user_id": user_id,
            "date": date,
            "hand": hand,
            "today": pd.Timestamp.now().isoformat(),
            "model": model, 
            "output": response.output_text
        })
        print(response.output_text)
        break
    except Exception as e:
        print(f"Error processing {file_id}: {str(e)}")
        
        

# Save results
with open(output_path, "w", encoding='utf-8') as f:
    json.dump(results, f, indent=2, ensure_ascii=False)

print("Done. Results saved to:", output_path)
