import os
import random
import json

def collect_user_ids(src_directory):
    user_ids = [user_id for user_id in os.listdir(src_directory) if os.path.isdir(os.path.join(src_directory, user_id))]
    return user_ids

def is_valid_number(user_id):
    try:
        int(user_id)
        return True
    except ValueError:
        return False

def save_validation_set(user_ids, validation_percentage, save_path):
    ids_start_with_07 = [user_id for user_id in user_ids if user_id.startswith("07")]
    ids_not_start_with_07 = [user_id for user_id in user_ids if not user_id.startswith("07") and is_valid_number(user_id)]
    
    validation_size_07 = int(len(ids_start_with_07) * validation_percentage)
    validation_size_not_07 = int(len(ids_not_start_with_07) * validation_percentage)
    
    validation_user_ids_07 = random.sample(ids_start_with_07, validation_size_07)
    validation_user_ids_not_07 = random.sample(ids_not_start_with_07, validation_size_not_07)
    
    validation_user_ids = validation_user_ids_07 + validation_user_ids_not_07
    
    with open(save_path, 'w') as file:
        json.dump(validation_user_ids, file)
    
    return validation_user_ids

def filter_users(data, user_ids):
    filtered_data = [entry for entry in data if entry[0] in user_ids]
    return filtered_data

def load_validation_user_ids(save_path):
    with open(save_path, 'r') as file:
        user_ids = json.load(file)
    return set(user_ids)

if __name__ == '__main__':
    base_directory = '\\\\files.ubc.ca\\team\\PPRC\\Camera\\CAMERA Booth Data\\Booth\\'
    validation_file = f'.\\csv\\validation_set_2.json'
    validation_percentage = 0.2
    
    # Collect all user IDs
    all_user_ids = collect_user_ids(base_directory)
    
    # Save validation set
    validation_user_ids = save_validation_set(all_user_ids, validation_percentage, validation_file)
    
    print(f"Validation User IDs: {validation_user_ids}")
    
    # Example of loading the validation user IDs and filtering data
    loaded_validation_user_ids = load_validation_user_ids(validation_file)
    print(f"Loaded Validation User IDs: {loaded_validation_user_ids}")

