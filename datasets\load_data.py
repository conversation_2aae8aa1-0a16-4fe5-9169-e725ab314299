import os
import sys
import numpy as np
import pandas as pd
from sklearn.model_selection import train_test_split

from datasets.select_validation_set import load_validation_user_ids

def load_data_user(folder_path):
    data_frames = []
    for file in os.listdir(folder_path):
        if file.endswith('.csv'):
            file_path = os.path.join(folder_path, file)
            df = pd.read_csv(file_path, header=None)

            # Extract feature names from first row and first column
            row_labels = df.iloc[0, 1:].values
            col_labels = df.iloc[1:, 0].values


            df = df.apply(pd.to_numeric, errors='coerce')

            # Combine row_labels and col_labels into feature names
            feature_names = [f"{row}_{col}"  for col in col_labels for row in row_labels]

            # Remove the first row and first column after extracting labels
            df = df.iloc[1:, 1:]
            flattened_features = df.values.flatten()

            # Create a DataFrame for the current file's data
            df_flattened = pd.DataFrame([flattened_features], columns=feature_names)

            # Add the label column
            df_flattened['user'] = file.split('.')[0]

            # Add the current file's DataFrame to the list of DataFrames
            data_frames.append(df_flattened)

    # Concatenate all DataFrames in the list into a single DataFrame
    all_data = pd.concat(data_frames, ignore_index=True)

    return all_data

def load_emotion_task_ts_data(origin_folder, emotions = ['happy', 'sad', 'angry','disgust'], task='emotions', type='text',au_sets=None, regex_filter='^(?!.*catch22).*'):
    dfs = []  # List to store DataFrames for each emotion

    for emotion in emotions:
        output_folder = os.path.join(origin_folder,'facial_expression', emotion, type, 'time_series_features', task)
        df_tsf = load_data_user(output_folder)

        # Add user ID and set it as index
        df_tsf['ID'] = df_tsf['user'].str.split('_').str[0].astype(int)
        df_tsf.set_index('ID', inplace=True)
        df_tsf.drop(columns=['user'], inplace=True)

        # Filter columns based on emotion and excluding 'catch22_'
        if regex_filter:
            df_tsf = df_tsf.filter(regex=regex_filter)
        if(task == 'emotions'):
            df_tsf = df_tsf.filter(regex=emotion)
        elif(task == 'action_units'):
            # Filter columns based on AU sets
            au_columns = au_sets.get(emotion, [])
            au_regex = '|'.join([f'{au.strip()}' for au in au_columns])
            df_tsf = df_tsf.filter(regex=au_regex)

        # Store the processed DataFrame
        dfs.append(df_tsf)

    # Merge all DataFrames
    merged_df = dfs[0]
    for df in dfs[1:]:
        merged_df = merged_df.merge(df, on='ID', how='outer')

    return merged_df

def load_labels_with_ID(origin_folder, label_file):
    label_path = os.path.join(origin_folder, label_file)
    label_df = pd.read_csv(label_path)

    # Set 'ID' as index
    label_df.set_index('ID', inplace=True)

    return label_df


def merge_df_with_labels(data_df, label_df, predict):
    merged_df = pd.merge(label_df, data_df, on='ID')
    merged_df = merged_df.dropna(subset=[predict])

    return merged_df

def get_train_classifiers_set(df,validation_file='.\\csv\\validation_set.json'):
    validation_users = load_validation_user_ids(validation_file)
    validation_users_int = [int(user_id) for user_id in validation_users]
    df = df[~df.index.isin(validation_users_int)]
    train_df, test_df = train_test_split(df, test_size=0.2, random_state=17)
    return train_df

def get_train_ensemble_set(df,validation_file='.\\csv\\validation_set.json'):
    validation_users = load_validation_user_ids(validation_file)
    validation_users_int = [int(user_id) for user_id in validation_users]
    df = df[~df.index.isin(validation_users_int)]
    train_df, test_df = train_test_split(df, test_size=0.2, random_state=17)
    return test_df

def get_train_set(df,validation_file='.\\csv\\validation_set.json'):
    validation_users = load_validation_user_ids(validation_file)
    validation_users_int = [int(user_id) for user_id in validation_users]
    df = df[~df.index.isin(validation_users_int)]
    return df

def get_validations(df,validation_file='.\\csv\\validation_set.json'):
    validation_users = load_validation_user_ids(validation_file)
    validation_users_int = [int(user_id) for user_id in validation_users]
    df = df[df.index.isin(validation_users_int)]
    return df