import cv2
import dlib
from scipy.spatial import distance
import numpy as np

# Define constants
EYE_AR_THRESH = 0.2
EYE_AR_CONSEC_FRAMES = 3
MOVING_AVERAGE_WINDOW = 5

# Initialize counters
blink_counter = 0
frame_counter = 0
ear_values = []

# Load the pre-trained dlib model for facial landmark detection
detector = dlib.get_frontal_face_detector()
predictor = dlib.shape_predictor('./libs/OpenFace_2.2.0/shape_predictor_68_face_landmarks.dat')

def eye_aspect_ratio(eye):
    # Compute the euclidean distances between the vertical eye landmarks
    A = distance.euclidean(eye[1], eye[5])
    B = distance.euclidean(eye[2], eye[4])

    # Compute the euclidean distance between the horizontal eye landmarks
    C = distance.euclidean(eye[0], eye[3])

    # Compute the eye aspect ratio
    ear = (A + B) / (2.0 * C)
    return ear

def detect_blinks(frame, landmarks):
    global blink_counter, frame_counter, ear_values

    # Extract the coordinates of the left and right eyes
    left_eye = [(landmarks.part(i).x, landmarks.part(i).y) for i in range(36, 42)]
    right_eye = [(landmarks.part(i).x, landmarks.part(i).y) for i in range(42, 48)]

    # Calculate the EAR for both eyes
    left_ear = eye_aspect_ratio(left_eye)
    right_ear = eye_aspect_ratio(right_eye)
    
    # Average the EAR values
    ear = (left_ear + right_ear) / 2.0
    
    # Append the current EAR value to the list
    ear_values.append(ear)
    
    # Smooth the EAR values using a moving average
    if len(ear_values) > MOVING_AVERAGE_WINDOW:
        ear_values.pop(0)
    smoothed_ear = np.mean(ear_values)
    
    # Determine the dynamic threshold
    dynamic_threshold = np.mean(ear_values) * 0.9
    
    # Check if the EAR is below the dynamic threshold, indicating a blink
    if ear < dynamic_threshold:
        frame_counter += 1
    else:
        if frame_counter >= EYE_AR_CONSEC_FRAMES:
            blink_counter += 1
        frame_counter = 0

# Open the video file
video_path = '\\\\files.ubc.ca\\team\\PPRC\\Camera\\Booth_Processed\\finger_tapping\\right\\videos\\07137_20240513_right_finger_tapping.mp4'
cap = cv2.VideoCapture(video_path)

while cap.isOpened():
    ret, frame = cap.read()
    if not ret:
        break
    
    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
    faces = detector(gray)

    for face in faces:
        landmarks = predictor(gray, face)
        detect_blinks(frame, landmarks)
    
    # Display the resulting frame with blink counter
    cv2.putText(frame, f"Blinks: {blink_counter}", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
    cv2.imshow('Frame', frame)

    # Break the loop on 'q' key press
    if cv2.waitKey(1) & 0xFF == ord('q'):
        break

# Release the video capture and close all windows
cap.release()
cv2.destroyAllWindows()

print(f"Total number of blinks: {blink_counter}")

