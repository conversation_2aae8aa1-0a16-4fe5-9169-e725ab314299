import pandas as pd
from scipy.spatial import distance
import numpy as np
import os

# Constants
EYE_AR_THRESH = 0.9
EYE_AR_CONSEC_FRAMES = 4
MOVING_AVERAGE_WINDOW = 6

# Function to compute Eye Aspect Ratio (EAR)
def eye_aspect_ratio(eye):
    A = distance.euclidean(eye[1], eye[5])
    B = distance.euclidean(eye[2], eye[4])
    C = distance.euclidean(eye[0], eye[3])
    return (A + B) / (2.0 * C)

# Function to load landmarks data from CSV
def load_landmarks(csv_file):
    return pd.read_csv(csv_file)

# Function to detect blinks in landmarks data
def detect_blinks(landmarks_df):
    blink_counter = 0
    frame_counter = 0
    ear_values = []

    for _, row in landmarks_df.iterrows():
        left_eye = [(row[f'Landmark_{i}_x'], row[f'Landmark_{i}_y']) for i in range(36, 42)]
        right_eye = [(row[f'Landmark_{i}_x'], row[f'Landmark_{i}_y']) for i in range(42, 48)]
        left_ear = eye_aspect_ratio(left_eye)
        right_ear = eye_aspect_ratio(right_eye)
        ear = (left_ear + right_ear) / 2.0
        ear_values.append(ear)
        if len(ear_values) > MOVING_AVERAGE_WINDOW:
            ear_values.pop(0)
        smoothed_ear = np.mean(ear_values)
        dynamic_threshold = smoothed_ear * EYE_AR_THRESH
        if ear < dynamic_threshold:
            frame_counter += 1
        else:
            if frame_counter >= EYE_AR_CONSEC_FRAMES:
                blink_counter += 1
            frame_counter = 0

    return blink_counter

# Main function to process each ID
def process_blinks(landmarks_dir,save_doc):
    results = []
    for files in os.listdir(landmarks_dir):
        landmarks_file = os.path.join(landmarks_dir, files)
        landmarks_df = load_landmarks(landmarks_file)
        total_blinks = detect_blinks(landmarks_df)
        id = files.split('_')[0]
        results.append({'ID': id, 'Blinks': total_blinks, 'Duration': len(landmarks_df)/60})
        print(f'ID: {id}, Blinks: {total_blinks}')

    # Save results to a new CSV file
    results_df = pd.DataFrame(results)
    results_df.to_csv(save_doc, index=False)
    print(f'Results have been saved to {save_doc}')

save_doc = '\\\\files.ubc.ca\\team\\PPRC\\Camera\\Booth_results\\eye_blinking\\Experiment_2\\finger_tapping_right_blink_results.csv'
#save_doc = '\\\\files.ubc.ca\\team\\PPRC\\Camera\\Booth_Processed\\hand_movement\\left_open_close\\csvs\\blink_results_right.csv'
landmarks_dir = '\\\\files.ubc.ca\\team\\PPRC\\Camera\\Booth_Processed\\finger_tapping\\right\\landmarks\\'
#landmarks_dir = '\\\\files.ubc.ca\\team\\PPRC\\Camera\\Booth_Processed\\hand_movement\\left_open_close\\landmarks'
# Example usage
process_blinks( '\\\\files.ubc.ca\\team\\PPRC\\Camera\\Booth_Processed\\finger_tapping\\right\\landmarks\\',save_doc)

# join saved csv file with blink results and save again
# df1 = pd.read_csv(counted_blinks)
# df2 = pd.read_csv(save_doc)
# df = pd.merge(df1, df2, on='ID')
# df['diff'] =  df['Right blinks'] -df['Blinks']
# print (sum(abs(df[df['Right blinks']>=0]['diff'])))
# df.to_csv(save_doc, index=False)

