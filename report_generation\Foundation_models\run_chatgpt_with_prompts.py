#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to run ChatGPT over prompts recorded in CSV file with corresponding images.

This script:
1. Reads a CSV file containing prompts and image file paths
2. For each row, uploads the image to OpenAI
3. Sends the prompt and image to ChatGPT
4. Collects and saves the responses
"""

import pandas as pd
import os
import json
from pathlib import Path
import argparse
from typing import Dict, List, Optional
from datetime import datetime
from openai import OpenAI



class ChatGPTProcessor:
    """Class to handle ChatGPT API calls with image uploads."""
    
    def __init__(self, api_key: str, model: str = "gpt-4o-mini"):
        """
        Initialize the ChatGPT processor.
        
        Args:
            api_key: OpenAI API key
            model: Model to use for ChatGPT
        """
        self.client = OpenAI(api_key=api_key)
        self.model = model
        
    def create_file(self, file_path: str) -> str:
        """
        Upload a file to OpenAI Files API.
        
        Args:
            file_path: Path to the file to upload
            
        Returns:
            File ID from OpenAI
        """
        try:
            with open(file_path, "rb") as file_content:
                result = self.client.files.create(
                    file=file_content,
                    purpose="vision",
                )
                return result.id
        except Exception as e:
            print(f"Error uploading file {file_path}: {e}")
            raise
    
    def send_prompt_with_image(self, prompt: str, image_path: str) -> Dict:
        """
        Send a prompt with an image to ChatGPT.
        
        Args:
            prompt: Text prompt to send
            image_path: Path to the image file
            
        Returns:
            Dictionary containing the response and metadata
        """
        try:
            # Upload the image
            file_id = self.create_file(image_path)
            
            # Send the request
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[{
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt},
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/png;base64,{self._encode_image(image_path)}"
                            }
                        }
                    ],
                }],
                #max_completion_tokens=1000
            )
            
            return {
                "success": True,
                "response": response.choices[0].message.content,
                "usage": response.usage.dict() if response.usage else None,
                "file_id": file_id
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "response": None,
                "usage": None,
                "file_id": None
            }
    
    def _encode_image(self, image_path: str) -> str:
        """Encode image to base64."""
        import base64
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')


def process_csv_with_chatgpt(
    csv_path: str,
    image_base_path: str,
    output_path: str,
    api_key: str,
    model: str = "gpt-4o-mini",
    max_records: Optional[int] = None
) -> None:
    """
    Process CSV file with ChatGPT API calls.
    
    Args:
        csv_path: Path to CSV file with prompts
        image_base_path: Base path for image files
        output_path: Path to save results
        api_key: OpenAI API key
        model: ChatGPT model to use
        max_records: Maximum number of records to process (None for all)
    """
    
    # Initialize processor
    processor = ChatGPTProcessor(api_key, model)
    
    # Load CSV
    print(f"Loading CSV from: {csv_path}")
    df = pd.read_csv(csv_path)
    print(f"Loaded {len(df)} records")
    
    # Validate required columns
    required_columns = ['file_name', 'prompt', 'image_file']
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        raise ValueError(f"Missing required columns: {missing_columns}")
    
    # Limit records if specified
    if max_records:
        df = df.head(max_records)
        print(f"Processing first {len(df)} records")
    
    # Process each record
    results = []
    successful = 0
    failed = 0
    
    for index, row in df.iterrows():
        file_name = row['file_name']
        prompt = row['prompt']
        image_file = row['image_file']
        
        print(f"\nProcessing {index + 1}/{len(df)}: {file_name}")
        
        # Construct full image path
        if os.path.isabs(image_file):
            image_path = image_file
        else:
            image_path = os.path.join(image_base_path, image_file)
        
        # Check if image exists
        if not os.path.exists(image_path):
            print(f"  Warning: Image not found: {image_path}")
            results.append({
                "file_name": file_name,
                "prompt": prompt,
                "image_path": image_path,
                "success": False,
                "error": "Image file not found",
                "response": None,
                "timestamp": datetime.now().isoformat(),
                "model": model
            })
            failed += 1
            continue
        
        # Send to ChatGPT
        print(f"  Sending prompt to ChatGPT...")
        result = processor.send_prompt_with_image(prompt, image_path)
        
        # Collect results
        record = {
            "file_name": file_name,
            "prompt": prompt,
            "image_path": image_path,
            "success": result["success"],
            "response": result["response"],
            "error": result.get("error"),
            "usage": result.get("usage"),
            "file_id": result.get("file_id"),
            "timestamp": datetime.now().isoformat(),
            "model": model
        }
        
        results.append(record)
        
        if result["success"]:
            successful += 1
            print(f"  ✓ Success")
            print(f"  Response preview: {result['response'][:100]}...")
        else:
            failed += 1
            print(f"  ✗ Failed: {result.get('error')}")
            
    
    # Save results
    print(f"\nSaving results to: {output_path}")
    
    # Save as JSON for detailed results
    json_output = output_path.replace('.csv', '.json') if output_path.endswith('.csv') else output_path + '.json'
    with open(json_output, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    # Save as CSV for easy viewing
    csv_output = output_path.replace('.json', '.csv') if output_path.endswith('.json') else output_path + '.csv'
    results_df = pd.DataFrame(results)
    results_df.to_csv(csv_output, index=False, encoding='utf-8-sig')
    
    # Summary
    print(f"\nProcessing completed:")
    print(f"  Total records: {len(df)}")
    print(f"  Successful: {successful}")
    print(f"  Failed: {failed}")
    print(f"  Results saved to: {json_output} and {csv_output}")


def main():
    """Main function to handle command line arguments."""
    parser = argparse.ArgumentParser(
        description="Run ChatGPT over prompts in CSV file with image uploads"
    )
    
    parser.add_argument(
        "--csv_path",
        default=r"G:\My Drive\Datasets\XAI\test_set_prompts.csv",
        help="Path to CSV file containing prompts and image paths"
    )
    
    parser.add_argument(
        "--image-base-path",
        default=r"G:\My Drive\Datasets\XAI\images\full-chart",
        help="Base path for image files (if image paths in CSV are relative)"
    )
    
    parser.add_argument(
        "--output",
        default=r"G:\My Drive\Datasets\XAI\gpt-outputs",
        help="Output file path (without extension)"
    )
    
    parser.add_argument(
        "--api-key",
        default = '********************************************************************************************************************************************************************',
        help="OpenAI API key (if not provided, will try to read from environment)"
    )
    
    parser.add_argument(
        "--model",
        default="gpt-5",
        help="ChatGPT model to use (default: gpt-4o-mini)"
    )
    
    parser.add_argument(
        "--max-records",
        type=int,
        default=60,
        help="Maximum number of records to process (for testing)"
    )
    
    args = parser.parse_args()
    
    # Get API key
    api_key = args.api_key
    if not api_key:
        api_key = os.getenv('OPENAI_API_KEY')
        if not api_key:
            raise ValueError("API key not provided. Use --api-key or set OPENAI_API_KEY environment variable")
    
    # Process the CSV
    process_csv_with_chatgpt(
        args.csv_path,
        args.image_base_path,
        args.output,
        api_key,
        args.model,
        args.max_records
    )


if __name__ == "__main__":
    main()
