#read labels
import pandas as pd
import os
from scipy.signal import savgol_filter  
import numpy as np
from tqdm import tqdm  # Import tqdm for progress display
from tensorflow.keras.preprocessing.sequence import pad_sequences
#create y
labels_path = r'\\files.ubc.ca\team\PPRC\CAMERA\Booth_Processed\finger_tapping\docs\weak_supervision_final.csv'
df_labels = pd.read_csv(labels_path)
df_labels.set_index('ID',inplace=True)
y = df_labels['snorkel_label_final']
#kip first duplicate
y = y[~y.index.duplicated(keep='first')]
y.dropna(inplace=True)
y_binary = y>0


#read labels
import pandas as pd
import os
import numpy as np
from tqdm import tqdm  # Import tqdm for progress display
from tensorflow.keras.preprocessing.sequence import pad_sequences



#create x
tasks=['right', 'left']

df_combined = pd.DataFrame(columns=['values', 'ID'])
df_combined['ID'] = y.index
df_combined.set_index('ID', inplace=True)

for task in tasks:
    file =  rf'\\files.ubc.ca\team\PPRC\Camera\Booth_Processed\finger_tapping\{task}\distances'
    segments_file = rf'\\files.ubc.ca\team\PPRC\Camera\Booth_Processed\finger_tapping\{task}\csvs\{task}_best_segments_and_indexes.csv'
    segments_file = pd.read_csv(segments_file).set_index('file_name')
    
    for root, dirs, filenames in os.walk(file):
        # Initialize progress bar
        filenames = [f for f in filenames if f.endswith('.csv')]
        with tqdm(total=len(filenames), desc=f"Processing {file}", unit="file") as pbar:
            for filename in filenames:
                file_path = os.path.join(root, filename)
                distances = pd.read_csv(file_path)[['Frame', 'Finger Distance', 'Finger Normalized Distance', 'Angular Distance', 'Wrist Coordinate']]
                if len(distances) < 5:
                    print(f"File {file} has less than 5 frames. Skipping...")
                    pbar.update(1)
                    continue
                
                #filter based on segments
                # if filename in segments_file.index:
                #     start,end,_ = segments_file.loc[filename].values
                #     distances = distances[(distances['Frame'] >= start) & (distances['Frame'] <= end)]
                    
                    
                normalized_distance = distances['Finger Normalized Distance']
                file_id = filename.split('_finger')[0]
                df_combined.loc[file_id, 'values'] = normalized_distance.to_numpy()
                pbar.update(1)
                
      

from tensorflow.keras.preprocessing.sequence import pad_sequences


#shuffle df_vcombined 
df_combined_shuffled = df_combined.copy().sample(frac=1, random_state=42)
# Select valid IDs
valid_ids = df_combined_shuffled['values'].dropna().index.intersection(y_binary.index)

# Extract sequences
sequences = df_combined_shuffled.loc[valid_ids, 'values'].tolist()

# Pad or truncate sequences to equal length
X_padded = pad_sequences(sequences, maxlen=450, dtype='float32', padding='post', truncating='post')


# Expand dims to match required shape: (samples, series_length, 1)
X = X_padded
y_binary = y_binary.loc[valid_ids]
y = y.loc[valid_ids]



# Check shapes
print(X.shape, y_binary.shape)


from sklearn.model_selection import train_test_split
from aeon.classification.deep_learning import TimeCNNClassifier
from sklearn.metrics import classification_report, confusion_matrix
from tensorflow.keras.preprocessing.sequence import pad_sequences

# Aeon classifiers
from aeon.classification.convolution_based import (
    MiniRocketClassifier,
    RocketClassifier,
    MultiRocketClassifier,
)
from aeon.classification.deep_learning import (
    InceptionTimeClassifier,
    ResNetClassifier,
    FCNClassifier,
)
from aeon.classification.dictionary_based import (
    BOSSEnsemble,
    WEASEL,
    TemporalDictionaryEnsemble,
)
from aeon.classification.distance_based import (
    KNeighborsTimeSeriesClassifier,
    ElasticEnsemble,
    ProximityForest,
)
from aeon.classification.shapelet_based import ShapeletTransformClassifier
from aeon.classification.feature_based import Catch22Classifier

X_train, X_test, y_train, y_test = train_test_split(X, y_binary, test_size=0.15, random_state=42, stratify=y_binary)
# Dictionary of classifiers
classifiers = {
     "MiniRocket": MiniRocketClassifier(),#0.88
    # "Rocket": RocketClassifier(),0.8
    # "MultiRocket": MultiRocketClassifier(),0.8
    # "InceptionTime": InceptionTimeClassifier(),
    # "ResNet": ResNetClassifier(),
    # "FCN": FCNClassifier(),
    # "BOSSEnsemble": BOSSEnsemble(),#0.77
    # "WEASEL": WEASEL(),#0.86
    # "TemporalDictionaryEnsemble": TemporalDictionaryEnsemble(),#0.81
    # "KNeighbors": KNeighborsTimeSeriesClassifier(),#0.75
    #"ElasticEnsemble": ElasticEnsemble(),
    #"ProximityForest": ProximityForest(),
    #"ShapeletTransform": ShapeletTransformClassifier(),
    #"Catch22Classifier": Catch22Classifier(),#0.84
}

# Evaluate each classifier
for name, clf in classifiers.items():
    print(f'\n=== Training {name} ===')
    clf.fit(X_train, y_train)
    y_pred = clf.predict(X_test)
    print(f'Classification Report for {name}:\n')
    print(classification_report(y_test, y_pred))

# test all classifiers
from sklearn.linear_model import LogisticRegression
from sklearn.model_selection import train_test_split
from aeon.classification.deep_learning import TimeCNNClassifier
from sklearn.metrics import classification_report, confusion_matrix
from tensorflow.keras.preprocessing.sequence import pad_sequences

# Aeon classifiers
from aeon.classification.convolution_based import (
    MiniRocketClassifier,
    RocketClassifier,
    MultiRocketClassifier,
)
from aeon.classification.deep_learning import (
    InceptionTimeClassifier,
    ResNetClassifier,
    FCNClassifier,
)
from aeon.classification.dictionary_based import (
    BOSSEnsemble,
    WEASEL,
    TemporalDictionaryEnsemble,
)
from aeon.classification.distance_based import (
    KNeighborsTimeSeriesClassifier,
    ElasticEnsemble,
    ProximityForest,
)
from aeon.classification.shapelet_based import ShapeletTransformClassifier
from aeon.classification.feature_based import Catch22Classifier

y[y==4]=3
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.15, random_state=22, stratify=y_binary)
# Dictionary of classifiers
classifiers = {
    "MiniRocket": MiniRocketClassifier(),
    "Rocket": RocketClassifier(),
    "MultiRocket": MultiRocketClassifier(),
    #"InceptionTime": InceptionTimeClassifier(),
    #"ResNet": ResNetClassifier(),
    #"FCN": FCNClassifier(),
    #"BOSSEnsemble": BOSSEnsemble(),
    #"WEASEL": WEASEL(),
    #"TemporalDictionaryEnsemble": TemporalDictionaryEnsemble(),
    #"KNeighbors": KNeighborsTimeSeriesClassifier(),
    #"ElasticEnsemble": ElasticEnsemble(),
    #"ProximityForest": ProximityForest(),
    #"ShapeletTransform": ShapeletTransformClassifier(),
    #"Catch22Classifier": Catch22Classifier(),
}

# Evaluate each classifier
for name, clf in classifiers.items():
    print(f'\n=== Training {name} ===')
    clf.fit(X_train, y_train)
    y_pred = clf.predict(X_test)
    print(f'Classification Report for {name}:\n')
    print(classification_report(y_test, y_pred))
    #show confusion matrix
    cm = confusion_matrix(y_test, y_pred)
    print(f'Confusion Matrix for {name}:\n')
    print(cm)

#labels Analysis

df_experts = df_labels[['KW','MG','SA','WM']]
df_experts.dropna(how='all',inplace=True)

df_experts['number_of_experts'] = df_experts.notnull().sum(axis=1)
df_experts['majority_vote'] = df_experts[['KW','MG','SA','WM']].mode(axis=1)[0]
df_experts = df_experts[df_experts['number_of_experts']>1]

y = df_experts['majority_vote']
y_binary = y>0

# Select valid IDs
valid_ids = df_combined['values'].dropna().index.intersection(y_binary.index)


# Extract sequences
sequences = df_combined.loc[valid_ids, 'values'].tolist()

# Pad or truncate sequences to equal length
X_padded = pad_sequences(sequences, maxlen=500, dtype='float32', padding='post', truncating='post')


# Expand dims to match required shape: (samples, series_length, 1)
X = X_padded
y_binary = y_binary.loc[valid_ids]
y = y.loc[valid_ids]

# Check shapes
print(X.shape, y_binary.shape)



#Add diff and diff2
from sklearn.model_selection import StratifiedKFold
from sklearn.metrics import classification_report, confusion_matrix
from aeon.classification.convolution_based import MiniRocketClassifier, MultiRocketHydraClassifier,RocketClassifier
import numpy as np
from scipy.ndimage import gaussian_filter1d
from scipy.signal import savgol_filter  
# Time series augmentation library
from tsaug import TimeWarp, AddNoise, Drift, Crop

def smooth_signal(signal, window_length=20, polyorder=2):
    if len(signal) < window_length:
        return signal
    return savgol_filter(signal, window_length=window_length, polyorder=polyorder)

# Define a function to calculate differences and apply Gaussian smoothing
def calculate_diffs_and_smooth(X):
    n_samples, series_length = X.shape

    # Smooth original
    X_smooth = smooth_signal(X)

    # First difference (same length using prepend)
    X_diff = np.diff(X, axis=1, prepend=X[:, [0]])
    X_diff_smooth = gaussian_filter1d(X_diff, sigma=1, axis=1)

    # Second difference (same length using prepend again)
    X_diff2 = np.diff(X_diff, axis=1, prepend=X_diff[:, [0]])
    X_diff2_smooth = gaussian_filter1d(X_diff2, sigma=1, axis=1)
    
    #add fourier transform
    X_fft = np.fft.fft(X, axis=1)
    X_fft = np.abs(X_fft)  # Take the magnitude of the FFT
    X_fft_smooth = gaussian_filter1d(X_fft, sigma=1, axis=1)
    #return X
    return np.stack([X, X_fft], axis=1)

# Assuming X and y are already defined and processed as per your initial setup

# Perform class label adjustment and filtering (as per your original code)
y = np.where(y == 4, 3, y)  # Change label 4 to 3
mask = (y == 0) | (y == 1) | (y == 2) | (y == 3)
y_np_comp = y[mask]
X_comp = X[mask]

# Initialize StratifiedKFold for cross-validation
kf = StratifiedKFold(n_splits=5, shuffle=True, random_state=13)
scores = []
maes = []

print("\n=== Manual Cross-Validation for MiniRocketClassifier + Augmentation ===")

# Iterate over folds
for fold, (train_index, test_index) in enumerate(kf.split(X_comp, y_np_comp), 1):
    X_train, X_test = X_comp[train_index], X_comp[test_index]
    y_train, y_test = y_np_comp[train_index], y_np_comp[test_index]

    # Preprocess training and testing data with differences and smoothing
    X_train_augmented = calculate_diffs_and_smooth(X_train)
    X_test_augmented = calculate_diffs_and_smooth(X_test)

    # Train classifier
    clf = MiniRocketClassifier(random_state=42,n_kernels=15000)
    clf.fit(X_train_augmented, y_train)
    y_pred = clf.predict(X_test_augmented)

    print(f"\n--- Fold {fold} ---")
    #print(classification_report(y_test, y_pred))
    cm = confusion_matrix(y_test, y_pred)
    print(f"Confusion Matrix:\n{cm}")

    accuracy = np.mean(y_pred == y_test)
    scores.append(accuracy)
    #print MAE and MSE
    print(f"Mean Absolute Error: {np.mean(np.abs(y_pred - y_test)):.4f}")
    print(f"Mean Squared Error: {np.mean((y_pred - y_test)**2):.4f}")
    maes.append(np.mean(np.abs(y_pred - y_test)))
    
    #print ids that diff is two or more
    diff = np.abs(y_pred - y_test)
    diff_ids = np.where(diff >= 2)[0]
    if len(diff_ids) > 0:
        user_id = df_combined.iloc[test_index[diff_ids]].index
        print(f"IDs with differences of 2 or more: {user_id.tolist()}")

print(f"\nCross-Validation Scores: {scores}")
print(f"Mean Accuracy: {np.mean(scores):.4f}")
print(f"Mean Absolute Error: {np.mean(maes):.4f}")


#Add diff and diff2
from sklearn.model_selection import StratifiedKFold
from sklearn.metrics import classification_report, confusion_matrix
from aeon.classification.convolution_based import MiniRocketClassifier, MultiRocketHydraClassifier,RocketClassifier
import numpy as np
from scipy.ndimage import gaussian_filter1d
from scipy.signal import savgol_filter  
# Time series augmentation library
from tsaug import TimeWarp, AddNoise, Drift, Crop

def smooth_signal(signal, window_length=20, polyorder=2):
    if len(signal) < window_length:
        return signal
    return savgol_filter(signal, window_length=window_length, polyorder=polyorder)

# Define a function to calculate differences and apply Gaussian smoothing
def calculate_diffs_and_smooth(X):
    n_samples, series_length = X.shape

    # Smooth original
    X_smooth = smooth_signal(X)

    # First difference (same length using prepend)
    X_diff = np.diff(X, axis=1, prepend=X[:, [0]])
    X_diff_smooth = gaussian_filter1d(X_diff, sigma=1, axis=1)

    # Second difference (same length using prepend again)
    X_diff2 = np.diff(X_diff, axis=1, prepend=X_diff[:, [0]])
    X_diff2_smooth = gaussian_filter1d(X_diff2, sigma=1, axis=1)
    
    #return X
    return np.stack([X, X_diff], axis=1)

# Assuming X and y are already defined and processed as per your initial setup

# Perform class label adjustment and filtering (as per your original code)
y = np.where(y == 4, 3, y)  # Change label 4 to 3
mask = (y == 0) | (y == 1) | (y == 2) | (y == 3)
y_np_comp = y[mask]
X_comp = X[mask]

# Initialize StratifiedKFold for cross-validation
kf = StratifiedKFold(n_splits=8, shuffle=True, random_state=10)
scores = []
maes = []

print("\n=== Manual Cross-Validation for MiniRocketClassifier + Augmentation ===")

# Iterate over folds
for fold, (train_index, test_index) in enumerate(kf.split(X_comp, y_np_comp), 1):
    X_train, X_test = X_comp[train_index], X_comp[test_index]
    y_train, y_test = y_np_comp[train_index], y_np_comp[test_index]

    # Preprocess training and testing data with differences and smoothing
    X_train_augmented = calculate_diffs_and_smooth(X_train)
    X_test_augmented = calculate_diffs_and_smooth(X_test)

    # Train classifier
    clf = MiniRocketClassifier(random_state=42,n_kernels=12000)
    clf.fit(X_train_augmented, y_train)
    y_pred = clf.predict(X_test_augmented)

    print(f"\n--- Fold {fold} ---")
    #print(classification_report(y_test, y_pred))
    cm = confusion_matrix(y_test, y_pred)
    print(f"Confusion Matrix:\n{cm}")

    accuracy = np.mean(y_pred == y_test)
    scores.append(accuracy)
    #print MAE and MSE
    print(f"Mean Absolute Error: {np.mean(np.abs(y_pred - y_test)):.4f}")
    print(f"Mean Squared Error: {np.mean((y_pred - y_test)**2):.4f}")
    maes.append(np.mean(np.abs(y_pred - y_test)))

print(f"\nCross-Validation Scores: {scores}")
print(f"Mean Accuracy: {np.mean(scores):.4f}")
print(f"Mean Absolute Error: {np.mean(maes):.4f}")


import numpy as np
import pandas as pd
from sklearn.metrics import classification_report, confusion_matrix, mean_absolute_error, mean_squared_error
from aeon.classification.convolution_based import MiniRocketClassifier
from scipy.ndimage import gaussian_filter1d
from scipy.signal import savgol_filter

# -------------------------
# Preprocessing Functions
# -------------------------

def smooth_signal(signal, window_length=20, polyorder=2):
    """Apply Savitzky-Golay smoothing to a 1D signal."""
    if len(signal) < window_length:
        return signal
    return savgol_filter(signal, window_length=window_length, polyorder=polyorder)

def calculate_diffs_and_smooth(X):
    """Compute first/second differences and FFT, apply smoothing, and stack them."""
    X_diff = np.diff(X, axis=1, prepend=X[:, [0]])
    X_diff2 = np.diff(X_diff, axis=1, prepend=X_diff[:, [0]])
    X_fft = np.abs(np.fft.fft(X, axis=1))

    # Apply smoothing
    X_diff_smooth = gaussian_filter1d(X_diff, sigma=1, axis=1)
    X_diff2_smooth = gaussian_filter1d(X_diff2, sigma=1, axis=1)
    X_fft_smooth = gaussian_filter1d(X_fft, sigma=1, axis=1)

    return np.stack([X,X_fft,X_diff_smooth], axis=1)

# -------------------------
# Load and Prepare Data
# -------------------------

# Load test set file names
test_set_path = r'\\files.ubc.ca\team\PPRC\Camera\Booth_Processed\finger_tapping\docs\test-set-balanced.csv'
test_file_names = pd.read_csv(test_set_path)['file_name']

# Create mask for test and train split
test_mask = valid_ids.isin(test_file_names)
y[y == 4] = 3  # Merge class 4 into class 3

X_train, y_train = X[~test_mask], y[~test_mask]
X_test, y_test = X[test_mask], y[test_mask]

# Preprocess signals
X_train_aug = calculate_diffs_and_smooth(X_train)
X_test_aug = calculate_diffs_and_smooth(X_test)

# -------------------------
# Train and Evaluate Classifier
# -------------------------

clf = MiniRocketClassifier(random_state=42, n_kernels=10000)
clf.fit(X_train_aug, y_train)
y_pred = clf.predict(X_test_aug)

# Evaluation
print(classification_report(y_test, y_pred))
print("Confusion Matrix:\n", confusion_matrix(y_test, y_pred))
print(f"Accuracy: {np.mean(y_pred == y_test):.4f}")
print(f"MAE: {mean_absolute_error(y_test, y_pred):.4f}")
print(f"MSE: {mean_squared_error(y_test, y_pred):.4f}")

# -------------------------
# Show Large Mismatches
# -------------------------

diff = np.abs(y_pred - y_test)
large_errors = np.where(diff >= 2)[0]

if len(large_errors) > 0:
    print("\nLarge mismatches (2 or more levels):")
    print("Predicted:", y_pred[large_errors])
    print("True     :", y_test[large_errors])


from aeon.transformations.collection.convolution_based import MiniRocket
from sklearn.linear_model import RidgeClassifierCV
from sklearn.pipeline import make_pipeline

# 1. Instantiate the MiniRocket transformer
transformer = MiniRocket(random_state=42,n_kernels=8000)

# 2. Fit the transformer to the training data
transformer.fit(X_train_augmented)

# 3. Transform both train and test sets
X_train_features = transformer.transform(X_train_augmented)
X_test_features = transformer.transform(X_test_augmented)

# 4. Train a classifier on the features
clf = RidgeClassifierCV(
    class_weight='balanced',        # handle class imbalance
    cv=10,                            # k-fold cross-validation
    scoring='neg_mean_absolute_error',             # metric for model selection
)
clf.fit(X_train_features, y_train)

# 5. Predict
y_pred = clf.predict(X_test_features)

# 6. Evaluate
print(classification_report(y_test, y_pred))
print(f"Confusion Matrix:\n{confusion_matrix(y_test, y_pred)}")
print(f"Accuracy: {np.mean(y_pred == y_test):.4f}")
print(f"MAE: {np.mean(np.abs(y_pred - y_test)):.4f}")
print(f"MSE: {np.mean((y_pred - y_test)**2):.4f}")

# 7. Access and inspect features
print("Shape of Rocket features:", X_test_features.shape)




from aeon.classification.interval_based import TimeSeriesForestClassifier
from sklearn.metrics import classification_report, confusion_matrix
import numpy as np

# 1. Instantiate and train the Time Series Forest model
clf = TimeSeriesForestClassifier(n_estimators=3000, random_state=42)
# Fit the model on the augmented training data
clf.fit(X_train_augmented, y_train)

# 2. Predict on the test set
y_pred = clf.predict(X_test_augmented)

# 3. Evaluation
print(classification_report(y_test, y_pred))
print("Confusion Matrix:\n", confusion_matrix(y_test, y_pred))
print(f"Accuracy: {np.mean(y_pred == y_test):.4f}")
print(f"MAE: {np.mean(np.abs(y_pred - y_test)):.4f}")
print(f"MSE: {np.mean((y_pred - y_test)**2):.4f}")


from sklearn.manifold import TSNE
import matplotlib.pyplot as plt

# 8. t-SNE visualization of the test features
tsne = TSNE(n_components=2, random_state=42, perplexity=20, n_iter=3000)
X_test_embedded = tsne.fit_transform(X_test_features)

# 9. Plot
plt.figure(figsize=(8, 6))
scatter = plt.scatter(X_test_embedded[:, 0], X_test_embedded[:, 1], c=y_test, cmap='tab10', alpha=0.8)
plt.title("t-SNE of MiniRocket Features (Test Set)")
plt.xlabel("Component 1")
plt.ylabel("Component 2")
plt.colorbar(scatter, label='True Label')
plt.grid(True)
plt.tight_layout()
plt.show()


# 3classifier
from sklearn.model_selection import StratifiedKFold
from sklearn.metrics import classification_report, confusion_matrix
from aeon.classification.convolution_based import MultiRocketClassifier
import numpy as np
from scipy.stats import mode

# Select only samples from classes 1, 2, and 3
mask = (y == 1) | (y == 2) | (y == 3)
y_np_comp = y[mask]
X_comp = X[mask]

kf = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
scores = []

print("\n=== Cross-Validation with 3 Binary Classifiers + Majority Voting ===")

for fold, (train_index, test_index) in enumerate(kf.split(X_comp, y_np_comp), 1):
    X_train, X_test = X_comp[train_index], X_comp[test_index]
    y_train, y_test = y_np_comp[train_index], y_np_comp[test_index]

    # Prepare data for each binary classification
    def prepare_binary(X, y, class_a, class_b):
        mask = (y == class_a) | (y == class_b)
        X_bin = X[mask]
        y_bin = y[mask]
        y_bin = np.where(y_bin == class_a, 0, 1)
        return X_bin, y_bin

    # 1 vs 3
    X_13, y_13 = prepare_binary(X_train, y_train, 1, 3)
    clf_13 = MultiRocketClassifier(random_state=42, n_jobs=8, class_weight='balanced')
    clf_13.fit(X_13, y_13)

    # 2 vs 3
    X_23, y_23 = prepare_binary(X_train, y_train, 2, 3)
    clf_23 = MultiRocketClassifier(random_state=42, n_jobs=8, class_weight='balanced')
    clf_23.fit(X_23, y_23)

    # 1 vs 2
    X_12, y_12 = prepare_binary(X_train, y_train, 1, 2)
    clf_12 = MultiRocketClassifier(random_state=42, n_jobs=8, class_weight='balanced')
    clf_12.fit(X_12, y_12)

    # Predict with each classifier on the full test set
    def binary_predict(clf, class_a, class_b, X_test):
        pred_bin = clf.predict(X_test)
        return np.where(pred_bin == 0, class_a, class_b)

    pred_13 = binary_predict(clf_13, 1, 3, X_test)
    pred_23 = binary_predict(clf_23, 2, 3, X_test)
    pred_12 = binary_predict(clf_12, 1, 2, X_test)

    # Majority voting
    preds = np.vstack([pred_13, pred_23, pred_12])
    y_pred_majority = mode(preds, axis=0).mode

    # Evaluation
    print(f"\n--- Fold {fold} ---")
    print(classification_report(y_test, y_pred_majority))
    cm = confusion_matrix(y_test, y_pred_majority)
    print(f"Confusion Matrix:\n{cm}")

    accuracy = np.mean(y_pred_majority == y_test)
    scores.append(accuracy)

print(f"\nCross-Validation Scores: {scores}")
print(f"Mean Accuracy: {np.mean(scores):.4f}")


#regression
from aeon.regression.convolution_based import MiniRocketRegressor,MultiRocketRegressor
from sklearn.model_selection import StratifiedKFold
from sklearn.metrics import mean_squared_error, classification_report, confusion_matrix
import numpy as np

#Add diff and diff2
from sklearn.model_selection import StratifiedKFold
from sklearn.metrics import classification_report, confusion_matrix
from aeon.classification.convolution_based import MiniRocketClassifier, MultiRocketHydraClassifier,RocketClassifier
import numpy as np
from scipy.ndimage import gaussian_filter1d
from scipy.signal import savgol_filter  
# Time series augmentation library
from tsaug import TimeWarp, AddNoise, Drift, Crop

# Define a function to calculate differences and apply Gaussian smoothing
def calculate_diffs_and_smooth(X):
    n_samples, series_length = X.shape

    # Smooth original
    X_smooth = smooth_signal(X)

    # First difference (same length using prepend)
    X_diff = np.diff(X, axis=1, prepend=X[:, [0]])
    X_diff_smooth = gaussian_filter1d(X_diff, sigma=1, axis=1)

    # Second difference (same length using prepend again)
    X_diff2 = np.diff(X_diff, axis=1, prepend=X_diff[:, [0]])
    X_diff2_smooth = gaussian_filter1d(X_diff2, sigma=1, axis=1)
    
    #return X
    return np.stack([X, X_diff], axis=1)

# Assuming X and y are already defined and processed as per your initial setup

# Perform class label adjustment and filtering (as per your original code)
y = np.where(y == 4, 3, y)  # Change label 4 to 3
mask = (y == 0) | (y == 1) | (y == 2) | (y == 3)
y_np_comp = y[mask]
X_comp = X[mask]

# Initialize StratifiedKFold for cross-validation
kf = StratifiedKFold(n_splits=8, shuffle=True, random_state=10)
accuracy_scores = []
mse_scores = []
mae_scores = [] 


print("\n=== Manual Cross-Validation for MiniRocketRegressor + Augmentation ===")

# Iterate over folds
for fold, (train_index, test_index) in enumerate(kf.split(X_comp, y_np_comp), 1):
    X_train, X_test = X_comp[train_index], X_comp[test_index]
    y_train, y_test = y_np_comp[train_index], y_np_comp[test_index]

    # Preprocess training and testing data with differences and smoothing
    X_train_augmented = calculate_diffs_and_smooth(X_train)
    X_test_augmented = calculate_diffs_and_smooth(X_test)

    
    reg = MiniRocketRegressor(random_state=42)
    reg.fit(X_train, y_train)
    y_pred_reg = reg.predict(X_test)

    # Calculate MSE
    mse = mean_squared_error(y_test, y_pred_reg)
    mse_scores.append(mse)
    
    # Calculate MAE
    mae = np.mean(np.abs(y_pred_reg - y_test))
    mae_scores.append(mae)
    

    # Convert regression output to class by rounding
    y_pred_class = np.round(y_pred_reg).astype(int)

    # Classification metrics
    accuracy = np.mean(y_pred_class == y_test)
    accuracy_scores.append(accuracy)

    print(f"\n--- Fold {fold} ---")
    print(f"MSE: {mse:.4f}")
    print(f"Accuracy (rounded): {accuracy:.4f}")
    print("Classification Report:")
    print(classification_report(y_test, y_pred_class))
    print("Confusion Matrix:")
    print(confusion_matrix(y_test, y_pred_class))

print("\n=== Summary ===")
print(f"Mean MSE: {np.mean(mse_scores):.4f}")
print(f"Mean Classification Accuracy: {np.mean(accuracy_scores):.4f}")
print(f"Mean MAE: {np.mean(mae_scores):.4f}")


#test parameters
from aeon.classification.convolution_based import MiniRocketClassifier
from sklearn.linear_model import RidgeClassifierCV, LogisticRegression
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import StratifiedKFold
from sklearn.metrics import classification_report, confusion_matrix
import numpy as np

# Merge class 4 into class 3
y_np = np.where(y == 4, 3, y)

# Classifiers to try
estimators = {
    "RidgeClassifierCV": RidgeClassifierCV(),
    "LogisticRegression": LogisticRegression(max_iter=1000),
    "RandomForest": RandomForestClassifier(n_estimators=100, random_state=42)
}

kf = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)

for name, est in estimators.items():
    print(f"\n=== Testing MiniRocketClassifier with {name} ===")
    scores = []

    for fold, (train_index, test_index) in enumerate(kf.split(X, y_np), 1):
        X_train, X_test = X[train_index], X[test_index]
        y_train, y_test = y_np[train_index], y_np[test_index]

        clf = MiniRocketClassifier(estimator=est)
        clf.fit(X_train, y_train)
        y_pred = clf.predict(X_test)

        accuracy = np.mean(y_pred == y_test)
        scores.append(accuracy)

        print(f"\n--- Fold {fold} ---")
        print(f"Accuracy: {accuracy:.4f}")
        print("Classification Report:")
        print(classification_report(y_test, y_pred))
        print("Confusion Matrix:")
        print(confusion_matrix(y_test, y_pred))

    print(f"\nMean Accuracy for {name}: {np.mean(scores):.4f}")


import pickle
import numpy as np
from aeon.classification.convolution_based import MiniRocketClassifier
from sklearn.metrics import accuracy_score, classification_report
from sklearn.model_selection import train_test_split
from sklearn.decomposition import PCA

def prepare_and_fit_minirocket_with_pca(X, selected_landmarks=[4,8], pca_components=8):
    """
    Prepare data, apply PCA on embeddings, and fit MiniRocketClassifier.

    Parameters:
        X: np.ndarray (shape: [samples, 1, series_length, landmarks, embedding_dim])
        y: np.ndarray (shape: [samples, ])
        selected_landmarks: landmarks indices to keep
        pca_components: number of PCA dimensions (default: 2)

    Returns:
        classifier: trained MiniRocketClassifier model
    """

    # Step 1: Remove redundant dimension
    X = np.squeeze(X, axis=1)  # (300, 400, 21, 16)

    # Step 2: Select specific landmarks
    X = X[:, :, selected_landmarks, :]  # (300, 400, 3, 16)

    # Step 3: Apply PCA on embeddings (last dimension)
    samples, series_length, n_landmarks, embed_dim = X.shape
    X_flat = X.reshape(-1, embed_dim)  # Combine samples, time, landmarks: (300*400*3, 16)

    # PCA fit-transform
    pca = PCA(n_components=pca_components)
    X_pca = pca.fit_transform(X_flat)  # (300*400*3, 2)

    # Restore original dimensions with reduced embeddings
    X_pca = X_pca.reshape(samples, series_length, n_landmarks, pca_components)  # (300, 400, 3, 2)

    # Step 4: Merge landmark and reduced embeddings into channels
    X_final = X_pca.reshape(samples, series_length, n_landmarks * pca_components)  # (300, 400, 6)

    # Transpose to (samples, channels, series_length)
    X_final = np.transpose(X_final, (0, 2, 1))  # (300, 6, 400)


    return X_final


data = np.load(r"G:\My Drive\Temporary Atefeh\stmae_embeddings_pd_4.npy",allow_pickle=True)
embeddings = data["embeddings"]
labels = data["labels"]
indexes = data["indexes"]
labels[labels==4] = 3

# Convert embeddings to format required by Aeon (n_samples, n_channels, series_length)
# Currently, embeddings might be (n_samples, embedding_dim). 
# MiniRocket expects at least 3D input (samples, channels, length).
# Here, we reshape assuming embeddings represent single-channel, and length equals embedding_dim.
embeddings = embeddings[:, np.newaxis, :]
embeddings = prepare_and_fit_minirocket_with_pca(embeddings)

# Train-test split
X_train, X_test, y_train, y_test = train_test_split(
    embeddings, labels, test_size=0.15, random_state=42, stratify=labels
)

# Initialize MiniRocketClassifier
classifier = MiniRocketClassifier()

# Fit classifier
classifier.fit(X_train, y_train)

# Predict on test set
y_pred = classifier.predict(X_test)

# Evaluate model
accuracy = accuracy_score(y_test, y_pred)
report = classification_report(y_test, y_pred)

# Print evaluation results
print(f"Accuracy: {accuracy:.4f}")
print("Classification Report:")
print(report)
