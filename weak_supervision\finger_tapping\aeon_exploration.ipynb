{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["#read labels\n", "import pandas as pd\n", "import os\n", "from scipy.signal import savgol_filter  \n", "import numpy as np\n", "from tqdm import tqdm  # Import tqdm for progress display\n", "from tensorflow.keras.preprocessing.sequence import pad_sequences\n", "#create y\n", "labels_path = r'\\\\files.ubc.ca\\team\\PPRC\\CAMERA\\Booth_Processed\\finger_tapping\\docs\\weak_supervision_final.csv'\n", "df_labels = pd.read_csv(labels_path)\n", "df_labels.set_index('ID',inplace=True)\n", "y = df_labels['snorkel_label_final']\n", "#kip first duplicate\n", "y = y[~y.index.duplicated(keep='first')]\n", "y.dropna(inplace=True)\n", "y_binary = y>0\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Processing \\\\files.ubc.ca\\team\\PPRC\\Camera\\Booth_Processed\\finger_tapping\\right\\distances: 100%|██████████| 238/238 [00:22<00:00, 10.75file/s]\n", "Processing \\\\files.ubc.ca\\team\\PPRC\\Camera\\Booth_Processed\\finger_tapping\\left\\distances: 100%|██████████| 234/234 [00:18<00:00, 12.36file/s]\n"]}], "source": ["#read labels\n", "import pandas as pd\n", "import os\n", "import numpy as np\n", "from tqdm import tqdm  # Import tqdm for progress display\n", "from tensorflow.keras.preprocessing.sequence import pad_sequences\n", "\n", "\n", "\n", "#create x\n", "tasks=['right', 'left']\n", "\n", "df_combined = pd.DataFrame(columns=['values', 'ID'])\n", "df_combined['ID'] = y.index\n", "df_combined.set_index('ID', inplace=True)\n", "\n", "for task in tasks:\n", "    file =  rf'\\\\files.ubc.ca\\team\\PPRC\\Camera\\Booth_Processed\\finger_tapping\\{task}\\distances'\n", "    segments_file = rf'\\\\files.ubc.ca\\team\\PPRC\\Camera\\Booth_Processed\\finger_tapping\\{task}\\csvs\\{task}_best_segments_and_indexes.csv'\n", "    segments_file = pd.read_csv(segments_file).set_index('file_name')\n", "    \n", "    for root, dirs, filenames in os.walk(file):\n", "        # Initialize progress bar\n", "        filenames = [f for f in filenames if f.endswith('.csv')]\n", "        with tqdm(total=len(filenames), desc=f\"Processing {file}\", unit=\"file\") as pbar:\n", "            for filename in filenames:\n", "                file_path = os.path.join(root, filename)\n", "                distances = pd.read_csv(file_path)[['Frame', 'Finger Distance', 'Finger Normalized Distance', 'Angular Distance', 'Wrist Coordinate']]\n", "                if len(distances) < 5:\n", "                    print(f\"File {file} has less than 5 frames. Skipping...\")\n", "                    pbar.update(1)\n", "                    continue\n", "                \n", "                #filter based on segments\n", "                # if filename in segments_file.index:\n", "                #     start,end,_ = segments_file.loc[filename].values\n", "                #     distances = distances[(distances['Frame'] >= start) & (distances['Frame'] <= end)]\n", "                    \n", "                    \n", "                normalized_distance = distances['Finger Normalized Distance']\n", "                file_id = filename.split('_finger')[0]\n", "                df_combined.loc[file_id, 'values'] = normalized_distance.to_numpy()\n", "                pbar.update(1)\n", "                \n", "      "]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(455, 450) (455,)\n"]}], "source": ["from tensorflow.keras.preprocessing.sequence import pad_sequences\n", "\n", "\n", "#shuffle df_vcombined \n", "df_combined_shuffled = df_combined.copy().sample(frac=1, random_state=42)\n", "# Select valid IDs\n", "valid_ids = df_combined_shuffled['values'].dropna().index.intersection(y_binary.index)\n", "\n", "# Extract sequences\n", "sequences = df_combined_shuffled.loc[valid_ids, 'values'].tolist()\n", "\n", "# Pad or truncate sequences to equal length\n", "X_padded = pad_sequences(sequences, maxlen=450, dtype='float32', padding='post', truncating='post')\n", "\n", "\n", "# Expand dims to match required shape: (samples, series_length, 1)\n", "X = X_padded\n", "y_binary = y_binary.loc[valid_ids]\n", "y = y.loc[valid_ids]\n", "\n", "\n", "\n", "# Check shapes\n", "print(X.shape, y_binary.shape)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Binary Classifier"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": []}], "source": ["from sklearn.model_selection import train_test_split\n", "from aeon.classification.deep_learning import TimeCNNClassifier\n", "from sklearn.metrics import classification_report, confusion_matrix\n", "from tensorflow.keras.preprocessing.sequence import pad_sequences\n", "\n", "# Aeon classifiers\n", "from aeon.classification.convolution_based import (\n", "    MiniRocketClassifier,\n", "    RocketClassifier,\n", "    MultiRocketClassifier,\n", ")\n", "from aeon.classification.deep_learning import (\n", "    InceptionTimeClassifier,\n", "    ResNetClassifier,\n", "    FCNClassifier,\n", ")\n", "from aeon.classification.dictionary_based import (\n", "    BOSSEnsemble,\n", "    WEASEL,\n", "    TemporalDictionaryEnsemble,\n", ")\n", "from aeon.classification.distance_based import (\n", "    KNeighborsTimeSeriesClassifier,\n", "    ElasticEnsemble,\n", "    ProximityForest,\n", ")\n", "from aeon.classification.shapelet_based import ShapeletTransformClassifier\n", "from aeon.classification.feature_based import Catch22Classifier\n", "\n", "X_train, X_test, y_train, y_test = train_test_split(X, y_binary, test_size=0.15, random_state=42, stratify=y_binary)\n", "# Dictionary of classifiers\n", "classifiers = {\n", "     \"MiniRocket\": MiniRocketClassifier(),#0.88\n", "    # \"Rocket\": RocketClassifier(),0.8\n", "    # \"MultiRocket\": MultiRocketClassifier(),0.8\n", "    # \"InceptionTime\": InceptionTimeClassifier(),\n", "    # \"ResNet\": ResNetClassifier(),\n", "    # \"FCN\": FCNClassifier(),\n", "    # \"BOSSEnsemble\": BOSSEnsemble(),#0.77\n", "    # \"WEASEL\": WEASEL(),#0.86\n", "    # \"TemporalDictionaryEnsemble\": TemporalDictionaryEnsemble(),#0.81\n", "    # \"KNeighbors\": KNeighborsTimeSeriesClassifier(),#0.75\n", "    #\"ElasticEnsemble\": ElasticEnsemble(),\n", "    #\"ProximityForest\": ProximityForest(),\n", "    #\"ShapeletTransform\": ShapeletTransformClassifier(),\n", "    #\"Catch22Classifier\": Catch22Classifier(),#0.84\n", "}\n", "\n", "# Evaluate each classifier\n", "for name, clf in classifiers.items():\n", "    print(f'\\n=== Training {name} ===')\n", "    clf.fit(X_train, y_train)\n", "    y_pred = clf.predict(X_test)\n", "    print(f'Classification Report for {name}:\\n')\n", "    print(classification_report(y_test, y_pred))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Multi Class Classifier and regression"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": []}], "source": ["# test all classifiers\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.model_selection import train_test_split\n", "from aeon.classification.deep_learning import TimeCNNClassifier\n", "from sklearn.metrics import classification_report, confusion_matrix\n", "from tensorflow.keras.preprocessing.sequence import pad_sequences\n", "\n", "# Aeon classifiers\n", "from aeon.classification.convolution_based import (\n", "    MiniRocketClassifier,\n", "    RocketClassifier,\n", "    MultiRocketClassifier,\n", ")\n", "from aeon.classification.deep_learning import (\n", "    InceptionTimeClassifier,\n", "    ResNetClassifier,\n", "    FCNClassifier,\n", ")\n", "from aeon.classification.dictionary_based import (\n", "    BOSSEnsemble,\n", "    WEASEL,\n", "    TemporalDictionaryEnsemble,\n", ")\n", "from aeon.classification.distance_based import (\n", "    KNeighborsTimeSeriesClassifier,\n", "    ElasticEnsemble,\n", "    ProximityForest,\n", ")\n", "from aeon.classification.shapelet_based import ShapeletTransformClassifier\n", "from aeon.classification.feature_based import Catch22Classifier\n", "\n", "y[y==4]=3\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.15, random_state=22, stratify=y_binary)\n", "# Dictionary of classifiers\n", "classifiers = {\n", "    \"MiniRocket\": MiniRocketClassifier(),\n", "    \"Rocket\": RocketClassifier(),\n", "    \"MultiRocket\": MultiRocketClassifier(),\n", "    #\"InceptionTime\": InceptionTimeClassifier(),\n", "    #\"ResNet\": ResNetClassifier(),\n", "    #\"FCN\": FCNClassifier(),\n", "    #\"BOSSEnsemble\": BOSSEnsemble(),\n", "    #\"WEASEL\": WEASEL(),\n", "    #\"TemporalDictionaryEnsemble\": TemporalDictionaryEnsemble(),\n", "    #\"KNeighbors\": KNeighborsTimeSeriesClassifier(),\n", "    #\"ElasticEnsemble\": ElasticEnsemble(),\n", "    #\"ProximityForest\": ProximityForest(),\n", "    #\"ShapeletTransform\": ShapeletTransformClassifier(),\n", "    #\"Catch22Classifier\": Catch22Classifier(),\n", "}\n", "\n", "# Evaluate each classifier\n", "for name, clf in classifiers.items():\n", "    print(f'\\n=== Training {name} ===')\n", "    clf.fit(X_train, y_train)\n", "    y_pred = clf.predict(X_test)\n", "    print(f'Classification Report for {name}:\\n')\n", "    print(classification_report(y_test, y_pred))\n", "    #show confusion matrix\n", "    cm = confusion_matrix(y_test, y_pred)\n", "    print(f'Confusion Matrix for {name}:\\n')\n", "    print(cm)"]}, {"cell_type": "code", "execution_count": 88, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": []}, {"name": "stdout", "output_type": "stream", "text": []}], "source": ["#labels Analysis\n", "\n", "df_experts = df_labels[['KW','MG','SA','WM']]\n", "df_experts.dropna(how='all',inplace=True)\n", "\n", "df_experts['number_of_experts'] = df_experts.notnull().sum(axis=1)\n", "df_experts['majority_vote'] = df_experts[['KW','MG','SA','WM']].mode(axis=1)[0]\n", "df_experts = df_experts[df_experts['number_of_experts']>1]\n", "\n", "y = df_experts['majority_vote']\n", "y_binary = y>0\n", "\n", "# Select valid IDs\n", "valid_ids = df_combined['values'].dropna().index.intersection(y_binary.index)\n", "\n", "\n", "# Extract sequences\n", "sequences = df_combined.loc[valid_ids, 'values'].tolist()\n", "\n", "# Pad or truncate sequences to equal length\n", "X_padded = pad_sequences(sequences, maxlen=500, dtype='float32', padding='post', truncating='post')\n", "\n", "\n", "# Expand dims to match required shape: (samples, series_length, 1)\n", "X = X_padded\n", "y_binary = y_binary.loc[valid_ids]\n", "y = y.loc[valid_ids]\n", "\n", "# Check shapes\n", "print(X.shape, y_binary.shape)\n", "\n"]}, {"cell_type": "code", "execution_count": 265, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": []}], "source": ["#Add diff and diff2\n", "from sklearn.model_selection import StratifiedKFold\n", "from sklearn.metrics import classification_report, confusion_matrix\n", "from aeon.classification.convolution_based import MiniRocketClassifier, MultiRocketHydraClassifier,RocketClassifier\n", "import numpy as np\n", "from scipy.ndimage import gaussian_filter1d\n", "from scipy.signal import savgol_filter  \n", "# Time series augmentation library\n", "from tsaug import TimeWarp, AddNoise, Drift, Crop\n", "\n", "def smooth_signal(signal, window_length=20, polyorder=2):\n", "    if len(signal) < window_length:\n", "        return signal\n", "    return savgol_filter(signal, window_length=window_length, polyorder=polyorder)\n", "\n", "# Define a function to calculate differences and apply Gaussian smoothing\n", "def calculate_diffs_and_smooth(X):\n", "    n_samples, series_length = X.shape\n", "\n", "    # Smooth original\n", "    X_smooth = smooth_signal(X)\n", "\n", "    # First difference (same length using prepend)\n", "    X_diff = np.diff(X, axis=1, prepend=X[:, [0]])\n", "    X_diff_smooth = gaussian_filter1d(X_diff, sigma=1, axis=1)\n", "\n", "    # Second difference (same length using prepend again)\n", "    X_diff2 = np.diff(X_diff, axis=1, prepend=X_diff[:, [0]])\n", "    X_diff2_smooth = gaussian_filter1d(X_diff2, sigma=1, axis=1)\n", "    \n", "    #add fourier transform\n", "    X_fft = np.fft.fft(X, axis=1)\n", "    X_fft = np.abs(X_fft)  # Take the magnitude of the FFT\n", "    X_fft_smooth = gaussian_filter1d(X_fft, sigma=1, axis=1)\n", "    #return X\n", "    return np.stack([X, X_fft], axis=1)\n", "\n", "# Assuming X and y are already defined and processed as per your initial setup\n", "\n", "# Perform class label adjustment and filtering (as per your original code)\n", "y = np.where(y == 4, 3, y)  # Change label 4 to 3\n", "mask = (y == 0) | (y == 1) | (y == 2) | (y == 3)\n", "y_np_comp = y[mask]\n", "X_comp = X[mask]\n", "\n", "# Initialize StratifiedKFold for cross-validation\n", "kf = StratifiedKFold(n_splits=5, shuffle=True, random_state=13)\n", "scores = []\n", "maes = []\n", "\n", "print(\"\\n=== Manual Cross-Validation for MiniRocketClassifier + Augmentation ===\")\n", "\n", "# Iterate over folds\n", "for fold, (train_index, test_index) in enumerate(kf.split(X_comp, y_np_comp), 1):\n", "    X_train, X_test = X_comp[train_index], X_comp[test_index]\n", "    y_train, y_test = y_np_comp[train_index], y_np_comp[test_index]\n", "\n", "    # Preprocess training and testing data with differences and smoothing\n", "    X_train_augmented = calculate_diffs_and_smooth(X_train)\n", "    X_test_augmented = calculate_diffs_and_smooth(X_test)\n", "\n", "    # Train classifier\n", "    clf = MiniRocketClassifier(random_state=42,n_kernels=15000)\n", "    clf.fit(X_train_augmented, y_train)\n", "    y_pred = clf.predict(X_test_augmented)\n", "\n", "    print(f\"\\n--- Fold {fold} ---\")\n", "    #print(classification_report(y_test, y_pred))\n", "    cm = confusion_matrix(y_test, y_pred)\n", "    print(f\"Confusion Matrix:\\n{cm}\")\n", "\n", "    accuracy = np.mean(y_pred == y_test)\n", "    scores.append(accuracy)\n", "    #print MAE and MSE\n", "    print(f\"Mean Absolute Error: {np.mean(np.abs(y_pred - y_test)):.4f}\")\n", "    print(f\"Mean Squared Error: {np.mean((y_pred - y_test)**2):.4f}\")\n", "    maes.append(np.mean(np.abs(y_pred - y_test)))\n", "    \n", "    #print ids that diff is two or more\n", "    diff = np.abs(y_pred - y_test)\n", "    diff_ids = np.where(diff >= 2)[0]\n", "    if len(diff_ids) > 0:\n", "        user_id = df_combined.iloc[test_index[diff_ids]].index\n", "        print(f\"IDs with differences of 2 or more: {user_id.tolist()}\")\n", "\n", "print(f\"\\nCross-Validation Scores: {scores}\")\n", "print(f\"Mean Accuracy: {np.mean(scores):.4f}\")\n", "print(f\"Mean Absolute Error: {np.mean(maes):.4f}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": []}], "source": ["#Add diff and diff2\n", "from sklearn.model_selection import StratifiedKFold\n", "from sklearn.metrics import classification_report, confusion_matrix\n", "from aeon.classification.convolution_based import MiniRocketClassifier, MultiRocketHydraClassifier,RocketClassifier\n", "import numpy as np\n", "from scipy.ndimage import gaussian_filter1d\n", "from scipy.signal import savgol_filter  \n", "# Time series augmentation library\n", "from tsaug import TimeWarp, AddNoise, Drift, Crop\n", "\n", "def smooth_signal(signal, window_length=20, polyorder=2):\n", "    if len(signal) < window_length:\n", "        return signal\n", "    return savgol_filter(signal, window_length=window_length, polyorder=polyorder)\n", "\n", "# Define a function to calculate differences and apply Gaussian smoothing\n", "def calculate_diffs_and_smooth(X):\n", "    n_samples, series_length = X.shape\n", "\n", "    # Smooth original\n", "    X_smooth = smooth_signal(X)\n", "\n", "    # First difference (same length using prepend)\n", "    X_diff = np.diff(X, axis=1, prepend=X[:, [0]])\n", "    X_diff_smooth = gaussian_filter1d(X_diff, sigma=1, axis=1)\n", "\n", "    # Second difference (same length using prepend again)\n", "    X_diff2 = np.diff(X_diff, axis=1, prepend=X_diff[:, [0]])\n", "    X_diff2_smooth = gaussian_filter1d(X_diff2, sigma=1, axis=1)\n", "    \n", "    #return X\n", "    return np.stack([X, X_diff], axis=1)\n", "\n", "# Assuming X and y are already defined and processed as per your initial setup\n", "\n", "# Perform class label adjustment and filtering (as per your original code)\n", "y = np.where(y == 4, 3, y)  # Change label 4 to 3\n", "mask = (y == 0) | (y == 1) | (y == 2) | (y == 3)\n", "y_np_comp = y[mask]\n", "X_comp = X[mask]\n", "\n", "# Initialize StratifiedKFold for cross-validation\n", "kf = StratifiedKFold(n_splits=8, shuffle=True, random_state=10)\n", "scores = []\n", "maes = []\n", "\n", "print(\"\\n=== Manual Cross-Validation for MiniRocketClassifier + Augmentation ===\")\n", "\n", "# Iterate over folds\n", "for fold, (train_index, test_index) in enumerate(kf.split(X_comp, y_np_comp), 1):\n", "    X_train, X_test = X_comp[train_index], X_comp[test_index]\n", "    y_train, y_test = y_np_comp[train_index], y_np_comp[test_index]\n", "\n", "    # Preprocess training and testing data with differences and smoothing\n", "    X_train_augmented = calculate_diffs_and_smooth(X_train)\n", "    X_test_augmented = calculate_diffs_and_smooth(X_test)\n", "\n", "    # Train classifier\n", "    clf = MiniRocketClassifier(random_state=42,n_kernels=12000)\n", "    clf.fit(X_train_augmented, y_train)\n", "    y_pred = clf.predict(X_test_augmented)\n", "\n", "    print(f\"\\n--- Fold {fold} ---\")\n", "    #print(classification_report(y_test, y_pred))\n", "    cm = confusion_matrix(y_test, y_pred)\n", "    print(f\"Confusion Matrix:\\n{cm}\")\n", "\n", "    accuracy = np.mean(y_pred == y_test)\n", "    scores.append(accuracy)\n", "    #print MAE and MSE\n", "    print(f\"Mean Absolute Error: {np.mean(np.abs(y_pred - y_test)):.4f}\")\n", "    print(f\"Mean Squared Error: {np.mean((y_pred - y_test)**2):.4f}\")\n", "    maes.append(np.mean(np.abs(y_pred - y_test)))\n", "\n", "print(f\"\\nCross-Validation Scores: {scores}\")\n", "print(f\"Mean Accuracy: {np.mean(scores):.4f}\")\n", "print(f\"Mean Absolute Error: {np.mean(maes):.4f}\")\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["              precision    recall  f1-score   support\n", "\n", "           0       0.58      0.64      0.61        11\n", "           1       0.62      0.44      0.52        18\n", "           2       0.82      0.75      0.78        12\n", "           3       0.59      0.83      0.69        12\n", "\n", "    accuracy                           0.64        53\n", "   macro avg       0.65      0.67      0.65        53\n", "weighted avg       0.65      0.64      0.63        53\n", "\n", "Confusion Matrix:\n", " [[ 7  3  0  1]\n", " [ 5  8  1  4]\n", " [ 0  1  9  2]\n", " [ 0  1  1 10]]\n", "Accuracy: 0.6415\n", "MAE: 0.4906\n", "MSE: 0.7925\n", "\n", "Large mismatches (2 or more levels):\n", "Predicted: [3 3 3 1 3 3]\n", "True     : ID\n", "24622_20230530_right    0\n", "15813_20240123_right    1\n", "33151_20231016_right    1\n", "17434_20230613_left     3\n", "30893_20230717_right    1\n", "18198_20230726_left     1\n", "Name: snorkel_label_final, dtype: int64\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_12964\\1743703499.py:75: FutureWarning: Series.__getitem__ treating keys as positions is deprecated. In a future version, integer keys will always be treated as labels (consistent with DataFrame behavior). To access a value by position, use `ser.iloc[pos]`\n", "  print(\"True     :\", y_test[large_errors])\n"]}], "source": ["import numpy as np\n", "import pandas as pd\n", "from sklearn.metrics import classification_report, confusion_matrix, mean_absolute_error, mean_squared_error\n", "from aeon.classification.convolution_based import MiniRocketClassifier\n", "from scipy.ndimage import gaussian_filter1d\n", "from scipy.signal import savgol_filter\n", "\n", "# -------------------------\n", "# Preprocessing Functions\n", "# -------------------------\n", "\n", "def smooth_signal(signal, window_length=20, polyorder=2):\n", "    \"\"\"Apply <PERSON><PERSON><PERSON> smoothing to a 1D signal.\"\"\"\n", "    if len(signal) < window_length:\n", "        return signal\n", "    return savgol_filter(signal, window_length=window_length, polyorder=polyorder)\n", "\n", "def calculate_diffs_and_smooth(X):\n", "    \"\"\"Compute first/second differences and FFT, apply smoothing, and stack them.\"\"\"\n", "    X_diff = np.diff(X, axis=1, prepend=X[:, [0]])\n", "    X_diff2 = np.diff(X_diff, axis=1, prepend=X_diff[:, [0]])\n", "    X_fft = np.abs(np.fft.fft(X, axis=1))\n", "\n", "    # Apply smoothing\n", "    X_diff_smooth = gaussian_filter1d(X_diff, sigma=1, axis=1)\n", "    X_diff2_smooth = gaussian_filter1d(X_diff2, sigma=1, axis=1)\n", "    X_fft_smooth = gaussian_filter1d(X_fft, sigma=1, axis=1)\n", "\n", "    return np.stack([X,X_fft,X_diff_smooth], axis=1)\n", "\n", "# -------------------------\n", "# Load and Prepare Data\n", "# -------------------------\n", "\n", "# Load test set file names\n", "test_set_path = r'\\\\files.ubc.ca\\team\\PPRC\\Camera\\Booth_Processed\\finger_tapping\\docs\\test-set-balanced.csv'\n", "test_file_names = pd.read_csv(test_set_path)['file_name']\n", "\n", "# Create mask for test and train split\n", "test_mask = valid_ids.isin(test_file_names)\n", "y[y == 4] = 3  # Merge class 4 into class 3\n", "\n", "X_train, y_train = X[~test_mask], y[~test_mask]\n", "X_test, y_test = X[test_mask], y[test_mask]\n", "\n", "# Preprocess signals\n", "X_train_aug = calculate_diffs_and_smooth(X_train)\n", "X_test_aug = calculate_diffs_and_smooth(X_test)\n", "\n", "# -------------------------\n", "# Train and Evaluate Classifier\n", "# -------------------------\n", "\n", "clf = MiniRocketClassifier(random_state=42, n_kernels=10000)\n", "clf.fit(X_train_aug, y_train)\n", "y_pred = clf.predict(X_test_aug)\n", "\n", "# Evaluation\n", "print(classification_report(y_test, y_pred))\n", "print(\"Confusion Matrix:\\n\", confusion_matrix(y_test, y_pred))\n", "print(f\"Accuracy: {np.mean(y_pred == y_test):.4f}\")\n", "print(f\"MAE: {mean_absolute_error(y_test, y_pred):.4f}\")\n", "print(f\"MSE: {mean_squared_error(y_test, y_pred):.4f}\")\n", "\n", "# -------------------------\n", "# Show Large Mismatches\n", "# -------------------------\n", "\n", "diff = np.abs(y_pred - y_test)\n", "large_errors = np.where(diff >= 2)[0]\n", "\n", "if len(large_errors) > 0:\n", "    print(\"\\nLarge mismatches (2 or more levels):\")\n", "    print(\"Predicted:\", y_pred[large_errors])\n", "    print(\"True     :\", y_test[large_errors])\n"]}, {"cell_type": "code", "execution_count": 91, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": []}], "source": ["from aeon.transformations.collection.convolution_based import MiniRocket\n", "from sklearn.linear_model import RidgeClassifierCV\n", "from sklearn.pipeline import make_pipeline\n", "\n", "# 1. Instantiate the MiniRocket transformer\n", "transformer = MiniRocket(random_state=42,n_kernels=8000)\n", "\n", "# 2. Fit the transformer to the training data\n", "transformer.fit(X_train_augmented)\n", "\n", "# 3. Transform both train and test sets\n", "X_train_features = transformer.transform(X_train_augmented)\n", "X_test_features = transformer.transform(X_test_augmented)\n", "\n", "# 4. Train a classifier on the features\n", "clf = RidgeClassifierCV(\n", "    class_weight='balanced',        # handle class imbalance\n", "    cv=10,                            # k-fold cross-validation\n", "    scoring='neg_mean_absolute_error',             # metric for model selection\n", ")\n", "clf.fit(X_train_features, y_train)\n", "\n", "# 5. Predict\n", "y_pred = clf.predict(X_test_features)\n", "\n", "# 6. <PERSON><PERSON><PERSON>\n", "print(classification_report(y_test, y_pred))\n", "print(f\"Confusion Matrix:\\n{confusion_matrix(y_test, y_pred)}\")\n", "print(f\"Accuracy: {np.mean(y_pred == y_test):.4f}\")\n", "print(f\"MAE: {np.mean(np.abs(y_pred - y_test)):.4f}\")\n", "print(f\"MSE: {np.mean((y_pred - y_test)**2):.4f}\")\n", "\n", "# 7. Access and inspect features\n", "print(\"Shape of Rocket features:\", X_test_features.shape)\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 75, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": []}], "source": ["from aeon.classification.interval_based import TimeSeriesForestClassifier\n", "from sklearn.metrics import classification_report, confusion_matrix\n", "import numpy as np\n", "\n", "# 1. Instantiate and train the Time Series Forest model\n", "clf = TimeSeriesForestClassifier(n_estimators=3000, random_state=42)\n", "# Fit the model on the augmented training data\n", "clf.fit(X_train_augmented, y_train)\n", "\n", "# 2. Predict on the test set\n", "y_pred = clf.predict(X_test_augmented)\n", "\n", "# 3. Evaluation\n", "print(classification_report(y_test, y_pred))\n", "print(\"Confusion Matrix:\\n\", confusion_matrix(y_test, y_pred))\n", "print(f\"Accuracy: {np.mean(y_pred == y_test):.4f}\")\n", "print(f\"MAE: {np.mean(np.abs(y_pred - y_test)):.4f}\")\n", "print(f\"MSE: {np.mean((y_pred - y_test)**2):.4f}\")\n"]}, {"cell_type": "code", "execution_count": 68, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": []}, {"name": "stdout", "output_type": "stream", "text": []}], "source": ["from sklearn.manifold import TSNE\n", "import matplotlib.pyplot as plt\n", "\n", "# 8. t-SNE visualization of the test features\n", "tsne = TSNE(n_components=2, random_state=42, perplexity=20, n_iter=3000)\n", "X_test_embedded = tsne.fit_transform(X_test_features)\n", "\n", "# 9. Plot\n", "plt.figure(figsize=(8, 6))\n", "scatter = plt.scatter(X_test_embedded[:, 0], X_test_embedded[:, 1], c=y_test, cmap='tab10', alpha=0.8)\n", "plt.title(\"t-SNE of MiniRocket Features (Test Set)\")\n", "plt.xlabel(\"Component 1\")\n", "plt.ylabel(\"Component 2\")\n", "plt.colorbar(scatter, label='True Label')\n", "plt.grid(True)\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 153, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": []}], "source": ["# 3classifier\n", "from sklearn.model_selection import StratifiedKFold\n", "from sklearn.metrics import classification_report, confusion_matrix\n", "from aeon.classification.convolution_based import MultiRocketClassifier\n", "import numpy as np\n", "from scipy.stats import mode\n", "\n", "# Select only samples from classes 1, 2, and 3\n", "mask = (y == 1) | (y == 2) | (y == 3)\n", "y_np_comp = y[mask]\n", "X_comp = X[mask]\n", "\n", "kf = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)\n", "scores = []\n", "\n", "print(\"\\n=== Cross-Validation with 3 Binary Classifiers + Majority Voting ===\")\n", "\n", "for fold, (train_index, test_index) in enumerate(kf.split(X_comp, y_np_comp), 1):\n", "    X_train, X_test = X_comp[train_index], X_comp[test_index]\n", "    y_train, y_test = y_np_comp[train_index], y_np_comp[test_index]\n", "\n", "    # Prepare data for each binary classification\n", "    def prepare_binary(X, y, class_a, class_b):\n", "        mask = (y == class_a) | (y == class_b)\n", "        X_bin = X[mask]\n", "        y_bin = y[mask]\n", "        y_bin = np.where(y_bin == class_a, 0, 1)\n", "        return X_bin, y_bin\n", "\n", "    # 1 vs 3\n", "    X_13, y_13 = prepare_binary(X_train, y_train, 1, 3)\n", "    clf_13 = MultiRocketClassifier(random_state=42, n_jobs=8, class_weight='balanced')\n", "    clf_13.fit(X_13, y_13)\n", "\n", "    # 2 vs 3\n", "    X_23, y_23 = prepare_binary(X_train, y_train, 2, 3)\n", "    clf_23 = MultiRocketClassifier(random_state=42, n_jobs=8, class_weight='balanced')\n", "    clf_23.fit(X_23, y_23)\n", "\n", "    # 1 vs 2\n", "    X_12, y_12 = prepare_binary(X_train, y_train, 1, 2)\n", "    clf_12 = MultiRocketClassifier(random_state=42, n_jobs=8, class_weight='balanced')\n", "    clf_12.fit(X_12, y_12)\n", "\n", "    # Predict with each classifier on the full test set\n", "    def binary_predict(clf, class_a, class_b, X_test):\n", "        pred_bin = clf.predict(X_test)\n", "        return np.where(pred_bin == 0, class_a, class_b)\n", "\n", "    pred_13 = binary_predict(clf_13, 1, 3, X_test)\n", "    pred_23 = binary_predict(clf_23, 2, 3, X_test)\n", "    pred_12 = binary_predict(clf_12, 1, 2, X_test)\n", "\n", "    # Majority voting\n", "    preds = np.vstack([pred_13, pred_23, pred_12])\n", "    y_pred_majority = mode(preds, axis=0).mode\n", "\n", "    # Evaluation\n", "    print(f\"\\n--- Fold {fold} ---\")\n", "    print(classification_report(y_test, y_pred_majority))\n", "    cm = confusion_matrix(y_test, y_pred_majority)\n", "    print(f\"Confusion Matrix:\\n{cm}\")\n", "\n", "    accuracy = np.mean(y_pred_majority == y_test)\n", "    scores.append(accuracy)\n", "\n", "print(f\"\\nCross-Validation Scores: {scores}\")\n", "print(f\"Mean Accuracy: {np.mean(scores):.4f}\")\n"]}, {"cell_type": "code", "execution_count": 179, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": []}], "source": ["#regression\n", "from aeon.regression.convolution_based import MiniRocketRegressor,MultiRocketRegressor\n", "from sklearn.model_selection import StratifiedKFold\n", "from sklearn.metrics import mean_squared_error, classification_report, confusion_matrix\n", "import numpy as np\n", "\n", "#Add diff and diff2\n", "from sklearn.model_selection import StratifiedKFold\n", "from sklearn.metrics import classification_report, confusion_matrix\n", "from aeon.classification.convolution_based import MiniRocketClassifier, MultiRocketHydraClassifier,RocketClassifier\n", "import numpy as np\n", "from scipy.ndimage import gaussian_filter1d\n", "from scipy.signal import savgol_filter  \n", "# Time series augmentation library\n", "from tsaug import TimeWarp, AddNoise, Drift, Crop\n", "\n", "# Define a function to calculate differences and apply Gaussian smoothing\n", "def calculate_diffs_and_smooth(X):\n", "    n_samples, series_length = X.shape\n", "\n", "    # Smooth original\n", "    X_smooth = smooth_signal(X)\n", "\n", "    # First difference (same length using prepend)\n", "    X_diff = np.diff(X, axis=1, prepend=X[:, [0]])\n", "    X_diff_smooth = gaussian_filter1d(X_diff, sigma=1, axis=1)\n", "\n", "    # Second difference (same length using prepend again)\n", "    X_diff2 = np.diff(X_diff, axis=1, prepend=X_diff[:, [0]])\n", "    X_diff2_smooth = gaussian_filter1d(X_diff2, sigma=1, axis=1)\n", "    \n", "    #return X\n", "    return np.stack([X, X_diff], axis=1)\n", "\n", "# Assuming X and y are already defined and processed as per your initial setup\n", "\n", "# Perform class label adjustment and filtering (as per your original code)\n", "y = np.where(y == 4, 3, y)  # Change label 4 to 3\n", "mask = (y == 0) | (y == 1) | (y == 2) | (y == 3)\n", "y_np_comp = y[mask]\n", "X_comp = X[mask]\n", "\n", "# Initialize StratifiedKFold for cross-validation\n", "kf = StratifiedKFold(n_splits=8, shuffle=True, random_state=10)\n", "accuracy_scores = []\n", "mse_scores = []\n", "mae_scores = [] \n", "\n", "\n", "print(\"\\n=== Manual Cross-Validation for MiniRocketRegressor + Augmentation ===\")\n", "\n", "# Iterate over folds\n", "for fold, (train_index, test_index) in enumerate(kf.split(X_comp, y_np_comp), 1):\n", "    X_train, X_test = X_comp[train_index], X_comp[test_index]\n", "    y_train, y_test = y_np_comp[train_index], y_np_comp[test_index]\n", "\n", "    # Preprocess training and testing data with differences and smoothing\n", "    X_train_augmented = calculate_diffs_and_smooth(X_train)\n", "    X_test_augmented = calculate_diffs_and_smooth(X_test)\n", "\n", "    \n", "    reg = MiniRocketRegressor(random_state=42)\n", "    reg.fit(X_train, y_train)\n", "    y_pred_reg = reg.predict(X_test)\n", "\n", "    # Calculate MSE\n", "    mse = mean_squared_error(y_test, y_pred_reg)\n", "    mse_scores.append(mse)\n", "    \n", "    # Calculate MAE\n", "    mae = np.mean(np.abs(y_pred_reg - y_test))\n", "    mae_scores.append(mae)\n", "    \n", "\n", "    # Convert regression output to class by rounding\n", "    y_pred_class = np.round(y_pred_reg).astype(int)\n", "\n", "    # Classification metrics\n", "    accuracy = np.mean(y_pred_class == y_test)\n", "    accuracy_scores.append(accuracy)\n", "\n", "    print(f\"\\n--- Fold {fold} ---\")\n", "    print(f\"MSE: {mse:.4f}\")\n", "    print(f\"Accuracy (rounded): {accuracy:.4f}\")\n", "    print(\"Classification Report:\")\n", "    print(classification_report(y_test, y_pred_class))\n", "    print(\"Confusion Matrix:\")\n", "    print(confusion_matrix(y_test, y_pred_class))\n", "\n", "print(\"\\n=== Summary ===\")\n", "print(f\"Mean MSE: {np.mean(mse_scores):.4f}\")\n", "print(f\"Mean Classification Accuracy: {np.mean(accuracy_scores):.4f}\")\n", "print(f\"Mean MAE: {np.mean(mae_scores):.4f}\")\n"]}, {"cell_type": "code", "execution_count": 77, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": []}, {"name": "stdout", "output_type": "stream", "text": []}, {"name": "stdout", "output_type": "stream", "text": []}, {"name": "stdout", "output_type": "stream", "text": []}, {"name": "stdout", "output_type": "stream", "text": []}, {"name": "stdout", "output_type": "stream", "text": []}, {"name": "stdout", "output_type": "stream", "text": []}, {"name": "stdout", "output_type": "stream", "text": []}, {"name": "stdout", "output_type": "stream", "text": []}, {"name": "stdout", "output_type": "stream", "text": []}, {"name": "stdout", "output_type": "stream", "text": []}], "source": ["#test parameters\n", "from aeon.classification.convolution_based import MiniRocketClassifier\n", "from sklearn.linear_model import RidgeClassifierCV, LogisticRegression\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.model_selection import StratifiedKFold\n", "from sklearn.metrics import classification_report, confusion_matrix\n", "import numpy as np\n", "\n", "# Merge class 4 into class 3\n", "y_np = np.where(y == 4, 3, y)\n", "\n", "# Classifiers to try\n", "estimators = {\n", "    \"RidgeClassifierCV\": RidgeClassifierCV(),\n", "    \"LogisticRegression\": LogisticRegression(max_iter=1000),\n", "    \"RandomForest\": RandomForestClassifier(n_estimators=100, random_state=42)\n", "}\n", "\n", "kf = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)\n", "\n", "for name, est in estimators.items():\n", "    print(f\"\\n=== Testing MiniRocketClassifier with {name} ===\")\n", "    scores = []\n", "\n", "    for fold, (train_index, test_index) in enumerate(kf.split(X, y_np), 1):\n", "        X_train, X_test = X[train_index], X[test_index]\n", "        y_train, y_test = y_np[train_index], y_np[test_index]\n", "\n", "        clf = MiniRocketClassifier(estimator=est)\n", "        clf.fit(X_train, y_train)\n", "        y_pred = clf.predict(X_test)\n", "\n", "        accuracy = np.mean(y_pred == y_test)\n", "        scores.append(accuracy)\n", "\n", "        print(f\"\\n--- Fold {fold} ---\")\n", "        print(f\"Accuracy: {accuracy:.4f}\")\n", "        print(\"Classification Report:\")\n", "        print(classification_report(y_test, y_pred))\n", "        print(\"Confusion Matrix:\")\n", "        print(confusion_matrix(y_test, y_pred))\n", "\n", "    print(f\"\\nMean Accuracy for {name}: {np.mean(scores):.4f}\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Using MAE"]}, {"cell_type": "code", "execution_count": 188, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": []}], "source": ["import pickle\n", "import numpy as np\n", "from aeon.classification.convolution_based import MiniRocketClassifier\n", "from sklearn.metrics import accuracy_score, classification_report\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.decomposition import PCA\n", "\n", "def prepare_and_fit_minirocket_with_pca(X, selected_landmarks=[4,8], pca_components=8):\n", "    \"\"\"\n", "    Prepare data, apply PCA on embeddings, and fit MiniRocketClassifier.\n", "\n", "    Parameters:\n", "        X: np.n<PERSON><PERSON> (shape: [samples, 1, series_length, landmarks, embedding_dim])\n", "        y: np.n<PERSON><PERSON> (shape: [samples, ])\n", "        selected_landmarks: landmarks indices to keep\n", "        pca_components: number of PCA dimensions (default: 2)\n", "\n", "    Returns:\n", "        classifier: trained MiniRocketClassifier model\n", "    \"\"\"\n", "\n", "    # Step 1: Remove redundant dimension\n", "    X = np.squeeze(X, axis=1)  # (300, 400, 21, 16)\n", "\n", "    # Step 2: Select specific landmarks\n", "    X = X[:, :, selected_landmarks, :]  # (300, 400, 3, 16)\n", "\n", "    # Step 3: Apply PCA on embeddings (last dimension)\n", "    samples, series_length, n_landmarks, embed_dim = X.shape\n", "    X_flat = X.reshape(-1, embed_dim)  # Combine samples, time, landmarks: (300*400*3, 16)\n", "\n", "    # PCA fit-transform\n", "    pca = PCA(n_components=pca_components)\n", "    X_pca = pca.fit_transform(X_flat)  # (300*400*3, 2)\n", "\n", "    # Restore original dimensions with reduced embeddings\n", "    X_pca = X_pca.reshape(samples, series_length, n_landmarks, pca_components)  # (300, 400, 3, 2)\n", "\n", "    # Step 4: Merge landmark and reduced embeddings into channels\n", "    X_final = X_pca.reshape(samples, series_length, n_landmarks * pca_components)  # (300, 400, 6)\n", "\n", "    # Transpose to (samples, channels, series_length)\n", "    X_final = np.transpose(X_final, (0, 2, 1))  # (300, 6, 400)\n", "\n", "\n", "    return X_final\n", "\n", "\n", "data = np.load(r\"G:\\My Drive\\Temporary Atefeh\\stmae_embeddings_pd_4.npy\",allow_pickle=True)\n", "embeddings = data[\"embeddings\"]\n", "labels = data[\"labels\"]\n", "indexes = data[\"indexes\"]\n", "labels[labels==4] = 3\n", "\n", "# Convert embeddings to format required by Aeon (n_samples, n_channels, series_length)\n", "# Currently, embeddings might be (n_samples, embedding_dim). \n", "# MiniRocket expects at least 3D input (samples, channels, length).\n", "# Here, we reshape assuming embeddings represent single-channel, and length equals embedding_dim.\n", "embeddings = embeddings[:, np.newaxis, :]\n", "embeddings = prepare_and_fit_minirocket_with_pca(embeddings)\n", "\n", "# Train-test split\n", "X_train, X_test, y_train, y_test = train_test_split(\n", "    embeddings, labels, test_size=0.15, random_state=42, stratify=labels\n", ")\n", "\n", "# Initialize MiniRocketClassifier\n", "classifier = MiniRocketClassifier()\n", "\n", "# Fit classifier\n", "classifier.fit(X_train, y_train)\n", "\n", "# Predict on test set\n", "y_pred = classifier.predict(X_test)\n", "\n", "# Evaluate model\n", "accuracy = accuracy_score(y_test, y_pred)\n", "report = classification_report(y_test, y_pred)\n", "\n", "# Print evaluation results\n", "print(f\"Accuracy: {accuracy:.4f}\")\n", "print(\"Classification Report:\")\n", "print(report)\n"]}], "metadata": {"kernelspec": {"display_name": "assessment", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.5"}}, "nbformat": 4, "nbformat_minor": 2}