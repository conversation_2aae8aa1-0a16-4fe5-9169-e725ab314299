import os
import sys
# Adjust the working directory to the project root
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.join(current_dir, '..')
sys.path.insert(0, project_root)
os.chdir(project_root)
import pdfkit

# Define paths for wkhtmltopdf
pdfkit_config = pdfkit.configuration(wkhtmltopdf=r'C:\Program Files\wkhtmltopdf\bin\wkhtmltopdf.exe')  # Adjust the path if needed

# Load your HTML content from the HTML file
with open(r"D:\Atefeh\Codes\Python\ParkinsonAssessment\report_generation\design\report.html") as f:
    html_content = f.read()

# Define the output path for the PDF
output_pdf = "finger_tapping_report.pdf"

# Convert HTML to PDF
pdfkit.from_string(html_content, output_pdf, configuration=pdfkit_config)

print(f"PDF report generated: {output_pdf}")