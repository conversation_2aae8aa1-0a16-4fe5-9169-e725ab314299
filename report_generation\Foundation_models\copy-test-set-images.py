#!/usr/bin/env python3"""
import os
import shutil
import pandas as pd
from pathlib import Path
import argparse
from typing import List
from config import BASE_PROCESSED_DIRECTORY, BASE_RESULTS_DIRECTORY
def copy_test_set_images(test_set_csv: str, source_dir: str, dest_dir: str) -> None:   
    """Copy images for users in the test set to destination directory.    
    Args:        test_set_csv: Path to test-set-balanced.csv
        source_dir: Directory containing source images        dest_dir: Destination directory for copied images
    """    
    # Read test set users
    test_df = pd.read_csv(test_set_csv)    
    test_users = test_df['file_name'].astype(str).tolist()
        # Create destination directory
    dest_path = Path(dest_dir)    
    dest_path.mkdir(parents=True, exist_ok=True)
        # Find and copy images for test users
    copied_count = 0    
    for file_name in test_users:
        user_id = file_name.split('_')[0]
        source_path = Path(source_dir) / user_id / 'finger_tapping' / 'images'        
        if not source_path.exists():
            continue            
        for img_file in source_path.glob('*full_chart*.png'):            
            dest_file = dest_path / f"{img_file.name}"
            shutil.copy2(img_file, dest_file)
            copied_count += 1            
            print(f"Copied: {img_file.name}")
        print(f"\nTotal images copied: {copied_count}")
def main():
    parser = argparse.ArgumentParser(        description="Copy test set images to a new directory"    )
        
    parser.add_argument(
        "--test-set",        default=r"G:\My Drive\Datasets\XAI\test-set-balanced.csv",
        help="Path to test-set-balanced.csv file"    )
       
    parser.add_argument(
        "--source-dir",        default=os.path.join(BASE_RESULTS_DIRECTORY, "reports", "users"),
        help="Source directory containing user images"    )
    parser.add_argument(
        "--dest-dir",        default=r'G:\My Drive\Datasets\XAI\images\test-set',
        help="Destination directory for test set images"    )
    args = parser.parse_args()
    
    print(f"Copying test set images from {args.source_dir} to {args.dest_dir}")    
    copy_test_set_images(args.test_set, args.source_dir, args.dest_dir)
if __name__ == "__main__":
    main()