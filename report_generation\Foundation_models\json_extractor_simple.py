#!/usr/bin/env python3
"""
Simple script to extract JSON from CSV response column and convert to CSV.
"""

import pandas as pd
import json
import re
import os


def find_json_in_text(text):
    """Find and extract JSON objects from text."""
    if not text or pd.isna(text):
        return []
    
    json_objects = []
    text = str(text)
    
    # Find all text between curly braces
    brace_count = 0
    start_pos = -1
    
    for i, char in enumerate(text):
        if char == '{':
            if brace_count == 0:
                start_pos = i
            brace_count += 1
        elif char == '}':
            brace_count -= 1
            if brace_count == 0 and start_pos != -1:
                # Found complete JSON object
                json_text = text[start_pos:i+1]
                try:
                    json_obj = json.loads(json_text)
                    json_objects.append(json_obj)
                except:
                    # Try to fix common issues
                    try:
                        # Fix single quotes and other issues
                        fixed_text = json_text.replace("'", '"')
                        fixed_text = re.sub(r'(\w+):', r'"\1":', fixed_text)
                        fixed_text = re.sub(r',(\s*[}\]])', r'\1', fixed_text)
                        json_obj = json.loads(fixed_text)
                        json_objects.append(json_obj)
                    except:
                        print(f"Failed to parse: {json_text[:100]}...")
                        continue
    
    return json_objects


def process_csv(input_path, output_path, response_column='response'):
    """Process CSV file and extract JSON to new CSV."""
    
    print(f"Reading: {input_path}")
    
    # Read CSV
    try:
        df = pd.read_csv(input_path, encoding='utf-8-sig')
        print(f"Loaded {len(df)} rows")
    except Exception as e:
        print(f"Error reading CSV: {e}")
        return
    
    # Check if response column exists
    if response_column not in df.columns:
        print(f"Column '{response_column}' not found!")
        print(f"Available columns: {list(df.columns)}")
        return
    
    # Extract JSON from each row
    all_records = []
    
    for idx, row in df.iterrows():
        response_text = row[response_column]
        
        print(f"Processing row {idx + 1}/{len(df)}: {row.get('file_name', idx)}")
        
        # Find JSON in response
        json_objects = find_json_in_text(response_text)
        
        if json_objects:
            for json_obj in json_objects:
                # Create new record
                record = {
                    'row_index': idx,
                    'file_name': row.get('file_name', ''),
                    'success': row.get('success', ''),
                    'timestamp': row.get('timestamp', ''),
                }
                
                # Add JSON fields
                if isinstance(json_obj, dict):
                    for key, value in json_obj.items():
                        # Convert complex values to strings
                        if isinstance(value, (dict, list)):
                            record[key] = json.dumps(value)
                        else:
                            record[key] = value
                
                all_records.append(record)
        else:
            # No JSON found
            record = {
                'row_index': idx,
                'file_name': row.get('file_name', ''),
                'success': row.get('success', ''),
                'timestamp': row.get('timestamp', ''),
                'note': 'No JSON found in response'
            }
            all_records.append(record)
    
    # Create output DataFrame
    output_df = pd.DataFrame(all_records)
    
    print(f"Extracted {len(output_df)} records")
    
    # Save to CSV
    try:
        output_df.to_csv(output_path, index=False, encoding='utf-8-sig')
        print(f"Saved to: {output_path}")
        
        # Show sample
        print("\nSample of extracted data:")
        print(output_df.head())
        
        # Show unique columns
        print(f"\nColumns in output: {list(output_df.columns)}")
        
    except Exception as e:
        print(f"Error saving: {e}")


if __name__ == "__main__":
    # Configuration
    INPUT_CSV = r"G:\My Drive\Datasets\XAI\gpt-outputs.csv"
    OUTPUT_CSV = r"G:\My Drive\Datasets\XAI\extracted_json.csv"
    RESPONSE_COLUMN = "response"  # or "output" depending on your CSV
    
    # Process the file
    process_csv(INPUT_CSV, OUTPUT_CSV, RESPONSE_COLUMN)
