import warnings
warnings.simplefilter(action='ignore', category=FutureWarning)
warnings.simplefilter(action='ignore', category=UserWarning)
warnings.simplefilter(action='ignore', category=RuntimeWarning)

from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor
from sklearn.model_selection import LeaveOneOut
from sklearn.metrics import accuracy_score, mean_absolute_error, mean_squared_error
from sklearn.feature_selection import SelectKBest, f_classif
import numpy as np
import pandas as pd

# # Read lf data from a file
# lf_path = r'\\files.ubc.ca\team\PPRC\CAMERA\Booth_Processed\docs\facial_labels.csv'
# df_lfs = pd.read_csv(lf_path)

# # Clean data
# df_lfs.set_index('ID', inplace=True)
# df_lfs = df_lfs.drop(columns=['Date'])
# df_lfs.replace({'N': 0, 'D': 1, 'VERY POOR': 0, 'POOR': 1, 'FAIR': 2, 'GOOD': 3, 'SOME': 1, 'MOST': 2, 'PD': 1, 'HC': 0}, inplace=True)

# lf_3 = df_lfs['F_UPDRS_KW_1'].rename('Label')
# lf_3 = lf_3[~lf_3.index.duplicated(keep='first')]
lf_path = r'D:\Codes\Python\ParkinsonAssessment\csv\4_label.csv'
df_lfs = pd.read_csv(lf_path)
df_lfs.set_index('ID', inplace=True)
lf_3 = df_lfs['0'].rename('Label')
lf_3 = lf_3[lf_3 >=0]

from facial_dataset import FacialDataset

au_sets = {
    'happy': [' AU06_r', ' AU12_r',' AU09_r', ' AU15_r', ' AU25_r',' AU01_r', ' AU04_r',' AU05_r', ' AU07_r', ' AU23_r'],
    'angry': [' AU06_r', ' AU12_r',' AU09_r', ' AU15_r', ' AU25_r',' AU01_r', ' AU04_r',' AU05_r', ' AU07_r', ' AU23_r'],
    'sad': [' AU06_r', ' AU12_r',' AU09_r', ' AU15_r', ' AU25_r',' AU01_r', ' AU04_r',' AU05_r', ' AU07_r', ' AU23_r'],
    'disgust': [' AU06_r', ' AU12_r',' AU09_r', ' AU15_r', ' AU25_r',' AU01_r', ' AU04_r',' AU05_r', ' AU07_r', ' AU23_r']
}

final_results_df = pd.DataFrame(index=lf_3.index)
final_results_df['Actual'] = lf_3

for emotion in au_sets.keys():
    print(f"Processing {emotion} emotion")
    facial_data = FacialDataset(r'\\files.ubc.ca\team\PPRC\CAMERA\Booth_Processed', au_sets, emotions=[emotion], regex_filter=None)
    facial_data.set_labels(lf_3)
    
    X = facial_data.get_data()
    y = facial_data.get_labels()
    X = X[y >= 0]
    y = y[y >= 0]
    X.fillna(0, inplace=True)
    
    if X.shape[1] < 24:
        print(f"Skipping {emotion} due to insufficient features.")
        continue

    loo = LeaveOneOut()
    y_predicted = pd.Series(index=y.index, dtype=float)
    
    for i, (train_index, test_index) in enumerate(loo.split(X, y)):
        X_train, X_test = X.iloc[train_index], X.iloc[test_index]
        y_train, y_test = y.iloc[train_index], y.iloc[test_index]

        selector = SelectKBest(score_func=f_classif, k=100)
        X_train = selector.fit_transform(X_train, y_train)
        X_test = selector.transform(X_test)

        rf_classifier = RandomForestClassifier(n_estimators=300, random_state=42, criterion='entropy', max_depth=8)
        y_train_binary, y_test_binary = y_train >= 1, y_test >= 1
        rf_classifier.fit(X_train, y_train_binary)
        y_pred_class = rf_classifier.predict(X_test)
        
        if y_pred_class[0] >= 1:
            X_train_regressor, y_train_regressor = X_train[y_train_binary], y_train[y_train_binary]
            regressor = RandomForestRegressor(n_estimators=300, random_state=21, max_depth=8)
            regressor.fit(X_train_regressor, y_train_regressor)
            y_pred_regressor = regressor.predict(X_test)
            y_predicted.iloc[test_index[0]] = y_pred_regressor[0]
        else:
            y_predicted.iloc[test_index[0]] = y_pred_class[0]
    
    final_results_df = final_results_df.join(y_predicted.rename(f'Predicted_{emotion}'), how='left')
    mse = mean_squared_error(y.dropna(), y_predicted.dropna())
    mae = mean_absolute_error(y.dropna(), y_predicted.dropna())
    accuracy = np.mean(y.dropna() == np.round(y_predicted.dropna()))
    print(f"{emotion} - LOO MSE: {mse:.4f}, MAE: {mae:.4f}, Accuracy: {accuracy:.4f}")

final_results_df.to_csv('csv/loo_predictions_all_emotions_5.csv', index=True)
