{"cells": [{"cell_type": "code", "execution_count": 112, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_21516\\1348818798.py:10: FutureWarning:\n", "\n", "Downcasting behavior in `replace` is deprecated and will be removed in a future version. To retain the old behavior, explicitly call `result.infer_objects(copy=False)`. To opt-in to the future behavior, set `pd.set_option('future.no_silent_downcasting', True)`\n", "\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_21516\\1348818798.py:14: FutureWarning:\n", "\n", "Downcasting behavior in `replace` is deprecated and will be removed in a future version. To retain the old behavior, explicitly call `result.infer_objects(copy=False)`. To opt-in to the future behavior, set `pd.set_option('future.no_silent_downcasting', True)`\n", "\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_21516\\1348818798.py:16: FutureWarning:\n", "\n", "Downcasting behavior in `replace` is deprecated and will be removed in a future version. To retain the old behavior, explicitly call `result.infer_objects(copy=False)`. To opt-in to the future behavior, set `pd.set_option('future.no_silent_downcasting', True)`\n", "\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_21516\\1348818798.py:18: FutureWarning:\n", "\n", "Downcasting behavior in `replace` is deprecated and will be removed in a future version. To retain the old behavior, explicitly call `result.infer_objects(copy=False)`. To opt-in to the future behavior, set `pd.set_option('future.no_silent_downcasting', True)`\n", "\n"]}], "source": ["\n", "# Read lf data from a file\n", "import pandas as pd\n", "lf_path = r'\\\\files.ubc.ca\\team\\PPRC\\CAMERA\\Booth_Processed\\docs\\facial_labels.csv'\n", "df_lfs = pd.read_csv(lf_path)\n", "\n", "#Clean data\n", "\n", "df_lfs = df_lfs.drop(columns=['Date'])\n", "df_lfs = df_lfs.replace('N', 0)\n", "df_lfs = df_lfs.replace('D', 1)\n", "df_lfs = df_lfs.replace('VERY POOR', 0)\n", "df_lfs = df_lfs.replace('POOR', 1)\n", "df_lfs = df_lfs.replace('FAIR', 2)\n", "df_lfs = df_lfs.replace('GOOD', 3)\n", "df_lfs = df_lfs.replace('SOME', 1)\n", "df_lfs = df_lfs.replace('MOST', 2)\n", "df_lfs = df_lfs.replace('PD', 1)\n", "df_lfs = df_lfs.replace('HC', 0)\n", "df_lfs.rename(columns = {'F_UPDRS_KW_1': 'label #1','F_UPDRS_KW_2': 'label #2', 'F_UPDRS_TM': 'label #3' },inplace=True)\n", "df_lfs.drop_duplicates(subset=['ID'],keep='first',inplace=True)\n", "df_lfs.set_index('ID', inplace=True)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Eye Blinking"]}, {"cell_type": "code", "execution_count": 147, "metadata": {}, "outputs": [], "source": ["#df_eb_neutral = pd.read_csv(r'\\\\files.ubc.ca\\team\\PPRC\\CAMERA\\Booth_Processed\\facial_expression\\docs\\eye_blinkings_neutral.csv')\n", "df_eb_neutral = pd.read_csv(r'\\\\files.ubc.ca\\team\\PPRC\\CAMERA\\Booth_Processed\\finger_tapping\\right\\csvs\\eye_blinkings.csv')\n", "df_eb_neutral.drop_duplicates(subset=['ID'],inplace=True,keep='first')\n", "df_eb_neutral.set_index('ID', inplace=True)\n", "df_eb_neutral['blink_rate'] = (df_eb_neutral['Blinks'])/df_eb_neutral['Duration (s)']\n", "combined_df = pd.concat([df_lfs, df_eb_neutral], axis=1, join='inner')\n", "combined_df['facial_symptom'] = combined_df['label #3']>1\n", "combined_df = combined_df[combined_df['Duration (s)'] >7]\n"]}, {"cell_type": "code", "execution_count": 146, "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"hovertemplate": "facial_symptom=True<br>blink_rate=%{x}<br>Max_EAR=%{y}<extra></extra>", "legendgroup": "True", "marker": {"color": "#636efa", "symbol": "circle"}, "mode": "markers", "name": "True", "orientation": "v", "showlegend": true, "type": "scatter", "x": [0.012719949120203519, 0.03169572107765452, 0.02844950213371266, 0.011031439602868174, 0.013683010262257697, 0.14647137150466044, 0.03420752565564424, 0.0741962077493817, 0.40787119856887305, 0.08807339449541285, 0.03306696059520529, 0.17716099505425556, 0.0200133422281521, 0.07666098807495741, 0.15052684395383842, 0.06309148264984227, 0.030864197530864196, 0.03215434083601286, 0.05542725173210161, 0.01580194890703187, 0.026166593981683382, 0.04638577502899112, 0.049099836333878884, 0.040160642570281124, 0.1215497594327678, 0.02245508982035928, 0.006663705019991115, 0.032520325203252036, 0.16849629818738832, 0.050547598989048016, 0.0422237860661506, 0.02249718785151856, 0.028129395218002815, 0.026702269692923896, 0.2801724137931035, 0.013882461823229986, 0.015113350125944586, 0.025305778152678194, 0.01641137855579869, 0.1281660054928288, 0.03305785123966942, 0.018387986515476556, 0.3161397670549085, 0.022796352583586626, 0.087890625, 0.0213903743315508, 0.0831255195344971, 0.18404907975460122, 0.08816120906801009, 0.3022162525184688, 0.015404364569961491, 0.018132366273798734, 0.14731673097158893, 0.03320420586607637, 0.17158176943699732, 0.04356243949661181, 0.14621178555604786, 0.011100832562442185, 0.23084321898044244, 0.03977461054027179, 0.08866995073891627, 0.033185840707964605, 0.11904761904761905, 0.2482544608223429, 0.020134228187919462, 0.15845070422535212, 0.2156118985825514, 0.2873359347286272, 0.0644352962233757, 0.1353892440767206, 0.02292701566679404, 0.011424219345011425, 0.10523678276121273, 0.03040283759817583, 0.09395184967704051, 0.062413314840499313, 0.047443331576172906, 0.014054813773717497, 0.014238253440911248, 0.03434459072696051, 0.013256738842244807, 0.07033997655334115, 0.07693716797948344, 0.014248397055331274, 0.025052192066805846, 0.01720676799541153, 0.07604562737642585, 0.06010016694490818, 0.1291248206599713, 0.34220532319391633, 0.012486992715920917, 0.059269015475798485, 0.0665188470066519, 0.018981335020563112, 0.045011252813203305, 0.020070245860511794, 0.07081038552321008, 0.02240477968633308, 0.017241379310344827, 0.4834377797672337, 0.07075471698113207, 0.4388185654008439, 0.020219039595619208, 0.03708281829419036, 0.013599274705349048, 0.034236804564907276, 0.049140049140049144, 0.056200824278756084, 0.02730168360382224, 0.044182621502209134, 0.011516314779270634, 0.049993056519927796, 0.008777062609713284, 0.008031053406505154, 0.012836970474967908, 0.08146639511201628, 0.014684287812041117, 0.033435497353023126, 0.026115342763873776, 0.2282947077135939, 0.03519061583577712, 0.016565433462175594, 0.015653535090007827, 0.04050632911392405], "xaxis": "x", "y": [0.4361994520175884, 0.6444921070522057, 1.0766170528818608, 0.586969408438488, 0.7495346643668408, 0.5671413014419029, 0.6724614966555793, 0.6747414509105762, 0.9588926786696595, 0.6794393712074075, 0.9177316147757686, 1.138963323675683, 0.5375454432829443, 0.6872517713845816, 0.6391295120177714, 0.4826507820011954, 0.9478091843409916, 0.629645557326418, 0.5973968480475746, 0.5448066693931083, 0.6694181055471718, 0.9690780958978352, 0.6823661028217917, 0.5373686082596318, 0.9223414879418624, 0.8635960326718388, 0.7208410125451004, 0.760722847812574, 0.8204002274346862, 0.7507317561075739, 0.5766357882767661, 0.8359462763599579, 0.6276814704091519, 0.5718842897636334, 0.7268216116614952, 0.639248405852699, 0.7300843602247996, 0.6300817316348535, 0.6072367140531916, 0.8409162037637065, 0.5097834172019301, 0.5440687360368905, 1.0448821546657203, 0.453721223391678, 0.5609828255992243, 0.4731025989153588, 0.9395191859209604, 0.9100888038313591, 0.5957541571750298, 0.6422925133223771, 0.7102563097022457, 1.1882686188665097, 0.6440332034150942, 0.7298406001226218, 0.9383196704851812, 0.6435378284371296, 0.6449319667708688, 0.6203244146278972, 0.8046687252756648, 0.6527708935277566, 0.587621255631589, 0.6251169056947022, 0.5459547061483393, 0.8658510381714135, 0.5165568460949426, 0.5595454344284113, 0.601876364869121, 0.9212851964601728, 0.6255008758201075, 0.6781048201615656, 0.6532464612200286, 0.6245607110418887, 0.4865458390150299, 1.1182671755138114, 0.6698400726615703, 0.7016863296920883, 0.6764611830127242, 0.6255763770555633, 0.7028964751051122, 0.9173815724470696, 0.5094681988427392, 0.5459859593643408, 0.7845648903899253, 0.7233530408695283, 0.7211996423999483, 0.7531349048908341, 0.7493797637335101, 0.8555917817969166, 0.826959869890683, 0.6998496167418164, 0.5858623885861571, 0.9403304304037272, 0.5435268403878768, 0.7549777612517428, 1.1372251830083733, 0.6512312781493228, 0.5735864023584213, 0.6205095558957285, 0.6774785165131872, 0.7718157530535107, 0.5246564381921281, 0.6446768488821953, 0.7245891398635668, 1.0457198656304612, 0.5001458369494458, 0.7431992316474169, 1.0670564889716232, 0.7820621903088224, 1.25094920202885, 0.8323816304950475, 0.4566511750239485, 0.8498132455214428, 0.5229548060547081, 0.6830618213816682, 0.6509062014689302, 1.0457845218033968, 0.6482312608927563, 0.6862513019785441, 0.550866554808987, 0.749133335749261, 0.8543463305629134, 0.5530792016980784, 0.6025556615640364, 1.5603075863155662], "yaxis": "y"}, {"hovertemplate": "facial_symptom=False<br>blink_rate=%{x}<br>Max_EAR=%{y}<extra></extra>", "legendgroup": "False", "marker": {"color": "#EF553B", "symbol": "circle"}, "mode": "markers", "name": "False", "orientation": "v", "showlegend": true, "type": "scatter", "x": [0.34122271472777455, 0.03722854188210962, 0.011419870574800152, 0.16492578339747113, 0.24948024948024947, 0.2896725440806045, 0.05231037489102005, 0.10380622837370243, 0.20221473278767452, 0.04006231916314267, 0.3907815631262525, 0.01384721901684745, 0.15243902439024393, 0.24061873388713834, 0.37524687294272546, 0.23389413212966761, 0.35928143712574845, 0.045714285714285714, 0.3111006363422107, 0.288135593220339, 0.10695187165775401, 0.19729206963249518, 0.10398613518197573, 0.2027027027027027, 0.12596899224806202, 0.09868421052631579, 0.23579201934703747, 0.12014417300760913, 0.19639934533551553, 0.19115890083632017, 0.2518891687657431, 0.0662739322533137, 0.14173228346456693, 0.11787819253438114, 0.03487358326068003, 0.3424124513618677, 0.5480817140009966, 0.059654006760787436, 0.0758773316471704, 0.039190071848465055, 0.11534025374855825, 0.04234297812279464, 0.5094339622641509, 0.10027855153203342, 0.31296572280178836, 0.2607885749767153, 0.2992021276595745, 0.4863481228668943, 0.1381957773512476, 0.5922676172196326, 0.035314891112419075, 0.7653061224489796, 0.1746591083192891, 0.1889763779527559, 0.039656311962987446, 0.6307490144546649, 0.11435832274459977, 0.039586540576204095, 0.2521714766040908, 0.16933207902163688, 0.023659305993690854, 0.07223693715386467, 0.38694074969770254, 0.3596403596403596, 0.031855588001061856, 0.3176130895091434, 0.13178537809852525, 0.07473841554559042, 0.28388533259114945, 0.04675324675324675, 0.08241758241758242, 0.15865384615384617, 0.27610008628127697, 0.2442126685321801, 0.11162790697674418, 0.08109934669970714, 0.10765550239234449, 0.10307064633884475, 0.26356589147286824, 0.6454282167976831, 0.4059040590405904, 0.19045200609446417, 0.054982817869415807, 0.427594070695553, 0.16793282686925232, 0.033660589060308554, 0.4574080212131256, 0.10661928031985785, 0.19303557910673733, 0.3073770491803279, 0.01015228426395939, 0.35450516986706054, 0.4573170731707317, 0.10489510489510488, 0.16129032258064516, 0.15170670037926676, 0.46563192904656314, 0.44642857142857145, 0.45086705202312133, 0.4545454545454546, 0.11614401858304298, 0.019808517662594914, 0.028804608737397985, 0.5245746691871456, 0.22549869904596703, 0.09024064171122995], "xaxis": "x", "y": [1.2309993845032383, 1.0987545009454638, 0.6524156842698854, 0.8069680806551975, 0.7666337374234335, 0.6793352938514604, 0.6450605354993783, 0.5746003093770752, 1.0200217257278466, 0.849293536821917, 0.5979110274031848, 0.6807841818294709, 0.5889208177536704, 0.6769246995537535, 0.9648888030980624, 0.8345202184389403, 0.841758528043977, 0.6524924253926536, 0.6164363107531674, 0.7288816156376807, 0.7467850759296428, 0.8223403033799273, 0.3486513745668991, 0.4567492628526158, 0.7076107108726424, 0.5062967759061291, 1.1484082789782248, 0.6905324412794049, 0.680803472830378, 0.7309234777179034, 0.9393083982544446, 0.6817817690414859, 0.5907517826598759, 0.599656457209405, 0.5874291508369901, 0.7270583361251455, 0.7433444456588605, 0.744965833115672, 0.6744450798933084, 0.6193840488899929, 0.6178205658279607, 0.5038438980828135, 0.7191895363621934, 0.7199485044343037, 0.8038299427640356, 0.7447914337345003, 0.6185091588435923, 0.5196524362185302, 0.722759008282991, 0.8439914151935939, 0.5836672226045401, 0.7143896020730667, 0.6505893786907757, 0.6368825620426244, 0.6422853220997038, 0.6393680578964003, 0.7363684982565184, 0.753182158641738, 1.0544707806796303, 0.5899666572676885, 0.7396235786401998, 0.7573798890109211, 0.5761307788161846, 0.7847062190245966, 0.4781751097394006, 1.146469861592214, 0.7074694076276901, 0.6202622085584314, 0.7110879597966551, 0.7241866447940655, 0.6846439106895714, 0.8753775318007637, 0.6741580486322489, 0.7444176584996833, 0.5904313896602467, 1.833361739586723, 0.4809238745843103, 0.7870854108433608, 0.5948293054639686, 0.6468397128117525, 0.6427048989487523, 1.344794356347225, 0.6274549155602965, 0.5663789962924286, 0.8517009035432842, 0.564713675803135, 0.4887301487403723, 0.622891944989069, 1.1192931302394384, 0.6224762047383288, 0.6651611472336921, 0.74411340824526, 0.5958504709195953, 0.6884919266839351, 0.5647139761383102, 0.5507605323962654, 0.5758606026294981, 0.7235279299778693, 0.660285670623509, 0.6225918383017068, 0.5179075706949827, 1.0038034638690945, 0.7830359170228836, 0.9989443864111984, 0.6955211967744568, 0.4389920713261154], "yaxis": "y"}], "layout": {"legend": {"title": {"text": "facial_symptom"}, "tracegroupgap": 0}, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "title": {"text": "Visualization of the Blinking Data"}, "xaxis": {"anchor": "y", "domain": [0, 1], "title": {"text": "blink_rate"}}, "yaxis": {"anchor": "x", "domain": [0, 1], "title": {"text": "Max_EAR"}}}}}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "#TSNE for visualization\n", "from sklearn.manifold import TSNE\n", "import plotly.express as px\n", "from sklearn.decomposition import PCA\n", "\n", "# Plot t-SNE results for the training data\n", "fig_train = px.scatter(combined_df, x=combined_df['blink_rate'], y=combined_df['Max_EAR'], color='facial_symptom', \n", "                       title='Visualization of the Blinking Data')\n", "fig_train.show()\n"]}, {"cell_type": "code", "execution_count": 144, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\assessment\\Lib\\site-packages\\sklearn\\manifold\\_t_sne.py:1162: FutureWarning:\n", "\n", "'n_iter' was renamed to 'max_iter' in version 1.5 and will be removed in 1.7.\n", "\n"]}, {"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"customdata": [[35623], [29377], [32519], [34492], [17599], [28726], [33927], [28615], [24757], [25779], [29157], [38519], [31961], [38215], [17000], [27123], [33164], [16883], [31437], [36407], [32282], [17434], [23160], [24318], [31769], [29570], [23284], [19015], [21401], [26692], [25352], [25533], [19124], [31319], [30893], [34142], [33023], [31240], [15377], [30982], [35246], [24889], [35747], [16219], [39528], [38100], [18198], [30148], [30104], [36436], [34965], [31092], [36532], [38256], [28813], [34509], [27425], [38050], [24475], [34914], [38255], [20959], [33151], [28411], [32853], [14555], [31318], [36581], [38902], [36660], [24860], [17980], [28550], [25260], [7571], [15813], [36564], [7415], [34417], [31231], [39200], [32594], [17200], [25957], [31182], [38073], [28321], [26145], [30009], [28350], [28630], [30414], [30279], [38046], [33527], [26407], [33430], [25793], [27423], [18372], [31100], [38141], [35154], [28715], [40046], [39712], [22224], [10039], [24798], [28225], [36880], [32424], [13390], [35080], [13484], [21128], [25412], [26326], [7477], [33661], [34802], [30428], [28306], [28498]], "hovertemplate": "label=True<br>TSNE Component 1=%{x}<br>TSNE Component 2=%{y}<br>ID=%{customdata[0]}<extra></extra>", "legendgroup": "True", "marker": {"color": "#636efa", "symbol": "circle"}, "mode": "markers", "name": "True", "orientation": "v", "showlegend": true, "type": "scatter", "x": [6.628121, 10.251578, 3.5295446, 9.895939, 4.7463675, 5.238315, -1.6938767, 7.084715, -3.447445, -7.211364, -0.82093984, 14.235436, -5.773418, -9.422575, -10.749495, -13.290267, 13.675932, -11.357046, -10.37863, 0.20594244, -9.988511, -8.384696, -9.096558, -12.275587, 1.9023558, -7.8932366, 13.527895, -11.407691, 1.4664267, 10.638582, -12.467215, -7.886322, -10.625424, -10.209908, -4.198384, 4.294077, 1.6515872, -9.582004, -0.6534859, -2.9668436, -0.68181175, -3.5462425, -0.24257746, 9.032195, 2.7317357, -7.0163693, -0.8960578, -0.7918034, 6.57422, -3.2639425, 1.0173812, -3.093762, -6.2014723, 9.595884, 8.828654, 2.8817842, 5.274316, 9.798983, -3.0222352, -5.45164, 2.4776266, -0.7966716, -1.4238124, 0.5727864, -5.9066787, -9.571527, 7.162667, 8.125464, 9.849096, -7.407054, -8.35498, 9.194019, 2.1073854, 1.4966823, 8.102967, -6.3557706, 0.31692487, 3.941834, 3.491625, 8.839258, 5.5411863, 3.8059614, 9.340724, 3.462983, -9.455967, -1.9142042, 9.570417, -5.546642, 3.15016, -2.2349498, 7.1155376, -5.1355453, 5.3344502, -4.474152, 11.891292, 10.997471, -8.532368, 9.487132, 12.281282, 7.5958247, 3.634051, -0.2710365, 10.873505, 7.32931, 4.9182844, -1.7347127, 7.431778, 9.079689, 11.833814, -7.4615006, 8.907922, 12.524489, 12.1632805, 12.721046, 6.408516, 4.7109346, 2.5473802, -1.1393548, 5.929767, -4.153242, -2.385248, -1.0122086, 0.5210803, 10.6627035], "xaxis": "x", "y": [-2.6291313, -0.53003097, -3.0708647, -1.1693257, -3.2411232, -0.6482411, -2.4369266, -1.3360509, 3.7402096, -0.06006298, -2.8836613, -1.0782269, -1.5162647, 0.4217245, 1.160904, 1.2818792, -0.21058427, 0.6864683, 0.4599953, -3.2860663, -0.16888577, -0.4324362, -0.09010716, 1.1608245, -1.3012023, -0.9776093, -0.06087081, 0.74461883, 0.023164107, 0.2222566, 1.1992707, -0.98470056, 0.22291407, -0.039788086, 2.8433456, -3.3172896, -3.368828, -0.3538513, -3.2326, -0.3102796, -2.6068156, -2.053282, 2.9551551, -1.203992, -2.0122788, -1.1526338, -1.8093854, 1.0146805, -1.3187681, 2.8705254, -3.3141747, -2.2303538, 0.48109496, -0.78786623, 1.2804242, -2.9385011, -0.6342639, -1.2410502, 1.9297942, -1.2598231, -2.0321589, -2.5542972, -0.937087, 2.2257648, -1.4367368, 1.1624147, 1.5266361, 2.350233, -0.124102965, 0.6174157, -0.8891475, -1.583374, -1.665445, -3.0565464, -0.5373513, -0.719441, -2.6060798, -3.395482, -3.4676685, -1.0683105, -3.116777, -2.2365122, -0.10316909, -3.468226, -0.40699142, -2.924173, -0.02754651, -0.8302169, -1.1917452, 3.1728172, -2.5015626, -0.9802262, -2.0470438, -1.7628088, 0.6010678, -0.3180931, -0.054357633, -1.096699, 0.47802484, 3.3685465, -2.2542028, 4.0256724, -0.3828114, -2.0211537, -3.195643, -2.4899232, -1.71051, -0.57912487, 0.36371478, -0.6730471, -1.6207205, 0.60943866, 0.23884298, 0.36369282, -2.755116, -1.8799282, -3.4327388, -2.6891558, -2.754329, 2.2010136, -2.2572765, -3.2574265, -3.303567, 0.08087664], "yaxis": "y"}, {"customdata": [[29880], [21696], [36297], [25439], [16827], [24622], [24192], [33166], [33749], [24352], [31848], [32160], [28641], [17202], [39274], [28731], [36220], [28637], [30961], [18317], [30593], [25934], [7928], [7900], [19091], [7794], [7610], [7629], [26546], [7735], [7652], [7609], [7580], [7452], [7225], [7488], [7640], [7706], [7852], [7822], [7628], [7888], [7985], [39685], [7417], [7578], [7597], [7987], [7518], [7814], [23191], [7634], [29079], [7795], [7755], [7288], [40613], [24601], [7284], [7566], [7819], [35816], [7677], [22178], [7855], [7587], [32541], [7155], [7536], [7681], [7334], [7727], [32200], [38210], [7771], [7137], [7152], [28408], [7762], [7776], [7709], [31457], [7501], [39686], [22227], [7884], [30201], [7242], [7555], [7198], [40771], [6961], [6703], [6238], [6846], [6509], [6371], [6654], [6295], [6582], [7945], [40550], [7143], [7631], [7389], [27166]], "hovertemplate": "label=False<br>TSNE Component 1=%{x}<br>TSNE Component 2=%{y}<br>ID=%{customdata[0]}<extra></extra>", "legendgroup": "False", "marker": {"color": "#EF553B", "symbol": "circle"}, "mode": "markers", "name": "False", "orientation": "v", "showlegend": true, "type": "scatter", "x": [14.245758, 7.157616, 9.20943, -0.608814, 7.692002, 6.438387, -9.790636, -14.066386, -10.34663, 13.535828, -10.426297, 4.3748684, -10.838981, -1.0492433, -2.6133683, 6.8374205, 2.1577072, -8.085434, 1.9119365, -0.64418674, 9.614797, 7.6110535, -14.073836, -14.121243, 14.181775, -13.959956, -1.9955472, -8.658785, -12.82647, -8.170453, 6.5391183, -7.369209, -8.269898, -8.379152, -2.1639388, 0.716403, 7.616062, 7.9951024, -4.1611896, 5.8909726, -7.903809, -6.7475896, -1.4092984, -1.0635898, -4.370846, -2.3249195, -3.1306398, -4.550254, -7.74609, 0.35258937, -11.780554, -3.5245314, 11.473169, -3.0340283, -5.3469095, -1.5878378, 6.2728267, 5.5611353, -0.58655304, -3.131654, -8.817714, 3.0595188, -4.6574216, 1.2474327, 0.048528597, 1.649856, -3.4869072, 2.1290193, -0.37827277, 0.7974591, -6.070126, 2.8914287, -1.0168747, 0.8524594, -3.4244225, 4.8587403, -2.6340356, 5.996668, 0.6075077, -3.6977324, -1.5191863, 13.233521, 4.4584913, -0.48860815, 7.296621, -1.3373253, -2.267291, -9.827096, 7.932073, -3.4723656, 10.902282, -13.755213, -13.762075, -14.165571, -13.617815, -13.535688, -13.24602, -13.729967, -11.000343, -13.4146595, -8.040796, 11.166606, 11.344709, 1.5242101, -1.2652478, 10.596506], "xaxis": "x", "y": [-1.0888451, -2.02891, -1.5814877, 0.5162844, 1.908089, 2.0485387, 0.2765111, 1.2589865, 1.4658223, -0.04741375, 2.3192506, -3.298211, 1.1947114, 1.9581494, 3.5094624, 1.6561003, 3.4894376, -0.442338, 3.1607642, 2.6190066, 0.5546235, 1.4084691, 1.2670895, 1.7077533, -0.9914829, 1.2690724, 1.9056631, 0.65789205, 1.5737519, 1.3622763, 1.7809693, -0.32965514, 0.88912463, 0.5127638, -2.3207064, 3.2683506, 3.4459445, -1.3061572, -1.086193, -2.3788319, 0.38071164, -0.9334141, 4.381802, -1.4485939, 3.1850777, 2.294536, 2.8195326, 4.207366, 0.766671, 4.600923, 0.99197614, 4.911847, 1.3468574, 1.1268353, -1.3128697, 4.766433, -0.8660265, -2.5445142, 2.0873382, 0.65524924, -0.69192576, -2.3338506, 3.7503252, 3.4218354, -2.94129, 3.166562, -0.11857481, -2.3747077, 2.5358024, -2.703345, -0.48891953, -0.6310955, 2.465704, 2.2018688, -0.6096067, -1.843158, -0.89607304, -1.2152764, 2.4664803, 4.7417903, 3.7344048, 0.57391196, -2.4770417, 3.9506645, 0.89381415, -2.6528778, 4.118787, 0.78992176, 1.4245267, 2.964254, -0.6065707, 1.9182136, 2.0362856, 1.7461928, 1.2005032, 1.2662644, 1.9621129, 2.0230114, 2.2445202, 1.9704736, 0.4082747, -0.14959753, 0.27451378, 4.385325, 1.7266487, 0.66715235], "yaxis": "y"}], "layout": {"legend": {"title": {"text": "label"}, "tracegroupgap": 0}, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "title": {"text": "t-SNE Visualization of the Training Data"}, "xaxis": {"anchor": "y", "domain": [0, 1], "title": {"text": "TSNE Component 1"}}, "yaxis": {"anchor": "x", "domain": [0, 1], "title": {"text": "TSNE Component 2"}}}}}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "#TSNE for visualization\n", "from sklearn.manifold import TSNE\n", "import plotly.express as px\n", "from sklearn.decomposition import PCA\n", "# Apply t-SNE to the training data\n", "tsne = TSNE(n_components=2, random_state=42,perplexity=40, n_iter=300)\n", "X_train_tsne = tsne.fit_transform(combined_df[df_eb_neutral.columns.tolist()])\n", "\n", "# Create a DataFrame for visualization\n", "df_tsne_train = pd.DataFrame(X_train_tsne, columns=['TSNE Component 1', 'TSNE Component 2'])\n", "df_tsne_train['label'] = combined_df[['facial_symptom']].values\n", "df_tsne_train['ID'] = combined_df.index\n", "\n", "# Plot t-SNE results for the training data\n", "fig_train = px.scatter(df_tsne_train, x=df_tsne_train['TSNE Component 1'], y=df_tsne_train['TSNE Component 2'], color='label', hover_data=['ID'],\n", "                       title='t-SNE Visualization of the Training Data')\n", "fig_train.show()\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Evaluate Algorithm"]}, {"cell_type": "code", "execution_count": 116, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["49.0\n"]}], "source": ["import numpy as np\n", "import pandas as pd\n", "# evaluate results \n", "df_true_blinking = pd.read_csv(r'\\\\files.ubc.ca\\team\\PPRC\\CAMERA\\Booth_Processed\\finger_tapping\\right\\csvs\\booth_right_blinking.csv')\n", "df_true_blinking.set_index('ID', inplace=True)\n", "df_combinned_true = pd.concat([df_true_blinking, df_eb_neutral], axis=1, join='inner')\n", "df_combinned_true['diff'] = df_combinned_true['number of blinks'] - df_combinned_true['Blinks']\n", "sum = np.sum(np.abs(df_combinned_true['diff']))\n", "print(sum)"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": 149, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Merged results saved to \\\\files.ubc.ca\\team\\PPRC\\CAMERA\\Booth_Processed\\docs\\merged_eye_blinking_results.csv\n"]}], "source": ["import os\n", "import pandas as pd\n", "\n", "def merge_eye_blinking_results(base_path, tasks, output_file):\n", "    \"\"\"\n", "    Reads all eye blinking result CSV files generated by EyeBlinkingsVideo\n", "    and merges them into a single DataFrame.\n", "    \n", "    :param base_path: The base directory where task folders are located.\n", "    :param tasks: A list of task folder paths relative to the base path.\n", "    :param output_file: The output CSV file to save the merged results.\n", "    \"\"\"\n", "    merged_df = pd.DataFrame()\n", "    \n", "    for task in tasks:\n", "        csv_path = os.path.join(base_path, task, 'csvs', 'eye_blinkings.csv')\n", "        if os.path.exists(csv_path):\n", "            df = pd.read_csv(csv_path)\n", "            df['Task'] = task  # Add a column to indicate the task\n", "            merged_df = pd.concat([merged_df, df], ignore_index=True)\n", "        else:\n", "            print(f\"Warning: File not found {csv_path}\")\n", "    \n", "    if not merged_df.empty:\n", "        merged_df.to_csv(output_file, index=False)\n", "        print(f\"Merged results saved to {output_file}\")\n", "    else:\n", "        print(\"No data merged. Check input files.\")\n", "\n", "if __name__ == '__main__':\n", "    base_path = r'\\\\files.ubc.ca\\team\\PPRC\\CAMERA\\Booth_Processed'\n", "    tasks = [r'facial_expression\\basic_facial\\text',\n", "             r'hand_movement\\left_open_close', r'hand_movement\\right_open_close',r'hand_movement\\left_up_down',r'hand_movement\\right_up_down',r'finger_tapping\\right', r'finger_tapping\\left']\n", "    \n", "    output_file = os.path.join(base_path, r'docs\\merged_eye_blinking_results.csv')\n", "    \n", "    merge_eye_blinking_results(base_path, tasks, output_file)"]}, {"cell_type": "code", "execution_count": 181, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "# Load the dataset\n", "merged_df_eb = pd.read_csv(r'\\\\files.ubc.ca\\team\\PPRC\\CAMERA\\Booth_Processed\\docs\\merged_eye_blinking_results.csv')\n", "\n", "# Aggregate the data properly\n", "comulative_df = merged_df_eb.groupby('ID').agg({\n", "    'Blinks': 'sum',\n", "    'Duration (s)': 'sum',\n", "    'Min_EAR': 'min',\n", "    'Max_EAR': 'max'\n", "})\n", "\n", "# Compute standard deviation per ID, ignoring zeros in mean calculation\n", "std_devs = merged_df_eb[merged_df_eb['Standard Deviation']>0].groupby('ID')['Standard Deviation'].mean()\n", "\n", "# Add standard deviation to the cumulative dataframe\n", "comulative_df['Standard Deviation'] = std_devs\n", "\n", "# Calculate blink rate\n", "comulative_df['blink_rate'] = (comulative_df['Blinks'] + 1) / comulative_df['Duration (s)']\n", "\n", "# Combine with the labels\n", "label_name = 'label #3'\n", "\n", "# Ensure df_lfs[label_name] is aligned with comulative_df\n", "if label_name in df_lfs.columns:\n", "    combined_df = pd.concat([df_lfs[label_name].reindex(comulative_df.index), comulative_df], axis=1, join='inner')\n", "\n", "    # Create binary facial symptom label\n", "    combined_df['facial_symptom'] = combined_df.index> 8000"]}, {"cell_type": "code", "execution_count": 176, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\assessment\\Lib\\site-packages\\sklearn\\manifold\\_t_sne.py:1162: FutureWarning:\n", "\n", "'n_iter' was renamed to 'max_iter' in version 1.5 and will be removed in 1.7.\n", "\n"]}, {"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"customdata": [[6238], [6295], [6371], [6509], [6582], [6654], [6703], [6846], [6961], [7137], [7143], [7152], [7155], [7182], [7198], [7225], [7242], [7284], [7288], [7334], [7389], [7417], [7452], [7488], [7501], [7518], [7536], [7555], [7566], [7578], [7580], [7587], [7597], [7609], [7610], [7628], [7629], [7631], [7634], [7640], [7652], [7677], [7681], [7706], [7709], [7727], [7735], [7755], [7762], [7771], [7776], [7794], [7795], [7814], [7819], [7822], [7852], [7855], [7884], [7888], [7900], [7928], [7945], [7985], [7987], [16827], [17202], [18317], [19091], [21696], [22178], [22227], [23191], [24192], [24352], [24601], [24622], [25439], [25934], [26546], [27166], [28408], [28637], [28641], [28731], [29079], [29880], [30201], [30593], [30961], [31457], [31848], [32160], [32200], [32541], [33166], [33749], [35816], [36220], [36297], [38210], [39274], [39685], [39686], [40550], [40613], [40771]], "hovertemplate": "label=False<br>TSNE Component 1=%{x}<br>TSNE Component 2=%{y}<br>ID=%{customdata[0]}<extra></extra>", "legendgroup": "False", "marker": {"color": "#636efa", "symbol": "circle"}, "mode": "markers", "name": "False", "orientation": "v", "showlegend": true, "type": "scatter", "x": [8.75791, 7.2334843, 8.310017, 8.271102, 8.445033, 8.69245, 8.712162, 8.329524, 8.610283, -3.4047072, -7.6868997, 1.3303009, -1.4486251, -5.1275873, 4.364229, 0.25031775, 5.7067914, 2.4391205, 4.769931, 3.15634, 2.5019639, 4.9283977, 4.819701, 2.5934832, -2.9095604, 4.654165, 2.6600573, -6.8257213, 2.6324244, 3.3914866, 4.997932, 1.918504, 4.133103, 3.801015, 2.9752061, 4.5048933, 5.0417776, -5.8239417, 5.799623, -8.069901, -6.2866206, 5.5000124, -1.0929803, -5.110148, 4.0179667, -0.7063894, 5.2729325, 2.279444, 2.0755556, 1.879839, 5.75291, 8.562023, 2.895971, 4.304251, 4.2913957, -3.5006328, 1.8758096, -0.9601034, -0.3308908, 3.159574, 8.794914, 8.720381, 4.6111336, 4.4359884, 5.7954693, -7.028933, 2.5664167, 2.8156562, -9.808958, -4.288094, 2.4826906, -6.118165, 6.9071, 5.379185, -9.427725, -3.318386, -6.392204, 1.2990019, -6.658709, 7.8541837, -7.557882, -4.321222, 4.069902, 6.482186, -6.3823714, -8.454745, -9.850901, 4.652287, -7.0003247, -5.244202, -9.313658, 6.8849835, -2.411983, 2.901497, 2.2268622, 8.722744, 6.3582306, -1.8476732, -5.721394, -5.4266415, 1.7614108, 4.386575, 0.39587754, 3.6868868, -7.3483047, -4.6657767, -6.9650216], "xaxis": "x", "y": [-7.177685, -5.397391, -6.576732, -7.259125, -6.5928335, -6.713784, -6.7265162, -7.2459893, -6.726215, 2.8055704, 6.297709, -1.8528891, 1.19551, 4.791179, -1.1359074, -1.9849374, -5.5912294, 0.6189252, 0.99753207, -4.075898, -0.02981226, -1.8663554, -4.944457, 1.8856661, 2.6890957, -4.453704, 0.9368841, 3.6015773, -1.735802, -0.54929996, -4.683089, 2.3954284, -0.931026, -4.833716, -0.45611534, -4.7575464, -5.0470176, 0.39007708, -0.37129253, 2.3765986, 2.455143, -1.9201664, 0.17113622, 4.8030868, 0.5643521, 2.2372487, -4.3567543, -3.9975266, 1.563654, -2.3777368, -0.59897316, -7.3471813, -1.5168456, 1.8774273, -5.920566, 3.741731, -3.066497, -0.42928025, -1.4603004, -4.7496295, -6.895474, -7.2255464, -4.820826, 0.8881354, -1.6213797, 3.1662834, 0.22966716, 0.7597655, 7.7780313, 4.560547, 2.2255447, 3.4553542, -6.621406, -5.9269238, 6.911398, 3.5582936, 2.2384222, 0.11506934, 3.4089444, -6.730222, 5.4894714, 3.416974, -5.3439593, -5.9570675, 2.7211478, 5.4103127, 7.865533, 0.22680123, 4.9892173, 1.0433818, 6.354214, -4.936822, 2.9336946, 0.45301497, -2.2833755, -7.220461, -5.51372, 1.8725102, 1.1256082, 5.753259, 1.6540194, -0.26324543, -0.7808454, 1.3174384, 6.3410625, 3.4780202, 6.3993397], "yaxis": "y"}, {"customdata": [[7415], [7477], [7571], [10039], [13390], [13484], [14555], [15377], [15813], [16219], [16883], [17000], [17200], [17434], [17599], [17980], [18198], [18372], [19015], [19124], [20959], [21128], [21401], [22224], [23160], [23284], [24318], [24475], [24757], [24798], [24860], [24889], [25260], [25352], [25412], [25533], [25779], [25793], [25957], [26145], [26326], [26407], [26692], [27123], [27423], [27425], [28225], [28306], [28321], [28350], [28411], [28498], [28550], [28615], [28630], [28715], [28726], [28813], [29157], [29377], [29570], [30009], [30104], [30148], [30279], [30414], [30428], [30893], [30982], [31092], [31100], [31182], [31231], [31240], [31318], [31319], [31437], [31769], [31961], [32282], [32424], [32519], [32594], [32853], [33023], [33151], [33164], [33430], [33527], [33661], [33927], [34142], [34417], [34492], [34509], [34802], [34914], [34965], [35080], [35154], [35246], [35623], [35747], [36407], [36436], [36532], [36564], [36581], [36660], [36880], [38046], [38050], [38073], [38100], [38141], [38215], [38255], [38256], [38519], [38902], [39200], [39528], [39712], [40046]], "hovertemplate": "label=True<br>TSNE Component 1=%{x}<br>TSNE Component 2=%{y}<br>ID=%{customdata[0]}<extra></extra>", "legendgroup": "True", "marker": {"color": "#EF553B", "symbol": "circle"}, "mode": "markers", "name": "True", "orientation": "v", "showlegend": true, "type": "scatter", "x": [-2.2519596, -3.3314528, -5.6872406, -6.0414968, -8.303051, -3.5017838, 5.8080792, -0.9766839, 3.1312628, -5.5884237, 6.49351, 6.4260955, -6.4702463, 4.241996, -2.5559738, -5.413552, 0.10559164, -7.979999, 6.5533743, 5.804847, -0.5017743, -3.3253624, 0.33886874, -4.584108, 4.8196025, -9.413217, 7.314759, 3.4297507, 4.9150014, -8.074546, 3.9577312, 0.9391889, -1.6200377, 7.4542837, -2.245173, 3.690985, 3.9013426, -5.8810334, -2.467719, 2.660023, -0.41791597, -7.175265, -7.308307, 8.082664, -8.433444, -4.3448935, 3.6351006, -1.4178859, -6.6112742, 3.9709804, 1.9162643, -7.2307425, -0.9778234, -4.7163186, -3.9628901, -4.359221, -4.322275, -7.2093744, -0.55677444, -6.630351, 3.667056, -1.241105, -4.517824, 1.7514627, -3.4977698, 2.38112, -0.85100114, 4.597498, 1.8664693, 0.65525645, -2.2977304, 4.7901855, -5.6110487, 4.8950405, -6.491602, 5.406908, 5.7921124, -0.6206403, 2.340156, 5.2266045, -8.708475, -2.521868, -2.6641903, 2.4603026, -1.8638415, 0.8525366, -9.510678, 4.5611115, -8.233639, 4.102751, -0.058201432, -2.3668492, -2.3215265, -6.0448008, -2.159284, 0.35955778, 2.3456693, -1.5946255, -8.8160095, -7.064404, -0.5524332, -3.6548798, 2.857622, -1.2977031, 4.210347, 3.794711, -0.8488631, -7.5722723, 4.4113626, -5.26768, 1.5178022, -5.94959, -0.2454475, 3.1373918, 3.6575346, 5.3033895, -1.4076884, -6.1279235, -9.843809, -6.681585, -2.935471, -1.5019306, -0.042896003, -2.6432042], "xaxis": "x", "y": [2.6251833, 3.891255, 4.5070243, 5.2051096, 6.495725, 4.258483, -5.246628, -1.1117944, -4.383345, 5.5276423, -6.560665, -5.9219246, 5.139379, -5.4957204, 3.1686661, 5.7419586, -0.7919504, 2.387063, -6.570364, -6.4227643, -1.1160524, 2.7291012, 1.4206265, 4.6082273, -5.7365575, 6.9225216, -6.799292, -1.2176139, -0.8105976, 6.4035397, -5.7143397, -2.9551852, 0.524408, -6.8602657, 1.1244823, -5.530058, -4.593008, 5.7688627, 1.9370927, -3.9073222, -1.3371164, 6.31582, 5.7982545, -7.1759663, 6.459123, 2.7214856, -5.0967793, -0.26418376, 5.2450733, -0.13103837, 1.4897531, 5.898917, 1.3661062, 4.1989074, 4.7019963, 4.6543365, 2.6972709, 4.227631, -1.0116267, 6.006256, -5.5041323, 2.250651, 3.8409648, 0.09079954, 3.2053716, -3.7136157, -1.3614097, -1.8454669, -1.913938, -2.6838884, 2.1886945, -6.1357827, 5.321907, -6.1396303, 3.0240207, -6.3427677, -6.1712065, 1.3280587, -4.3742495, -6.2836213, 6.33268, 1.9137812, 2.174066, -4.458948, 0.5347232, -0.93854403, 7.078717, -5.382399, 6.1855993, -2.0764256, -1.7036601, 2.8967679, 2.1406672, 6.073746, 1.5230538, -2.1264353, -4.0507913, 0.113744326, 6.526115, 6.296807, -1.0065517, 4.406018, 1.1920474, -0.4589318, -1.0144316, -3.6314645, -0.13980755, 3.2486455, -4.316773, 5.6239233, -3.5399246, 6.044919, -1.9945134, -5.042489, 1.4822265, -5.6138396, 1.5488961, 5.697572, 7.860752, 5.4766836, 3.7487035, 1.7226131, -1.7039698, 3.2937753], "yaxis": "y"}], "layout": {"legend": {"title": {"text": "label"}, "tracegroupgap": 0}, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "title": {"text": "t-SNE Visualization of the Training Data"}, "xaxis": {"anchor": "y", "domain": [0, 1], "title": {"text": "TSNE Component 1"}}, "yaxis": {"anchor": "x", "domain": [0, 1], "title": {"text": "TSNE Component 2"}}}}}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "#TSNE for visualization\n", "from sklearn.manifold import TSNE\n", "import plotly.express as px\n", "from sklearn.decomposition import PCA\n", "# Apply t-SNE to the training data\n", "tsne = TSNE(n_components=2, random_state=42,perplexity=50, n_iter=300)\n", "X_train_tsne = tsne.fit_transform(combined_df[comulative_df.columns.tolist()])\n", "\n", "# Create a DataFrame for visualization\n", "df_tsne_train = pd.DataFrame(X_train_tsne, columns=['TSNE Component 1', 'TSNE Component 2'])\n", "df_tsne_train['label'] = combined_df[['facial_symptom']].values\n", "df_tsne_train['ID'] = combined_df.index\n", "\n", "# Plot t-SNE results for the training data\n", "fig_train = px.scatter(df_tsne_train, x=df_tsne_train['TSNE Component 1'], y=df_tsne_train['TSNE Component 2'], color='label', hover_data=['ID'],\n", "                       title='t-SNE Visualization of the Training Data')\n", "fig_train.show()"]}, {"cell_type": "code", "execution_count": 164, "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"alignmentgroup": "True", "box": {"visible": true}, "hovertemplate": "facial_symptom=%{x}<br>Blinks=%{y}<extra></extra>", "legendgroup": "False", "marker": {"color": "blue"}, "name": "False", "offsetgroup": "False", "orientation": "v", "points": "all", "scalegroup": "True", "showlegend": true, "type": "violin", "x": [false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false], "x0": " ", "xaxis": "x", "y": [0, 12, 6, 1, 5, 4, 4, 1, 3, 5, 2, 5, 4, 4, 14, 1, 3, 14, 31, 3, 12, 13, 4, 21, 3, 5, 16, 16, 8, 13, 5, 21, 14, 2, 12, 4, 4, 36, 29, 54, 19, 15, 2, 4, 21, 10, 7, 1, 16, 5, 25, 0, 9, 35, 0, 2, 3, 1, 1, 1, 1, 0, 4, 26, 18, 21, 13, 16, 25, 2, 23, 13, 0, 1, 5, 2, 22, 9, 16, 3, 8, 7, 1, 4, 18, 18, 67, 22, 9, 21, 24, 12, 0, 15, 6, 0, 6, 4, 25, 0, 15, 18, 5, 24, 1, 8, 0], "y0": " ", "yaxis": "y"}, {"alignmentgroup": "True", "box": {"visible": true}, "hovertemplate": "facial_symptom=%{x}<br>Blinks=%{y}<extra></extra>", "legendgroup": "True", "marker": {"color": "red"}, "name": "True", "offsetgroup": "True", "orientation": "v", "points": "all", "scalegroup": "True", "showlegend": true, "type": "violin", "x": [true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true], "x0": " ", "xaxis": "x", "y": [0, 1, 7, 4, 0, 0, 5, 0, 2, 1, 0, 4, 6, 1, 0, 0, 4, 44, 0, 0, 1, 5, 10, 3, 1, 0, 0, 11, 18, 2, 0, 0, 1, 0, 0, 0, 3, 1, 0, 2, 1, 1, 4, 0, 1, 10, 1, 0, 6, 17, 15, 3, 6, 5, 0, 2, 10, 15, 1, 2, 0, 8, 6, 10, 4, 2, 0, 12, 6, 0, 4, 0, 2, 0, 17, 0, 1, 7, 0, 0, 5, 1, 4, 0, 0, 6, 4, 2, 4, 10, 1, 0, 0, 0, 2, 1, 1, 0, 0, 1, 1, 0, 18, 0, 14, 6, 2, 26, 5, 0, 0, 0, 0, 0, 25, 2, 5, 2, 39, 5, 0, 5, 1, 0], "y0": " ", "yaxis": "y"}], "layout": {"legend": {"title": {"text": "facial_symptom"}, "tracegroupgap": 0}, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "title": {"text": "Violin Plot of Blinks"}, "violinmode": "overlay", "xaxis": {"anchor": "y", "categoryarray": [false, true], "categoryorder": "array", "domain": [0, 1], "title": {"text": "facial_symptom"}}, "yaxis": {"anchor": "x", "domain": [0, 1], "title": {"text": "Blinks"}}}}}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["t-statistic: 5.765750519387023, p-value: 2.6141689937417563e-08\n"]}, {"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"alignmentgroup": "True", "box": {"visible": true}, "hovertemplate": "facial_symptom=%{x}<br>Duration (s)=%{y}<extra></extra>", "legendgroup": "False", "marker": {"color": "blue"}, "name": "False", "offsetgroup": "False", "orientation": "v", "points": "all", "scalegroup": "True", "showlegend": true, "type": "violin", "x": [false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false], "x0": " ", "xaxis": "x", "y": [9.533333333333335, 28.833333333333336, 15.033333333333335, 13.183333333333334, 13.2, 11.2, 10.933333333333334, 12.4, 11.283333333333333, 73.98333333333333, 104.14999999999999, 55.733333333333334, 66.9, 83.81666666666666, 48.8, 57.35, 37.516666666666666, 59.483333333333334, 50.733333333333334, 48.53333333333333, 57.650000000000006, 44.733333333333334, 42.416666666666664, 64.25, 72.75, 43.416666666666664, 59.88333333333333, 88.06666666666666, 53.15, 53.68333333333334, 42.333333333333336, 69.26666666666667, 50.13333333333333, 45.266666666666666, 55.13333333333333, 43.35, 41.61666666666667, 70.53333333333333, 39.2, 100.35, 79.39999999999999, 41.35, 64.16666666666667, 83.81666666666666, 54.2, 69.33333333333333, 41.85, 50.43333333333333, 64.5, 53.75, 40.28333333333333, 10.133333333333333, 52.916666666666664, 60.78333333333333, 42.266666666666666, 76.55, 52.71666666666667, 62.78333333333333, 59.416666666666664, 47.233333333333334, 9.866666666666667, 9.616666666666667, 43.05, 53, 39.06666666666666, 88.18333333333334, 58.18333333333334, 58.99999999999999, 206.39999999999998, 80.58333333333333, 66.73333333333333, 83.36666666666666, 28.316666666666663, 38.233333333333334, 149.76666666666665, 75.78333333333333, 79.4, 60.63333333333333, 86.16666666666666, 20.366666666666667, 99.73333333333333, 77.61666666666667, 43.75, 32.8, 81.23333333333333, 108.78333333333333, 199.28333333333333, 50.28333333333333, 93.5, 70.71666666666667, 131.26666666666668, 33.266666666666666, 72.21666666666667, 57.949999999999996, 53.11666666666667, 9.633333333333333, 34.61666666666667, 69.21666666666667, 72.36666666666667, 87.56666666666666, 65.51666666666667, 50.63333333333333, 59.833333333333336, 58.46666666666667, 100.96666666666667, 78.69999999999999, 98.5], "y0": " ", "yaxis": "y"}, {"alignmentgroup": "True", "box": {"visible": true}, "hovertemplate": "facial_symptom=%{x}<br>Duration (s)=%{y}<extra></extra>", "legendgroup": "True", "marker": {"color": "red"}, "name": "True", "offsetgroup": "True", "orientation": "v", "points": "all", "scalegroup": "True", "showlegend": true, "type": "violin", "x": [true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true], "x0": " ", "xaxis": "x", "y": [71.15, 76.58333333333333, 85.15, 88.96666666666667, 113.93333333333332, 77.89999999999999, 37.86666666666667, 60.93333333333333, 48.06666666666666, 87.73333333333333, 31.1, 33.21666666666667, 90.98333333333332, 43.11666666666666, 73.08333333333333, 87.53333333333333, 60.15, 93.08333333333333, 30.75, 35.55, 60.266666666666666, 73.65, 65.28333333333333, 81.39999999999999, 40.733333333333334, 150.06666666666666, 24.9, 51.983333333333334, 46.58333333333333, 109.88333333333333, 43.61666666666667, 54.38333333333333, 65.78333333333333, 23.683333333333334, 68.1, 44.45, 45.416666666666664, 89.26666666666668, 70.18333333333334, 49.916666666666664, 59.81666666666666, 99.64999999999999, 98.91666666666667, 15.85, 116, 75.23333333333333, 45.266666666666666, 63.88333333333333, 92.05, 52.6, 64.45, 98.75, 66.51666666666667, 80.86666666666667, 80.08333333333333, 80.89999999999999, 75.10000000000001, 93.25, 60.483333333333334, 94.65, 44.53333333333333, 69.7, 79.39999999999999, 59.766666666666666, 75.16666666666666, 50.61666666666667, 60.36666666666667, 46.4, 54.61666666666667, 55.14999999999999, 70.66666666666667, 39.916666666666664, 87.35, 39.516666666666666, 83.48333333333333, 37.45, 36.083333333333336, 65.81666666666666, 49.96666666666667, 38.21666666666667, 120.01666666666665, 70.3, 71.08333333333333, 49.66666666666667, 66.16666666666666, 58.8, 162, 42.36666666666667, 111.08333333333333, 48.18333333333334, 58.46666666666667, 72.03333333333333, 70.23333333333333, 90.65, 68.86666666666667, 56.833333333333336, 50.28333333333334, 64.91666666666666, 124.51666666666667, 98.91666666666667, 60.5, 78.61666666666667, 60.1, 63.28333333333333, 49.63333333333333, 47.516666666666666, 63.233333333333334, 93.96666666666667, 44.31666666666666, 86.83333333333333, 52.68333333333334, 90.08333333333333, 58.11666666666667, 46.75, 59.25, 39.13333333333333, 67.66666666666666, 90.35, 225.78333333333333, 93.11666666666666, 75.43333333333334, 68.26666666666667, 58.416666666666664, 73.53333333333333], "y0": " ", "yaxis": "y"}], "layout": {"legend": {"title": {"text": "facial_symptom"}, "tracegroupgap": 0}, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "title": {"text": "Violin Plot of Duration (s)"}, "violinmode": "overlay", "xaxis": {"anchor": "y", "categoryarray": [false, true], "categoryorder": "array", "domain": [0, 1], "title": {"text": "facial_symptom"}}, "yaxis": {"anchor": "x", "domain": [0, 1], "title": {"text": "Duration (s)"}}}}}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["t-statistic: 25.118516961030902, p-value: 9.832730066423198e-68\n"]}, {"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"alignmentgroup": "True", "box": {"visible": true}, "hovertemplate": "facial_symptom=%{x}<br>Min_EAR=%{y}<extra></extra>", "legendgroup": "False", "marker": {"color": "blue"}, "name": "False", "offsetgroup": "False", "orientation": "v", "points": "all", "scalegroup": "True", "showlegend": true, "type": "violin", "x": [false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false], "x0": " ", "xaxis": "x", "y": [0.2982802821614321, 0.0140458578325284, 0.0388732111987413, 0.049358664716536, 0.0288850030485369, 0.0674461851273284, 0.0296144767774148, 0.0878110530886265, 0.0273891747625722, 0.0126656080804325, 0.1087580990872784, 0.0292922221985605, 0.0738838551900073, 0.2644090487254195, 0.0531719543049024, 0.2022251458737123, 0.0381957030142951, 0.1017998640875548, 0.0270349060947712, 0.0500423573410535, 0.0270381189120838, 0.0334768528726749, 0.0101508295034804, 0.0178361519270438, 0.0879381945592582, 0.0432883887818618, 0.0068392479363969, 0.0148588626117067, 0.0228561227016412, 0.1120208490661329, 0.03146073769484, 0.0603416330488909, 0.0389508505247347, 0.0239965406369202, 0.0231186969699899, 0.0324576179722365, 0.1467725904181957, 0.01078067017339, 0.0228306252830379, 0.0253630620746989, 0.0461590885811518, 0.027478965525786, 0.1931409512712726, 0.2644090487254195, 0.0633462021632687, 0.0390414113738036, 0.074979696646231, 0.1747899451204566, 0.0336143626870373, 0.0706269708417676, 0.1025552323704197, 0.2957477901641705, 0.1078311033067446, 0.041641057712157, 0.3406374720438453, 0.1560706878099451, 0.1475694574871012, 0.0352581800735599, 0.0597230211420135, 0.041767931902132, 0.0494131852887133, 0.1803121242775955, 0.0478978376237354, 0.0634266549302, 0.0122696911117737, 0.0594832973603383, 0.055851409159704, 0.0474033987526419, 0.0557091828319501, 0.1025371009947303, 0.0151187840189057, 0.2044786863053286, 0.3286215494721014, 0.1638442215947391, 0.1026328379684095, 0.1357352830474241, 0.1780469257296122, 0.0662244131408355, 0.0608610865699181, 0.22495956494406, 0.0153844201407592, 0.0338044694965897, 0.2580341399008397, 0.1069139704963561, 0.0527178203487449, 0.0572032463061764, 0.0665611708454879, 0.006502209028557, 0.1508817505533061, 0.0190257222291938, 0.1023730325672912, 0.0462270788784363, 0.3379970744933954, 0.1616653021572379, 0.0663271492261025, 0.2860524705096728, 0.0996684667134196, 0.0642390186800321, 0.1004406590364038, 0.3377418258011279, 0.0634946322205786, 0.0398104477928518, 0.1072326254489614, 0.0241139294239806, 0.201123903566707, 0.0522061376576603, 0.3327106735090194], "y0": " ", "yaxis": "y"}, {"alignmentgroup": "True", "box": {"visible": true}, "hovertemplate": "facial_symptom=%{x}<br>Min_EAR=%{y}<extra></extra>", "legendgroup": "True", "marker": {"color": "red"}, "name": "True", "offsetgroup": "True", "orientation": "v", "points": "all", "scalegroup": "True", "showlegend": true, "type": "violin", "x": [true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true], "x0": " ", "xaxis": "x", "y": [0.2977919832468849, 0.1246455809711826, 0.114341445329426, 0.2909241817678434, 0.2584684498211865, 0.3310696318592881, 0.0925274079191373, 0.257775999585755, 0.0782619959687366, 0.016620487052811, 0.3161821905416799, 0.0284985619560663, 0.0525745541644416, 0.1643628275504796, 0.350392636095415, 0.241154114012256, 0.2003532620305232, 0.1288470713174148, 0.3405676984344492, 0.3286903574130274, 0.2051415028107202, 0.030740371624454, 0.2354774471795197, 0.1185828055975393, 0.0410054401016973, 0.2231534814263794, 0.116687804336016, 0.069887909427233, 0.0660565626618723, 0.1829906637567054, 0.3599489836681221, 0.1836797639408177, 0.046456219083579, 0.1601846654379726, 0.3273660754400252, 0.3661006074369684, 0.0639199497656561, 0.1744275941960841, 0.4019411926658624, 0.0405601294101352, 0.2663200538759397, 0.2086639290466148, 0.1827945006580884, 0.1560858746706548, 0.0632368084271789, 0.0307492885923981, 0.2054299912263283, 0.321520998893769, 0.0701346252860416, 0.1234857985229578, 0.150352413633189, 0.1648965071442081, 0.0166513742407779, 0.0358561140973275, 0.2862117320929072, 0.0774048638755139, 0.0482136193822999, 0.1529113396198197, 0.1578393778185396, 0.1955347393190127, 0.2295795086189014, 0.0969725690071873, 0.0819136155687835, 0.0691659072080622, 0.0640117493785019, 0.2211345090003975, 0.2487305043713835, 0.0944157657520648, 0.0582566449309312, 0.4359120804062668, 0.0317505629189461, 0.3576045568673423, 0.2740180325108911, 0.333953290369992, 0.0528125434777233, 0.3266728963960265, 0.1828203520834278, 0.040361715809578, 0.292281767556131, 0.1900078315535343, 0.1587782979984752, 0.1106333766059064, 0.0719491001117065, 0.1899376550489559, 0.3084601465864061, 0.044734437293898, 0.0968552423095241, 0.0610311072927486, 0.0670021026544445, 0.0430455356985256, 0.2110456874253917, 0.3402573031960605, 0.3552858884071532, 0.2711583509627246, 0.0386175926030388, 0.2031946009434505, 0.0414793482920671, 0.2863570695650414, 0.3056770559978755, 0.1594118622041878, 0.120787222685294, 0.1606161558374799, 0.0335350312003485, 0.1582409608482289, 0.0235337874160309, 0.0705283767584287, 0.1309784194067327, 0.0482014988471042, 0.0374679278708001, 0.1748188861554122, 0.2339518278041598, 0.3185550063273933, 0.3207160060479692, 0.1936773110531808, 0.0188242278809911, 0.2014781120871687, 0.0723518124422904, 0.1833684990952828, 0.0271151482903244, 0.1256145674349523, 0.2674607000413501, 0.0061134228544167, 0.1559274699716151, 0.1966490175410237], "y0": " ", "yaxis": "y"}], "layout": {"legend": {"title": {"text": "facial_symptom"}, "tracegroupgap": 0}, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "title": {"text": "Violin Plot of Min_EAR"}, "violinmode": "overlay", "xaxis": {"anchor": "y", "categoryarray": [false, true], "categoryorder": "array", "domain": [0, 1], "title": {"text": "facial_symptom"}}, "yaxis": {"anchor": "x", "domain": [0, 1], "title": {"text": "Min_EAR"}}}}}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["t-statistic: -2.493792029514635, p-value: 0.013345689353070496\n"]}, {"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"alignmentgroup": "True", "box": {"visible": true}, "hovertemplate": "facial_symptom=%{x}<br>Max_EAR=%{y}<extra></extra>", "legendgroup": "False", "marker": {"color": "blue"}, "name": "False", "offsetgroup": "False", "orientation": "v", "points": "all", "scalegroup": "True", "showlegend": true, "type": "violin", "x": [false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false], "x0": " ", "xaxis": "x", "y": [0.6884919266839351, 0.660285670623509, 0.5758606026294981, 0.5507605323962654, 0.6225918383017068, 0.7235279299778693, 0.5958504709195953, 0.5647139761383102, 0.74411340824526, 1.833361739586723, 0.7830359170228836, 0.4809238745843103, 0.6202622085584314, 0.744965833115672, 0.6224762047383288, 0.5874291508369901, 0.622891944989069, 1.0544707806796303, 0.6393680578964003, 0.6846439106895714, 0.6955211967744568, 0.8038299427640356, 0.599656457209405, 0.7270583361251455, 0.6274549155602965, 0.722759008282991, 0.7110879597966551, 1.1192931302394384, 0.5899666572676885, 0.7447914337345003, 0.5907517826598759, 1.146469861592214, 0.6185091588435923, 0.6817817690414859, 1.1484082789782248, 0.6178205658279607, 0.6905324412794049, 0.9989443864111984, 0.7143896020730667, 0.7433444456588605, 0.9393083982544446, 0.5761307788161846, 0.7241866447940655, 0.744965833115672, 0.6427048989487523, 0.8753775318007637, 0.7309234777179034, 0.6422853220997038, 0.5948293054639686, 0.5904313896602467, 0.6468397128117525, 0.5062967759061291, 0.6368825620426244, 0.8439914151935939, 0.7396235786401998, 0.6193840488899929, 0.6744450798933084, 0.4781751097394006, 0.564713675803135, 0.5038438980828135, 0.4567492628526158, 0.3486513745668991, 0.5179075706949827, 0.7191895363621934, 0.5196524362185302, 0.7666337374234335, 0.6769246995537535, 0.7288816156376807, 0.7076107108726424, 1.0987545009454638, 0.7847062190245966, 0.8517009035432842, 0.5836672226045401, 0.6450605354993783, 0.849293536821917, 0.753182158641738, 0.6793352938514604, 0.8069680806551975, 0.8223403033799273, 0.680803472830378, 0.4389920713261154, 0.7870854108433608, 0.6524924253926536, 0.5889208177536704, 0.8345202184389403, 0.6505893786907757, 1.2309993845032383, 0.4887301487403723, 0.7467850759296428, 0.6164363107531674, 1.344794356347225, 0.5979110274031848, 0.6807841818294709, 0.6741580486322489, 0.7074694076276901, 0.5746003093770752, 1.0200217257278466, 0.7573798890109211, 0.841758528043977, 0.6524156842698854, 0.7444176584996833, 0.9648888030980624, 0.7199485044343037, 0.5663789962924286, 1.0038034638690945, 0.7363684982565184, 0.6651611472336921], "y0": " ", "yaxis": "y"}, {"alignmentgroup": "True", "box": {"visible": true}, "hovertemplate": "facial_symptom=%{x}<br>Max_EAR=%{y}<extra></extra>", "legendgroup": "True", "marker": {"color": "red"}, "name": "True", "offsetgroup": "True", "orientation": "v", "points": "all", "scalegroup": "True", "showlegend": true, "type": "violin", "x": [true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true], "x0": " ", "xaxis": "x", "y": [0.6255763770555633, 0.550866554808987, 0.6698400726615703, 0.7820621903088224, 0.5229548060547081, 0.6509062014689302, 0.5595454344284113, 0.6072367140531916, 0.7016863296920883, 0.453721223391678, 0.629645557326418, 0.6391295120177714, 0.7845648903899253, 0.9690780958978352, 0.7495346643668408, 0.6245607110418887, 0.9395191859209604, 0.7718157530535107, 0.760722847812574, 0.6276814704091519, 0.6251169056947022, 1.0457845218033968, 0.8204002274346862, 1.0670564889716232, 0.6823661028217917, 0.7208410125451004, 0.5373686082596318, 0.8046687252756648, 0.9588926786696595, 1.25094920202885, 0.6532464612200286, 0.5440687360368905, 1.1182671755138114, 0.5766357882767661, 0.6482312608927563, 0.8359462763599579, 0.6794393712074075, 0.6205095558957285, 0.7233530408695283, 0.8555917817969166, 0.6862513019785441, 0.6512312781493228, 0.7507317561075739, 0.4826507820011954, 0.6774785165131872, 0.6449319667708688, 0.8323816304950475, 0.6025556615640364, 0.7493797637335101, 0.6998496167418164, 0.8658510381714135, 1.5603075863155662, 0.4865458390150299, 0.6747414509105762, 0.5858623885861571, 1.0457198656304612, 0.5671413014419029, 0.9383196704851812, 0.9177316147757686, 0.6444921070522057, 0.8635960326718388, 0.826959869890683, 0.5957541571750298, 0.9100888038313591, 0.5435268403878768, 0.9403304304037272, 0.5530792016980784, 0.7268216116614952, 0.8409162037637065, 1.1882686188665097, 0.5246564381921281, 0.7211996423999483, 0.9173815724470696, 0.6300817316348535, 0.601876364869121, 0.5718842897636334, 0.5973968480475746, 0.9223414879418624, 0.5375454432829443, 0.6694181055471718, 0.8498132455214428, 1.0766170528818608, 0.5459859593643408, 0.5165568460949426, 0.7300843602247996, 0.5459547061483393, 0.9478091843409916, 0.5735864023584213, 1.1372251830083733, 0.749133335749261, 0.6724614966555793, 0.639248405852699, 0.7028964751051122, 0.586969408438488, 0.6435378284371296, 0.8543463305629134, 0.6527708935277566, 0.7102563097022457, 0.6830618213816682, 0.7245891398635668, 0.5097834172019301, 0.4361994520175884, 1.0448821546657203, 0.5448066693931083, 0.6422925133223771, 0.6440332034150942, 0.6764611830127242, 0.9212851964601728, 0.6781048201615656, 0.4566511750239485, 0.7549777612517428, 0.6203244146278972, 0.7531349048908341, 0.4731025989153588, 0.6446768488821953, 0.6872517713845816, 0.587621255631589, 0.7298406001226218, 1.138963323675683, 0.6255008758201075, 0.5094681988427392, 0.5609828255992243, 0.7431992316474169, 0.5001458369494458], "y0": " ", "yaxis": "y"}], "layout": {"legend": {"title": {"text": "facial_symptom"}, "tracegroupgap": 0}, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "title": {"text": "Violin Plot of Max_EAR"}, "violinmode": "overlay", "xaxis": {"anchor": "y", "categoryarray": [false, true], "categoryorder": "array", "domain": [0, 1], "title": {"text": "facial_symptom"}}, "yaxis": {"anchor": "x", "domain": [0, 1], "title": {"text": "Max_EAR"}}}}}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["t-statistic: 21.73034485404742, p-value: 1.4394709286492053e-57\n"]}, {"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"alignmentgroup": "True", "box": {"visible": true}, "hovertemplate": "facial_symptom=%{x}<br>Standard Deviation=%{y}<extra></extra>", "legendgroup": "False", "marker": {"color": "blue"}, "name": "False", "offsetgroup": "False", "orientation": "v", "points": "all", "scalegroup": "True", "showlegend": true, "type": "violin", "x": [false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false], "x0": " ", "xaxis": "x", "y": [0.0466501323532309, 0.11887632264103176, 0.0897876873685286, 0.0427401180813011, 0.09791075259535556, 0.08968497811621955, 0.11528179559517254, 0.03598512466332965, 0.08293920104989665, 0.0934128562004397, 0.029759771853234085, 0.04980755435470203, 0.03061383648456656, 0.038570571309614124, 0.08786435794935298, 0.029799913286081885, 0.030775307039325146, 0.07339071938202091, 0.1207789302284397, 0.04995592458750881, 0.07787419372963389, 0.09224847327827068, 0.04301061718991374, 0.10167984607078262, 0.026128159746338343, 0.048480268404979245, 0.10336591436158413, 0.0578057226737601, 0.07188917447226646, 0.07049540786001561, 0.05255182092294525, 0.07778918342294605, 0.09510927259424824, 0.03615648619188103, 0.09387512855823944, 0.03734001672148991, 0.053150465852856654, 0.09923165639119949, 0.13952170823525548, 0.12024113369814447, 0.10786496229107047, 0.08171915346626024, 0.03547028517675337, 0.038570571309614124, 0.08074881657831849, 0.059822330299095686, 0.07166683463449176, 0.042539377798281056, 0.0666483345775951, 0.043075666760841784, 0.08286743031594937, 0.0197924171890624, 0.043679152448957, 0.13628974903795607, 0.04072185181003873, 0.02385703539685813, 0.038441817197032546, 0.0375458367498688, 0.033571206667844786, 0.036168749391962664, 0.0394967566505116, 0.0271541173260724, 0.03871120603030427, 0.09806247169617492, 0.10266557251494579, 0.08077809458842165, 0.08886702056306436, 0.08554158484135392, 0.050422505780296355, 0.0637080362599856, 0.12268046919025251, 0.08464022506108206, 0.03505633770683885, 0.03982554546670447, 0.03524694599061522, 0.025701081531033183, 0.06799073327119026, 0.07615426120247791, 0.06944770791690655, 0.052751963000859, 0.048468084958508746, 0.06150210637843413, 0.03201422301406789, 0.046987099476996315, 0.08125748595204452, 0.04754176958823762, 0.09398519566869035, 0.07318470624876043, 0.05675013391467344, 0.0722270510048979, 0.07502821805023986, 0.06832389916455829, 0.024990618105818263, 0.05493437452156686, 0.05047684142177539, 0.0492331858364696, 0.06247843661256856, 0.041593200905717706, 0.08956668567107232, 0.0284437047572749, 0.12106227388120147, 0.0883262080424818, 0.07126975843351656, 0.08175707685614506, 0.030108986126775532, 0.05584377274634184, 0.0236135649083152], "y0": " ", "yaxis": "y"}, {"alignmentgroup": "True", "box": {"visible": true}, "hovertemplate": "facial_symptom=%{x}<br>Standard Deviation=%{y}<extra></extra>", "legendgroup": "True", "marker": {"color": "red"}, "name": "True", "offsetgroup": "True", "orientation": "v", "points": "all", "scalegroup": "True", "showlegend": true, "type": "violin", "x": [true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true], "x0": " ", "xaxis": "x", "y": [0.027836409682017216, 0.04839421037044256, 0.05207279293281571, 0.03241708620417337, 0.01717889410141797, 0.030125686841905214, 0.06212021742750884, 0.03307828220650777, 0.04351821949653795, 0.04152801584538747, 0.023081424989264642, 0.04131358229594006, 0.039680594019295266, 0.06652442268058861, 0.03027129764144441, 0.027392419555602853, 0.05627589449227244, 0.08779166455771523, 0.036652571224812316, 0.0252213750083151, 0.02938612423463733, 0.04935077411992318, 0.06040289292280167, 0.05267433601884346, 0.030127358053497803, 0.034231724778179075, 0.02782472048861136, 0.07310181118868127, 0.10396684758299435, 0.03980848860582897, 0.020283491650995057, 0.021396816060799042, 0.037138790817384346, 0.03510616133799535, 0.019906831113449958, 0.02705986003217254, 0.03880345698154723, 0.028855940926271584, 0.026558386407697688, 0.0691763652227253, 0.029530055374836987, 0.0307062745345015, 0.03595559489031823, 0.05414985711178199, 0.027203185517776927, 0.05131450266551733, 0.050287754771538254, 0.017613232689511944, 0.08917345089442362, 0.06342972981701941, 0.0784436821509172, 0.06427575575752884, 0.05185162111609284, 0.052738297737719916, 0.021735337536248945, 0.04086359080031919, 0.053664001402834595, 0.05513186338441063, 0.038664218459893436, 0.03213833514642023, 0.03503407764377696, 0.10014138087047963, 0.04203532101390158, 0.07497708853697871, 0.03551365437455323, 0.03073739389369327, 0.02779972376854616, 0.08298657938722329, 0.04060321378331243, 0.04203010622089319, 0.029700217630188826, 0.026949691971432942, 0.03330108948992807, 0.024507491891181416, 0.07418552080857063, 0.018379392770007556, 0.03008164166719664, 0.04579932667509658, 0.023038744174086828, 0.035754556163252085, 0.034726547299519746, 0.04400532937171054, 0.0565755165066562, 0.02784118462781307, 0.027932943404924228, 0.0334652683108913, 0.03620295808093129, 0.03651372268142624, 0.06749524570190475, 0.06715526148518963, 0.029993590787447597, 0.024164636107732673, 0.020279110361446483, 0.025090201117737073, 0.03267858823369973, 0.029682239846102028, 0.02689741889477067, 0.03700905886184746, 0.02538669080659777, 0.024563849290542127, 0.02171917165035783, 0.025480077165285987, 0.08852532721420156, 0.023983396301616545, 0.06456006784241886, 0.07338316041871944, 0.04984405612871402, 0.10522418850263096, 0.0413998361982345, 0.025845425513494728, 0.026497507722349743, 0.017817980810124, 0.045888032089970054, 0.028943743571302784, 0.06850220390680488, 0.04007419647768867, 0.03955942799824422, 0.027650502685553045, 0.058765132820116354, 0.03302915507502126, 0.016444216030774714, 0.05704614134675374, 0.0272676635866991, 0.0273166919617397], "y0": " ", "yaxis": "y"}], "layout": {"legend": {"title": {"text": "facial_symptom"}, "tracegroupgap": 0}, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "title": {"text": "Violin Plot of Standard Deviation"}, "violinmode": "overlay", "xaxis": {"anchor": "y", "categoryarray": [false, true], "categoryorder": "array", "domain": [0, 1], "title": {"text": "facial_symptom"}}, "yaxis": {"anchor": "x", "domain": [0, 1], "title": {"text": "Standard Deviation"}}}}}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["t-statistic: -11.352609362126689, p-value: 5.47051905698352e-24\n"]}, {"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"alignmentgroup": "True", "box": {"visible": true}, "hovertemplate": "facial_symptom=%{x}<br>blink_rate=%{y}<extra></extra>", "legendgroup": "False", "marker": {"color": "blue"}, "name": "False", "offsetgroup": "False", "orientation": "v", "points": "all", "scalegroup": "True", "showlegend": true, "type": "violin", "x": [false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false], "x0": " ", "xaxis": "x", "y": [0.10489510489510488, 0.45086705202312133, 0.46563192904656314, 0.15170670037926676, 0.4545454545454546, 0.44642857142857145, 0.4573170731707317, 0.16129032258064516, 0.35450516986706054, 0.08109934669970714, 0.028804608737397985, 0.10765550239234449, 0.07473841554559042, 0.059654006760787436, 0.3073770491803279, 0.03487358326068003, 0.10661928031985785, 0.2521714766040908, 0.6307490144546649, 0.08241758241758242, 0.22549869904596703, 0.31296572280178836, 0.11787819253438114, 0.3424124513618677, 0.054982817869415807, 0.1381957773512476, 0.28388533259114945, 0.19303557910673733, 0.16933207902163688, 0.2607885749767153, 0.14173228346456693, 0.3176130895091434, 0.2992021276595745, 0.0662739322533137, 0.23579201934703747, 0.11534025374855825, 0.12014417300760913, 0.5245746691871456, 0.7653061224489796, 0.5480817140009966, 0.2518891687657431, 0.38694074969770254, 0.04675324675324675, 0.059654006760787436, 0.4059040590405904, 0.15865384615384617, 0.19115890083632017, 0.039656311962987446, 0.26356589147286824, 0.11162790697674418, 0.6454282167976831, 0.09868421052631579, 0.1889763779527559, 0.5922676172196326, 0.023659305993690854, 0.039190071848465055, 0.0758773316471704, 0.031855588001061856, 0.033660589060308554, 0.04234297812279464, 0.2027027027027027, 0.10398613518197573, 0.11614401858304298, 0.5094339622641509, 0.4863481228668943, 0.24948024948024947, 0.24061873388713834, 0.288135593220339, 0.12596899224806202, 0.03722854188210962, 0.3596403596403596, 0.16793282686925232, 0.035314891112419075, 0.05231037489102005, 0.04006231916314267, 0.039586540576204095, 0.2896725440806045, 0.16492578339747113, 0.19729206963249518, 0.19639934533551553, 0.09024064171122995, 0.10307064633884475, 0.045714285714285714, 0.15243902439024393, 0.23389413212966761, 0.1746591083192891, 0.34122271472777455, 0.4574080212131256, 0.10695187165775401, 0.3111006363422107, 0.19045200609446417, 0.3907815631262525, 0.01384721901684745, 0.27610008628127697, 0.13178537809852525, 0.10380622837370243, 0.20221473278767452, 0.07223693715386467, 0.35928143712574845, 0.011419870574800152, 0.2442126685321801, 0.37524687294272546, 0.10027855153203342, 0.427594070695553, 0.019808517662594914, 0.11435832274459977, 0.01015228426395939], "y0": " ", "yaxis": "y"}, {"alignmentgroup": "True", "box": {"visible": true}, "hovertemplate": "facial_symptom=%{x}<br>blink_rate=%{y}<extra></extra>", "legendgroup": "True", "marker": {"color": "red"}, "name": "True", "offsetgroup": "True", "orientation": "v", "points": "all", "scalegroup": "True", "showlegend": true, "type": "violin", "x": [true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true], "x0": " ", "xaxis": "x", "y": [0.014054813773717497, 0.026115342763873776, 0.09395184967704051, 0.056200824278756084, 0.008777062609713284, 0.012836970474967908, 0.15845070422535212, 0.01641137855579869, 0.062413314840499313, 0.022796352583586626, 0.03215434083601286, 0.15052684395383842, 0.07693716797948344, 0.04638577502899112, 0.013683010262257697, 0.011424219345011425, 0.0831255195344971, 0.4834377797672337, 0.032520325203252036, 0.028129395218002815, 0.033185840707964605, 0.08146639511201628, 0.16849629818738832, 0.049140049140049144, 0.049099836333878884, 0.006663705019991115, 0.040160642570281124, 0.23084321898044244, 0.40787119856887305, 0.02730168360382224, 0.02292701566679404, 0.018387986515476556, 0.03040283759817583, 0.0422237860661506, 0.014684287812041117, 0.02249718785151856, 0.08807339449541285, 0.02240477968633308, 0.014248397055331274, 0.06010016694490818, 0.033435497353023126, 0.020070245860511794, 0.050547598989048016, 0.06309148264984227, 0.017241379310344827, 0.14621178555604786, 0.044182621502209134, 0.015653535090007827, 0.07604562737642585, 0.34220532319391633, 0.2482544608223429, 0.04050632911392405, 0.10523678276121273, 0.0741962077493817, 0.012486992715920917, 0.03708281829419036, 0.14647137150466044, 0.17158176943699732, 0.03306696059520529, 0.03169572107765452, 0.02245508982035928, 0.1291248206599713, 0.08816120906801009, 0.18404907975460122, 0.0665188470066519, 0.059269015475798485, 0.016565433462175594, 0.2801724137931035, 0.1281660054928288, 0.018132366273798734, 0.07075471698113207, 0.025052192066805846, 0.03434459072696051, 0.025305778152678194, 0.2156118985825514, 0.026702269692923896, 0.05542725173210161, 0.1215497594327678, 0.0200133422281521, 0.026166593981683382, 0.049993056519927796, 0.02844950213371266, 0.07033997655334115, 0.020134228187919462, 0.015113350125944586, 0.11904761904761905, 0.030864197530864196, 0.07081038552321008, 0.045011252813203305, 0.2282947077135939, 0.03420752565564424, 0.013882461823229986, 0.014238253440911248, 0.011031439602868174, 0.04356243949661181, 0.03519061583577712, 0.03977461054027179, 0.015404364569961491, 0.008031053406505154, 0.020219039595619208, 0.03305785123966942, 0.012719949120203519, 0.3161397670549085, 0.01580194890703187, 0.3022162525184688, 0.14731673097158893, 0.047443331576172906, 0.2873359347286272, 0.1353892440767206, 0.011516314779270634, 0.018981335020563112, 0.011100832562442185, 0.01720676799541153, 0.0213903743315508, 0.4388185654008439, 0.07666098807495741, 0.08866995073891627, 0.03320420586607637, 0.17716099505425556, 0.0644352962233757, 0.013256738842244807, 0.087890625, 0.034236804564907276, 0.013599274705349048], "y0": " ", "yaxis": "y"}], "layout": {"legend": {"title": {"text": "facial_symptom"}, "tracegroupgap": 0}, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "title": {"text": "Violin Plot of blink_rate"}, "violinmode": "overlay", "xaxis": {"anchor": "y", "categoryarray": [false, true], "categoryorder": "array", "domain": [0, 1], "title": {"text": "facial_symptom"}}, "yaxis": {"anchor": "x", "domain": [0, 1], "title": {"text": "blink_rate"}}}}}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["t-statistic: -7.800600011309657, p-value: 2.1683278552786673e-13\n"]}], "source": ["# show each feature in violent graph using plotly\n", "from scipy.stats import ttest_ind\n", "import numpy as np\n", "\n", "for column in comulative_df.columns:\n", "    fig = px.violin(combined_df, y=column, x='facial_symptom', box=True, points=\"all\",color='facial_symptom', title=f'Violin Plot of {column}',color_discrete_map={True: 'red', False: 'blue'})\n", "    fig.show()\n", "    #significance test\n", "\n", "    # Split the data into two groups\n", "    group1 = combined_df[combined_df['facial_symptom']]\n", "    group2 = combined_df[~combined_df['facial_symptom']]\n", "    # Perform the t-test\n", "    t_stat, p_value = ttest_ind(group1[column], group2['blink_rate'])\n", "    print(f\"t-statistic: {t_stat}, p-value: {p_value}\")\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 182, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Accuracy: 0.8723404255319149\n", "Confusion Matrix:\n", "[[ 5  5]\n", " [ 1 36]]\n", "       Blinks  Duration (s)   Min_EAR   Max_EAR  Standard Deviation  \\\n", "ID                                                                    \n", "7137        5     73.983333  0.012666  1.833362            0.093413   \n", "7225        1     57.350000  0.202225  0.587429            0.029800   \n", "7334        3     48.533333  0.050042  0.684644            0.049956   \n", "7855        1     62.783333  0.035258  0.478175            0.037546   \n", "7681        2     64.166667  0.193141  0.724187            0.035470   \n", "22178      23     66.733333  0.015119  0.784706            0.122680   \n", "\n", "       blink_rate  \n", "ID                 \n", "7137     0.081099  \n", "7225     0.034874  \n", "7334     0.082418  \n", "7855     0.031856  \n", "7681     0.046753  \n", "22178    0.359640  \n", "ID\n", "7137     False\n", "7225     False\n", "7334     False\n", "7855     False\n", "7681     False\n", "22178     True\n", "Name: facial_symptom, dtype: bool\n"]}], "source": ["#train random forest model to predict the facial symptom\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import accuracy_score\n", "from sklearn.metrics import confusion_matrix\n", "from sklearn.svm import SVC\n", "\n", "# Split the data into training and testing sets\n", "X = combined_df.drop(columns=['facial_symptom',label_name])\n", "#drop columns where duration <20 or number of blinks ==0\n", "#X = X[X['Duration (s)'] >20]\n", "#X = X.drop(columns=['Duration (s)'])\n", "y = combined_df['facial_symptom']\n", "y = y[y.index.isin(X.index)]\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)\n", "\n", "# Train a random forest classifier\n", "clf = RandomForestClassifier(random_state=42, n_estimators=300)\n", "clf.fit(X_train, y_train)\n", "\n", "# Make predictions\n", "y_pred = clf.predict(X_test)\n", "\n", "# Evaluate the model\n", "accuracy = accuracy_score(y_test, y_pred)\n", "conf_matrix = confusion_matrix(y_test, y_pred)\n", "print(f\"Accuracy: {accuracy}\")\n", "print(f\"Confusion Matrix:\\n{conf_matrix}\")\n", "\n", "#show X of the unmatched data\n", "unmatched_data = X_test[y_test != y_pred]\n", "print(unmatched_data)\n", "print(y_test[y_test != y_pred])\n"]}, {"cell_type": "code", "execution_count": 183, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["     Metric     Value\n", "0  Accuracy  0.770563\n"]}], "source": ["from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.model_selection import LeaveOneOut\n", "from sklearn.metrics import accuracy_score, confusion_matrix\n", "import pandas as pd\n", "import numpy as np\n", "\n", "# Assuming `combined_df` is already loaded\n", "# Drop columns where 'Duration (s)' < 20 or 'number of blinks' == 0\n", "#filtered_df = combined_df[(combined_df['Duration (s)'] > 20)]\n", "filtered_df = combined_df\n", "# Define features and target variable\n", "X = filtered_df.drop(columns=['facial_symptom',label_name])\n", "y = filtered_df['facial_symptom']\n", "#X = X[X['Duration (s)'] >20]\n", "y= y[y.index.isin(X.index)]\n", "# Leave-One-Out Cross-Validation\n", "loo = LeaveOneOut()\n", "y_true, y_pred = [], []\n", "#use scaler\n", "from sklearn.preprocessing import StandardScaler\n", "scaler = StandardScaler()\n", "X = pd.DataFrame(scaler.fit_transform(X))\n", "\n", "\n", "# Initialize the classifier\n", "clf = RandomForestClassifier(random_state=42, n_estimators=300)\n", "\n", "for train_idx, test_idx in loo.split(X):\n", "    X_train, X_test = X.iloc[train_idx], X.iloc[test_idx]\n", "    y_train, y_test = y.iloc[train_idx], y.iloc[test_idx]\n", "\n", "    # Train the model\n", "    clf.fit(X_train, y_train)\n", "    \n", "    # Predict\n", "    y_pred.append(clf.predict(X_test)[0])\n", "    y_true.append(y_test.values[0])\n", "\n", "# Compute accuracy and confusion matrix\n", "loo_accuracy = accuracy_score(y_true, y_pred)\n", "loo_conf_matrix = confusion_matrix(y_true, y_pred)\n", "\n", "# Display results\n", "loo_results = pd.DataFrame({\"Metric\": [\"Accuracy\"], \"Value\": [loo_accuracy]})\n", "\n", "loo_conf_matrix\n", "print(loo_results)\n"]}, {"cell_type": "code", "execution_count": 184, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[ 30  38]\n", " [ 15 148]]\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["print(loo_conf_matrix)\n", "#draw confusion matrix\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "\n", "# Plot the confusion matrix\n", "plt.figure(figsize=(8, 6))\n", "sns.heatmap(loo_conf_matrix, annot=True, fmt='d', cmap='Blues', cbar=False)\n", "plt.xlabel('Predicted Label')\n", "plt.ylabel('True Label')\n", "plt.title('Confusion Matrix')\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 185, "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"customdata": [[6295], [7137], [7143], [7152], [7155], [7198], [7225], [7242], [7284], [7288], [7334], [7415], [7417], [7452], [7477], [7488], [7501], [7518], [7536], [7555], [7566], [7571], [7578], [7580], [7587], [7597], [7609], [7610], [7628], [7631], [7634], [7640], [7652], [7677], [7681], [7706], [7709], [7727], [7735], [7755], [7762], [7771], [7776], [7795], [7814], [7819], [7822], [7852], [7855], [7884], [7888], [7945], [7985], [7987]], "hovertemplate": "Label=False<br>PC1=%{x}<br>PC2=%{y}<br>ID=%{customdata[0]}<extra></extra>", "legendgroup": "False", "marker": {"color": "#636efa", "symbol": "circle"}, "mode": "markers", "name": "False", "orientation": "v", "showlegend": true, "type": "scatter", "x": [3.5112722929118863, 1.7605766239128948, -0.7868215139798282, 0.5081822351800036, -0.09197173225396954, 2.371558815602983, -1.231975722249945, -0.24170519873706722, 2.0064172269822986, 5.067490967306019, 0.3826322805292009, -2.083835984098911, 2.6083488682644713, 0.5202536264549874, -1.0457120823439439, 3.2889433208805543, -0.7092434973427656, 0.3375665962109328, 2.8940039498085244, 1.5812002766017579, 1.3774004013323755, -0.3304317418357676, 1.702835097023171, 0.7487642190634999, 2.871640914074425, 2.5249107112009663, 0.11386198286804158, 2.2054620979266093, 0.33780795598509034, 4.834968697091323, 5.841163613939572, 6.000032828012636, 2.6473677796256743, 2.6491117055025892, -0.5574761153821479, -0.9745302624754193, 2.9106651525301186, 1.3300735867647262, 1.309329820495982, -0.8410625390694492, 1.9715106996855043, -0.043298127786802344, 3.8481300129587943, 0.33672997622408213, 5.500612638281522, -1.138354873800298, -1.1081076504126277, -0.21033578957609725, -0.21418239296822225, -0.3109688315996335, -0.5612123130708433, 0.24800290701463581, 3.892863389535746, 3.5517886598577073], "xaxis": "x", "y": [-1.7107631735950108, 3.6946686673048355, 0.9720845222278914, -1.5323383308710787, -0.7823855639126498, -1.1943417813808832, -0.8662826124017714, -1.372061907735031, 0.5477772142165328, -1.0393255940790265, -1.052803215316291, -0.10932165597272948, -0.7285047389878606, -1.5350087138369906, -0.2571215249824084, -0.35822342222522247, -0.37887728550371547, -0.8528444095288437, -0.5598343693614137, 1.7475566216982765, -1.2114114639372262, 0.3989877394069488, -0.6429302237238543, -1.5440629106093384, 1.1513620867757628, -1.160938948450564, -1.1853390046189052, 0.9465424727368618, -1.4473542398372492, 0.7832554201864851, -1.2092943971199959, 1.03599644135952, 1.0274158034000127, -1.6128143513755406, -0.45615690119833835, 0.41361017414521273, -0.9616751915902211, 0.1768684599900123, -1.0528896894155946, -0.8631923832423231, -0.8508789520024679, -0.9911672126527411, -1.4218985777335336, -0.84051224307099, 0.02341384642827383, -0.9099138431992456, -0.2800615911606784, -0.9431703433165909, -1.3767316192883419, -1.1766162877829418, -1.478111198123065, -1.7740250324141462, -0.7182892420501504, -1.8646109550032985], "yaxis": "y"}, {"customdata": [[10039], [13390], [13484], [14555], [15377], [15813], [16219], [16827], [16883], [17000], [17200], [17202], [17434], [17599], [17980], [18198], [18317], [18372], [19015], [19091], [19124], [20959], [21128], [21401], [21696], [22178], [22224], [22227], [23160], [23284], [24192], [24318], [24352], [24475], [24601], [24622], [24757], [24798], [24860], [24889], [25260], [25352], [25412], [25533], [25779], [25793], [25934], [25957], [26145], [26326], [26407], [26692], [27423], [27425], [28225], [28306], [28321], [28350], [28408], [28411], [28498], [28550], [28615], [28630], [28641], [28715], [28726], [28731], [28813], [29079], [29157], [29377], [29570], [30009], [30104], [30148], [30201], [30279], [30414], [30428], [30593], [30893], [30961], [30982], [31092], [31100], [31182], [31231], [31240], [31318], [31319], [31437], [31457], [31769], [31848], [31961], [32160], [32200], [32282], [32424], [32519], [32541], [32594], [32853], [33023], [33151], [33164], [33430], [33527], [33661], [33749], [33927], [34142], [34417], [34492], [34509], [34802], [34914], [34965], [35080], [35154], [35246], [35623], [35747], [35816], [36220], [36297], [36407], [36436], [36532], [36564], [36581], [36660], [36880], [38046], [38050], [38073], [38100], [38141], [38210], [38215], [38255], [38256], [38519], [38902], [39200], [39274], [39528], [39685], [39686], [39712], [40046], [40550], [40613], [40771]], "hovertemplate": "Label=True<br>PC1=%{x}<br>PC2=%{y}<br>ID=%{customdata[0]}<extra></extra>", "legendgroup": "True", "marker": {"color": "#EF553B", "symbol": "circle"}, "mode": "markers", "name": "True", "orientation": "v", "showlegend": true, "type": "scatter", "x": [-1.5400405697849906, -2.2626635688262606, -2.154046070634383, 0.02134199463030025, -1.8386850790538574, -0.6743927586099356, -1.5996403269761372, 2.4409160503889713, -2.1373058853381033, -0.1606632287100753, -0.3826648823454643, 2.1275858019652976, -0.4874157099835539, -2.150491361181596, -2.2789727653120164, -0.5584307330385604, 2.4458561675171615, 3.5864802418456336, -1.8881153323415172, 1.172172021727613, -2.161109042936924, -1.5987471873282153, 0.01855580601830065, -0.08456705528297204, 0.11402733524827766, 3.8846244787456268, -0.4400266007665073, 1.3610287049080536, -0.8928272859847147, -1.7353466551728383, -0.7966344479025564, -1.3648940659359488, -0.4680259920190076, 1.0062198946665344, -0.9148114117269921, 1.5590355135078366, 2.6604150009223284, -0.9431416619342518, -2.372626906378551, -1.8187509878266044, -0.5924462272132838, -1.3518468994510238, -2.6810377575845736, -2.155516379800744, -0.5831161431077367, -1.5648637541287744, 1.4613482086061678, -2.414833225351555, 0.0231336662854392, -1.773903882556771, -1.650266895285299, -1.501783027649086, -1.2051821695451066, 0.2800065583094446, -1.0304104798096183, -2.7245609435600735, 0.4456036801984007, 0.8959218610466391, 0.9714181497238912, 1.113591730853077, -0.8510593372106556, -0.4639459159193702, -0.5672116061019417, -2.1941552084117872, 0.3688599973880132, -0.6183654709655393, -0.15109503612216876, 2.3140372408459084, 0.43180171055363054, 1.3307261388480278, -1.0738074162155662, -1.8494392956154997, -1.5045001658979422, 0.9070289121475417, -0.5144290006770931, 0.8921245869155652, 2.7418564807976695, -0.778443809401576, -1.2816987827367632, -1.9375432615223056, 0.17984422886875612, 1.277573291721282, 2.1924357011886, -0.14323952819259342, -1.9280561345112732, -0.7648179094017521, -2.1871956866160804, -1.5547297836818068, -2.204716633943158, 1.1907259417881624, -2.322584095804641, -1.4265928120735514, 2.4643324194137364, 0.08774598382974916, 1.8480067769326347, -2.1695569162512673, -1.8821752166210637, 0.9811276714042028, -1.819141128566376, -0.9809334633935884, -0.7307373535360637, 0.3043761503761035, -0.3982289595025443, -2.0930859273119036, -2.045787279556485, -0.44885904419562894, -0.8258883067443615, -0.7902595281614495, 0.07394063400314296, 0.8991582621840808, 0.8620671446959167, -1.5734454531154864, -2.295758485441494, -2.378130206337528, -2.0921467947520926, -1.2179386210413246, -1.4313114119400447, -1.0133873590008406, -1.8093772464640785, -2.1893162675879823, -1.541149718159799, -1.5141868708359858, -2.1345437189744754, 2.2204010358375137, -0.14221049992551266, 3.101219787491489, -1.8572848276420435, -1.6965901749082848, 1.3110626462225377, 0.36731446038423937, -0.8253397835832441, 2.6871341173851393, -0.1757505465719468, -1.812634727599825, -1.7661322446059868, -2.3737503941178573, -1.7277874842978902, -1.7423643518679206, 2.4305174740894633, 2.485095690654141, -1.49370513738195, -0.5711667363053694, -1.4620390955898495, 2.2223424777230183, -0.9630602677937078, -2.26701337056995, 3.0816948406442624, -0.03142197926717807, 0.3867296085689012, 2.8824507304767213, -1.3828217165244, -1.816519959538265, -1.0491420316306233, 0.49417532165600186, -1.9349330341957829], "xaxis": "x", "y": [0.9030744837333459, 0.6376558720164334, 0.17072470114207874, -1.2762419929890976, -0.4488037343340346, -0.5501171531975598, 0.10941402653879526, 0.4589756023710009, -1.1542788819740852, -1.2109018045857782, 0.8753694939213088, -0.7289786611655481, 0.27454392750411566, 0.3777003628591396, 0.5162989679863087, 0.6408546329509989, -0.5373002246752844, 1.2839150618422033, -0.6996323264184443, 3.692054153761196, -1.0317878811800443, -0.436699258867774, 1.2657508546009582, 0.434287927497401, 1.4388193535234415, -0.05322634622127861, 1.579994927765746, 0.6267906115708474, -0.8498430778725157, 2.241813787578601, -1.1924615678129706, -1.7145095022682528, 2.424565606685386, -0.05925781839442664, 0.1318571030985463, 0.1907814491783216, 0.32149526710175624, 2.938530047643282, -0.7260649060970261, -0.8835170209173014, 1.2635628069837792, -1.5861364126994806, 0.11410491309876979, -0.09037445936224768, -0.7118714678836413, 0.29767308979375573, 0.7611685321657345, 0.23464374248602984, 0.02857859861219535, -0.2180940406988498, 0.6908493303857167, 1.2454200685032453, 1.1294998216830259, 0.022270572621887884, -0.12266116181266096, -0.15313649546127758, 0.8774559910680709, -0.14398904932315645, 0.10063539072777981, 0.5649093222203028, 4.155136552412799, -0.534157392835721, 0.4779468804334489, -0.02091426825754446, -1.7962436620411997, 1.4514952536482575, 0.00326274515239801, 0.4622129145374194, 1.5806462905626864, 0.573463734486904, 0.521514310621433, 0.7685903644950667, -0.0506846160722783, 0.568555849287873, -0.03760842116639463, 0.5149050400021433, -1.4077286858825162, -0.3532606682482936, 0.3507154843680878, -0.6544200804607535, 0.6886294653972299, -0.45697838039931354, -0.34633087605777463, 0.07370793032564207, 1.4067859732343688, -0.5611626109997481, -0.5916046944118302, 1.283873038400022, -0.9167290424286468, 0.20021219942843863, -1.1762780407383255, -1.186489360003029, 3.5890602204844835, 0.6545121100951918, -1.5890226618212158, -0.9661046030833421, -0.11295977945539518, -0.5172030830446285, -0.6460995061344766, 1.8962408156292099, 1.2885853219867767, -0.6140823756159575, -0.41674693306998445, -0.8570595621635567, 0.10814880816508307, -0.7958353832365986, 3.284754866575317, -1.1458821077670498, 2.603161116251562, -0.3774377956546387, -0.09341125702763918, -0.3252586909875442, -0.026641737555189633, 0.1359758749487697, 0.25765962449837015, 0.007952324936519522, 0.22393720477061071, -0.6952074500836096, 0.014271051165831421, 1.4759907521267108, 0.8784699099772797, -0.8624070295074457, -0.3776814224658346, 0.9912531202745412, -0.029027251269541834, 0.3012346728263255, 0.2017734966645202, -0.6549737114490247, -0.7001963396172167, -0.7141981295114505, -0.1890996884724186, 1.6568436160695998, -0.7602457871378897, -0.3154774280100689, -0.20348615847876717, 0.3634571700849633, 0.007261282980516783, -1.3008615991552104, -0.37194363232754574, -0.0029766585106507405, -0.5604022005380335, -0.3956275494920445, 0.6944083757751804, 6.0254858980749635, 0.42771177159751994, -0.411241926966139, -0.01974769111088411, -0.47045519407615927, -0.33445343013245066, -0.8598484971008346, -0.1256064481007267, -0.5083444863208932, 1.6501821260327112, 0.19674864637454267, 0.5195022570169715], "yaxis": "y"}], "layout": {"legend": {"title": {"text": "Label"}, "tracegroupgap": 0}, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "title": {"text": "PCA Visualization"}, "xaxis": {"anchor": "y", "domain": [0, 1], "title": {"text": "PC1"}}, "yaxis": {"anchor": "x", "domain": [0, 1], "title": {"text": "PC2"}}}}}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "import pandas as pd\n", "import numpy as np\n", "import plotly.express as px\n", "from sklearn.decomposition import PCA\n", "from sklearn.preprocessing import StandardScaler\n", "\n", "# Load your dataset\n", "# combined_df = pd.read_csv(\"your_file.csv\")  # Uncomment this to load your dataset\n", "\n", "# Drop non-numeric columns and apply filtering\n", "filtered_df = combined_df[(combined_df['Duration (s)'] > 20)].copy()\n", "\n", "# Store IDs for tooltips\n", "filtered_df['ID'] = filtered_df.index\n", "filtered_df.dropna(inplace=True)\n", "\n", "# Select features (exclude non-numeric columns like labels)\n", "features = filtered_df.drop(columns=['facial_symptom', 'ID'])  # Exclude categorical or target columns\n", "\n", "# Standardize the features\n", "scaler = StandardScaler()\n", "X_scaled = scaler.fit_transform(features)\n", "\n", "\n", "# Apply PCA\n", "pca = PCA(n_components=2)  # Reduce to 2 dimensions for visualization\n", "principal_components = pca.fit_transform(X_scaled)\n", "\n", "# Create a DataFrame with PCA results\n", "pca_df = pd.DataFrame(data=principal_components, columns=['PC1', 'PC2'])\n", "pca_df.index = filtered_df.index\n", "pca_df['ID'] = filtered_df['ID']\n", "pca_df['Label'] = filtered_df['facial_symptom']  # Assuming 'facial_symptom' is categorical\n", "\n", "# Plot with Plotly\n", "fig = px.scatter(\n", "    pca_df, \n", "    x='PC1', \n", "    y='PC2', \n", "    color='Label', \n", "    hover_data=['ID'], \n", "    title=\"PCA Visualization\"\n", ")\n", "fig.show()\n"]}, {"cell_type": "code", "execution_count": 170, "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"alignmentgroup": "True", "box": {"visible": true}, "hovertemplate": "label #3=%{x}<br>blink_rate=%{y}<extra></extra>", "legendgroup": "", "marker": {"color": "#636efa"}, "name": "", "offsetgroup": "", "orientation": "v", "points": "all", "scalegroup": "True", "showlegend": false, "type": "violin", "x": [0, 1, 1, 0, 0, null, 0, 1, 1, 0, 0, 0, null, 2, 0, 0, 2, 0, 1, 1, 0, 1, 0, 2, 0, 0, 0, 0, 0, 1, 0, null, 0, 0, 0, 1, 0, 0, 1, 0, 0, 0, 1, 0, 1, 0, 1, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 2, 2, 2, 2, 2, 2, 4, 0, 2, 2, 2, 0, 2, 2, 3, 2, 0, 2, 2, 1, 2, 2, 2, 2, 1, 0, 2, 0, 2, null, 2, 1, 2, 1, 2, 1, 1, 2, 2, 2, 2, 2, 2, 3, null, 2, 2, 2, 1, 2, 2, 2, 2, null, 3, null, 2, 2, 2, 3, 2, 3, 0, 2, 4, 3, 3, 2, null, 0, 2, 3, 0, 2, 0, 2, 3, 2, null, 2, 2, 2, 1, 2, 2, 2, 1, 2, 1, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0, 2, 1, 2, 1, 1, 3, 2, 2, 1, 2, 3, 2, 2, 2, 2, 2, 2, 1, 2, 2, 2, 2, 3, 2, 2, 2, 2, 2, 2, 3, 2, 1, 0, 1, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 1, 3, 2, 2, 2, 2, 2, 0, 2, 1, 1, 2, 2, 1, 1, 1], "x0": " ", "xaxis": "x", "y": [0.45086705202312133, 0.08109934669970714, 0.028804608737397985, 0.10765550239234449, 0.07473841554559042, 0.059654006760787436, 0.3073770491803279, 0.03487358326068003, 0.10661928031985785, 0.2521714766040908, 0.6307490144546649, 0.08241758241758242, 0.22549869904596703, 0.014054813773717497, 0.31296572280178836, 0.11787819253438114, 0.026115342763873776, 0.3424124513618677, 0.054982817869415807, 0.1381957773512476, 0.28388533259114945, 0.19303557910673733, 0.16933207902163688, 0.09395184967704051, 0.2607885749767153, 0.14173228346456693, 0.3176130895091434, 0.2992021276595745, 0.0662739322533137, 0.23579201934703747, 0.11534025374855825, 0.12014417300760913, 0.5245746691871456, 0.7653061224489796, 0.5480817140009966, 0.2518891687657431, 0.38694074969770254, 0.04675324675324675, 0.059654006760787436, 0.4059040590405904, 0.15865384615384617, 0.19115890083632017, 0.039656311962987446, 0.26356589147286824, 0.11162790697674418, 0.6454282167976831, 0.1889763779527559, 0.5922676172196326, 0.023659305993690854, 0.039190071848465055, 0.0758773316471704, 0.031855588001061856, 0.033660589060308554, 0.04234297812279464, 0.11614401858304298, 0.5094339622641509, 0.4863481228668943, 0.056200824278756084, 0.008777062609713284, 0.012836970474967908, 0.15845070422535212, 0.01641137855579869, 0.062413314840499313, 0.022796352583586626, 0.24948024948024947, 0.03215434083601286, 0.15052684395383842, 0.07693716797948344, 0.24061873388713834, 0.04638577502899112, 0.013683010262257697, 0.011424219345011425, 0.0831255195344971, 0.288135593220339, 0.4834377797672337, 0.032520325203252036, 0.12596899224806202, 0.028129395218002815, 0.033185840707964605, 0.08146639511201628, 0.16849629818738832, 0.03722854188210962, 0.3596403596403596, 0.049140049140049144, 0.16793282686925232, 0.049099836333878884, 0.035314891112419075, 0.006663705019991115, 0.05231037489102005, 0.040160642570281124, 0.04006231916314267, 0.23084321898044244, 0.039586540576204095, 0.2896725440806045, 0.40787119856887305, 0.02730168360382224, 0.02292701566679404, 0.018387986515476556, 0.03040283759817583, 0.0422237860661506, 0.014684287812041117, 0.16492578339747113, 0.02249718785151856, 0.08807339449541285, 0.02240477968633308, 0.19729206963249518, 0.014248397055331274, 0.06010016694490818, 0.033435497353023126, 0.020070245860511794, 0.19639934533551553, 0.050547598989048016, 0.09024064171122995, 0.017241379310344827, 0.14621178555604786, 0.044182621502209134, 0.015653535090007827, 0.07604562737642585, 0.34220532319391633, 0.10307064633884475, 0.2482544608223429, 0.04050632911392405, 0.10523678276121273, 0.0741962077493817, 0.012486992715920917, 0.045714285714285714, 0.15243902439024393, 0.03708281829419036, 0.14647137150466044, 0.23389413212966761, 0.17158176943699732, 0.1746591083192891, 0.03306696059520529, 0.03169572107765452, 0.02245508982035928, 0.34122271472777455, 0.1291248206599713, 0.08816120906801009, 0.18404907975460122, 0.4574080212131256, 0.0665188470066519, 0.059269015475798485, 0.016565433462175594, 0.10695187165775401, 0.2801724137931035, 0.3111006363422107, 0.1281660054928288, 0.018132366273798734, 0.07075471698113207, 0.025052192066805846, 0.03434459072696051, 0.025305778152678194, 0.2156118985825514, 0.026702269692923896, 0.05542725173210161, 0.19045200609446417, 0.1215497594327678, 0.3907815631262525, 0.0200133422281521, 0.01384721901684745, 0.27610008628127697, 0.026166593981683382, 0.049993056519927796, 0.02844950213371266, 0.13178537809852525, 0.07033997655334115, 0.020134228187919462, 0.015113350125944586, 0.11904761904761905, 0.030864197530864196, 0.07081038552321008, 0.045011252813203305, 0.2282947077135939, 0.20221473278767452, 0.03420752565564424, 0.013882461823229986, 0.014238253440911248, 0.011031439602868174, 0.04356243949661181, 0.03519061583577712, 0.03977461054027179, 0.015404364569961491, 0.008031053406505154, 0.020219039595619208, 0.03305785123966942, 0.012719949120203519, 0.3161397670549085, 0.07223693715386467, 0.35928143712574845, 0.011419870574800152, 0.01580194890703187, 0.3022162525184688, 0.14731673097158893, 0.047443331576172906, 0.2873359347286272, 0.1353892440767206, 0.011516314779270634, 0.018981335020563112, 0.011100832562442185, 0.01720676799541153, 0.0213903743315508, 0.4388185654008439, 0.2442126685321801, 0.07666098807495741, 0.08866995073891627, 0.03320420586607637, 0.17716099505425556, 0.0644352962233757, 0.013256738842244807, 0.37524687294272546, 0.087890625, 0.10027855153203342, 0.427594070695553, 0.034236804564907276, 0.013599274705349048, 0.019808517662594914, 0.11435832274459977, 0.01015228426395939], "y0": " ", "yaxis": "y"}], "layout": {"legend": {"tracegroupgap": 0}, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "title": {"text": "Distribution of Label #3 and Blink Rate"}, "violinmode": "group", "xaxis": {"anchor": "y", "domain": [0, 1], "title": {"text": "label #3"}}, "yaxis": {"anchor": "x", "domain": [0, 1], "title": {"text": "blink_rate"}}}}}, "metadata": {}, "output_type": "display_data"}], "source": ["#show distribution based on label 3 and eye blinking rate\n", "import plotly.express as px\n", "import pandas\n", "import numpy\n", "import plotly.graph_objects as go\n", "\n", "# Load the dataset\n", "filtered_df = combined_df[(combined_df['Duration (s)'] > 20)].copy()\n", "df_labels = filtered_df['label #3']\n", "df_blink_rate = filtered_df['blink_rate']\n", "\n", "# Create a DataFrame for visualization\n", "df_plot = pandas.DataFrame({\n", "    'label #3': df_labels,\n", "    'blink_rate': df_blink_rate\n", "})\n", "\n", "# Plot the distribution in violin plot\n", "fig = px.violin(df_plot, y='blink_rate', x='label #3', box=True, points=\"all\", title='Distribution of Label #3 and Blink Rate')\n", "fig.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#relation between motor taask"]}], "metadata": {"kernelspec": {"display_name": "assessment", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.5"}}, "nbformat": 4, "nbformat_minor": 2}