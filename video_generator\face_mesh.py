import cv2
import mediapipe as mp
import numpy as np

# Initialize Mediapipe Face Mesh
mp_face_mesh = mp.solutions.face_mesh
face_mesh = mp_face_mesh.FaceMesh(static_image_mode=False, max_num_faces=1)
mp_drawing = mp.solutions.drawing_utils

# Dummy emotion classifier (for demonstration purposes)
def classify_emotion(landmarks):
    # This is just a placeholder. You would normally use a trained model for emotion classification.
    # Here we simply return random emotions with confidence scores.
    emotions = ["Happy", "Sad", "Angry", "Surprised", "Neutral"]
    confidence_scores = {emotion: np.random.uniform(60, 100) for emotion in emotions}  # Random confidence between 60% and 100%
    return confidence_scores

# Open the video file or webcam
cap = cv2.VideoCapture(r"C:\Users\<USER>\Videos\20241030_123809.mp4")  # Provide video file path

# Define the codec and create VideoWriter object to save the output
fourcc = cv2.VideoWriter_fourcc(*'mp4v')
out = cv2.VideoWriter(r"C:\Users\<USER>\Videos\angry_output_video.mp4", fourcc, 20.0, (int(cap.get(3)), int(cap.get(4))))

while cap.isOpened():
    ret, frame = cap.read()
    if not ret:
        break

    # Convert the frame to RGB (for Mediapipe)
    rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

    # Perform face mesh detection
    results = face_mesh.process(rgb_frame)

    if results.multi_face_landmarks:
        for face_landmarks in results.multi_face_landmarks:
            # Draw the face mesh on the frame
            mp_drawing.draw_landmarks(
                image=frame,
                landmark_list=face_landmarks,
                connections=mp_face_mesh.FACEMESH_TESSELATION,
                landmark_drawing_spec=mp_drawing.DrawingSpec(color=(0, 255, 0), thickness=1, circle_radius=1),
                connection_drawing_spec=mp_drawing.DrawingSpec(color=(0, 255, 0), thickness=1, circle_radius=1)
            )

            # Get bounding box coordinates around the face
            h, w, _ = frame.shape
            x_min = min([int(landmark.x * w) for landmark in face_landmarks.landmark])
            y_min = min([int(landmark.y * h) for landmark in face_landmarks.landmark])
            x_max = max([int(landmark.x * w) for landmark in face_landmarks.landmark])
            y_max = max([int(landmark.y * h) for landmark in face_landmarks.landmark])

            # Draw a white frame around the face
            cv2.rectangle(frame, (x_min, y_min), (x_max, y_max), (255, 255, 255), 2)

            # Classify the emotions
            #emotions_confidence = classify_emotion(face_landmarks.landmark)

            # Write all emotions and their confidence values in the top left corner
            y_offset = 20
            # for emotion, confidence in emotions_confidence.items():
            #     cv2.putText(frame, f"{emotion}: {confidence:.2f}%", (10, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 2)
            #     y_offset += 20

    # Write the frame to the output video
    out.write(frame)

    # Display the frame
    cv2.imshow('Face Mesh with Emotion', frame)

    # Break the loop with 'q'
    if cv2.waitKey(1) & 0xFF == ord('q'):
        break

# Release resources
cap.release()
out.release()
cv2.destroyAllWindows()
