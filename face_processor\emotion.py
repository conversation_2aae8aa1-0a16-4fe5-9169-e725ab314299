import cv2
from deepface import DeepFace
import pandas as pd
import os

class ImageFrameEmotionExtractor:
    def __init__(self, frames_path,output_path):
        self.frames_path = frames_path
        self.emotions_df = None
        self.output_csv = output_path

    def extract_emotions(self):
        """Extracts emotions from image frames, storing results in a DataFrame."""
        frame_results = []
        frame_count = 0

        # List all files in the directory containing the frames
        frames = [f for f in os.listdir(self.frames_path) if f.endswith(('.png', '.jpg', '.jpeg'))]
        frames.sort()  # Optional: sort the frame files if needed

        for frame_file in frames:
            frame_path = os.path.join(self.frames_path, frame_file)
            frame = cv2.imread(frame_path)

            if frame is not None:
                # Convert the frame to RGB as DeepFace expects an RGB image
                rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

                try:
                    # Analyze the frame with DeepFace
                    analysis = DeepFace.analyze(rgb_frame, actions=['emotion'], enforce_detection=False)
                    emotions = analysis[0]['emotion']

                    # Append the results with frame number
                    frame_results.append({
                        'frame': frame_count,
                        **emotions
                    })
                except Exception as e:
                    print(f"Error processing frame {frame_count}: {e}")

                frame_count += 1
            else:
                print(f"Failed to read frame {frame_count}")

        self.emotions_df = pd.DataFrame(frame_results)
        self.emotions_df.to_csv(self.output_csv, index=False)

    def get_emotion_statistics(self):
        """Returns emotions and their intensities for each frame."""
        if self.emotions_df is None:
            raise ValueError("Emotion data is not loaded. Call extract_emotions first.")
        return self.emotions_df

