body {
    font-family: 'Inter', sans-serif;
    background-color: #f3f8fb;
}

/* Header with the main title */
.header {
    background-color: #34a2bb;
    color: white;
    padding: 20px;
    text-align: center;
}

.header h1 {
    margin: 0;
    font-size: 2.5rem;
    font-weight: 600;
}

/* Footer section */
.footer {
    background-color: #68b9cc;
    color: white;
    padding: 15px;
    text-align: center;
    bottom: 0;
    width: 100%;
}

.footer p {
    margin: 0;
    font-size: 1rem;
}

/* Report container and cards */
.report-container {
    padding: 40px;
    margin: auto;
    max-width: 900px;
}

.card {
    background-color: #ffffff;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 30px;
    margin-bottom: 30px;
}

.card-title {
    display: flex;
    align-items: center;
    font-weight: 600;
    font-size: 1.5rem;
    margin-bottom: 20px;
    color: #000;
}

/* Additional CSS for card title */
.card-title::before {
    content: "";
    position: absolute;
    left: 0px;
    top: 0;
    width: 5px;
    height: 100%;
    background-color: #68b9cc;
}

p {
    line-height: 1.6;
    text-align: justify;
    color: #6c757d;
}

/* Table styling */
.table {
    margin-bottom: 30px;
    color: #212529;
    text-align: center;
}

.table th {
    background-color: #68b9cc;
    color: #ffffff;
    text-align: center;
    border: 1px solid #ffffff;
}

.table td {
    vertical-align: middle;
    border: 1px solid #e9ecef;
}

.table tbody td:first-child {
    background-color: #fafafa;
}

.text-center img {
    max-width: 150px;
}

.results-summary img {
    max-width: 80%;
    margin-top: 10px;
}

.finger-distance-chart img {
    max-width: 90%;
    margin-bottom: 20px;
}

/* Gallery styles */
.gallery-container {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    margin-top: 20px;
}

.gallery-item {
    flex: 1 1 calc(50% - 10px);
    margin-bottom: 20px;
}

.gallery-item img {
    width: 80%;
    border-radius: 10px;
}

.img-fluid {
    max-width: 390px;
    height: auto;
}

.patient-only, .doctor-only {
    display: none;
}

/* Show content for the correct role */
.is-patient .patient-only,
.is-doctor .doctor-only {
    display: block;
}

.is-admin .admin-only,
.is-admin .patient-only,
.is-admin .doctor-only {
    display: block;
}

/* Print styles */
@media print {
    body {
        width: 210mm;
        height: 297mm;
        margin: 0;
    }

    .container {
        page-break-inside: avoid;
        margin-top: 0;
        margin-bottom: 0;
    }

    h1, h2, h3, h4, h5, h6 {
        page-break-after: avoid;
    }
}
