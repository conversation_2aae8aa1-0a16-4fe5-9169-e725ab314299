import os
import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestClassifier
import sys
sys.path.insert(0, 'd:\\Atefeh\\Codes\\Python\\ParkinsonAssessment')
from weak_supervision.LFBase import LFBaseClass
from datasets.load_data import load_labels_with_ID, load_emotion_task_ts_data, get_train_classifiers_set, merge_df_with_labels
from helpers.classification_analysis import perform_analysis_cv, save_trained_model


class LF9Class(LFBaseClass):
    def __init__(self, model_path = '.\\models\\best_lf9_v4.json'):
        
        self.type='text'
        super().__init__(model_path=model_path)
        
    # Define the function to calculate minimal brow movement
    def calculate_brow_movement(self, df,au):
        # Calculate the movement score by summing up the standard deviations of the AUs
        brow_movement_score = df[au].std()
        return brow_movement_score

    def prepare_dataset(self):
        # Create an empty list to store the results
        results = []
        
        # Iterate over tasks and emotions
        for task in self.tasks:
            for emotion in self.emotions:
                root_folder = f'{self.origin_folder}\\facial_expression\\{emotion}\\{task}\\action_units'
                
                # Iterate over all CSV files in the folder
                for file_name in os.listdir(root_folder):
                    if file_name.endswith('.csv'):
                        for au in [' AU01_r', ' AU02_r', ' AU04_r']:                            
                            # Read the data from the CSV file
                            file_path = os.path.join(root_folder, file_name)
                            data = pd.read_csv(file_path)
                            
                            # Extract ID from file_name (assuming ID is part of the file_name)
                            # Modify this extraction logic based on your actual file naming convention
                            id_ = file_name.split('_')[0]
                            

                            # Step 1: Calculate std of brows in each video using action units
                            brow_movement_score = self.calculate_brow_movement(data,au)
                            
                            # Step 2: Calculate number of frames in that video
                            num_frames = len(data)
                            
                            # Step 3: Calculate average brow movement score per frame
                            avg_brow_movement_score = brow_movement_score / num_frames
                            
                            
                            # Store the results
                            results.append({
                                'ID': id_,
                                'emotion_task': f'{emotion}_{task}_{au}',
                                'brow_movement': avg_brow_movement_score
                            })

        # Create a DataFrame from the results
        brow_data_df = pd.DataFrame(results)

        # Pivot the DataFrame to get columns for each task_emotion combination
        brow_data_pivot_df = brow_data_df.pivot(index='ID', columns='emotion_task', values='brow_movement').reset_index()
        brow_data_pivot_df['ID'] = brow_data_pivot_df['ID'].astype(int)
        brow_data_pivot_df.set_index('ID', inplace=True)


        # Step 4: Merge the pivoted DataFrame with booth_updrs_df on the 'ID' column
        booth_updrs_df = load_labels_with_ID(os.path.join(self.origin_folder, 'docs'), 'booth_updrs.csv')
        brow_data_df.fillna(0, inplace=True)
        #booth_updrs_df.dropna(subset=[self.predict], inplace=True)
        merged_df = merge_df_with_labels(brow_data_pivot_df, booth_updrs_df, self.predict)
        return merged_df

    def train_classifier(self):
        return super().train_classifier('text|imitation')

        
        
# lf = LF9Class()
# classifier, selected_features =  lf.train_classifier()
# lf.save_model(classifier, selected_features)
# lf.evaluate_classifier(classifier, selected_features)